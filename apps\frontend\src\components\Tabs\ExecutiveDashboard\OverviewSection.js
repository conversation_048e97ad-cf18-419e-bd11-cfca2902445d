import React from 'react';
import KPICards from '../Dashboard/KPICards';
import RecentActivity from '../Dashboard/RecentActivity';

const OverviewSection = ({ dashboardData, onViewChange }) => {
  console.log('🔍 OverviewSection received dashboardData:', dashboardData);

  const kpis = dashboardData?.kpis || {};
  const recentActivity = dashboardData?.recentActivity || [];
  const insights = dashboardData?.insights || [];

  return (
    <div className="overview-section">
      {/* KPI Cards */}
      <div className="section-header">
        <h3 className="section-title">Key Performance Indicators</h3>
        <p className="section-subtitle">Real-time portfolio metrics and performance</p>
      </div>
      
      <KPICards kpis={kpis} />

      {/* Insights Section */}
      {insights.length > 0 && (
        <div className="insights-section">
          <h3 className="section-title">Strategic Insights</h3>
          <div className="insights-grid">
            {insights.map((insight, index) => (
              <div key={index} className="insight-card">
                <div className="insight-icon">
                  <i className="fas fa-lightbulb"></i>
                </div>
                <p className="insight-text">{insight}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Quick Navigation */}
      <div className="quick-navigation">
        <h3 className="section-title">Quick Navigation</h3>
        <div className="nav-cards">
          <button 
            className="nav-card"
            onClick={() => onViewChange('portfolio')}
          >
            <div className="nav-icon">
              <i className="fas fa-chart-pie"></i>
            </div>
            <div className="nav-content">
              <h4>Portfolio Analysis</h4>
              <p>View Master Business Cases and Programs</p>
            </div>
            <i className="fas fa-arrow-right nav-arrow"></i>
          </button>

          <button 
            className="nav-card"
            onClick={() => onViewChange('financial')}
          >
            <div className="nav-icon">
              <i className="fas fa-chart-line"></i>
            </div>
            <div className="nav-content">
              <h4>Financial Performance</h4>
              <p>Analyze financial metrics and trends</p>
            </div>
            <i className="fas fa-arrow-right nav-arrow"></i>
          </button>

          <button 
            className="nav-card"
            onClick={() => onViewChange('risk')}
          >
            <div className="nav-icon">
              <i className="fas fa-shield-alt"></i>
            </div>
            <div className="nav-content">
              <h4>Risk & Compliance</h4>
              <p>Monitor risk levels and compliance status</p>
            </div>
            <i className="fas fa-arrow-right nav-arrow"></i>
          </button>
        </div>
      </div>

      {/* Two Column Layout for Activity and Actions */}
      <div className="bottom-section">
        <div className="activity-column">
          <h3 className="section-title">Recent Activity</h3>
          <RecentActivity activities={recentActivity} />
        </div>
        
        <div className="actions-column">
          <h3 className="section-title">Quick Actions</h3>
          <div className="quick-actions">
            <button className="action-btn primary">
              <i className="fas fa-plus-circle"></i>
              Create Business Case
            </button>
            <button className="action-btn secondary">
              <i className="fas fa-sitemap"></i>
              Master Business Case
            </button>
            <button className="action-btn tertiary">
              <i className="fas fa-upload"></i>
              Bulk Import
            </button>
          </div>
        </div>
      </div>

      <style jsx>{`
        .overview-section {
          padding: 24px;
        }

        .section-header {
          margin-bottom: 24px;
        }

        .section-title {
          font-size: 20px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 8px 0;
        }

        .section-subtitle {
          color: #64748b;
          margin: 0 0 24px 0;
        }

        .insights-section {
          margin: 32px 0;
        }

        .insights-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 16px;
        }

        .insight-card {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          padding: 16px;
          background: #f8fafc;
          border-radius: 8px;
          border-left: 4px solid #3b82f6;
        }

        .insight-icon {
          color: #3b82f6;
          font-size: 18px;
          margin-top: 2px;
        }

        .insight-text {
          color: #334155;
          margin: 0;
          line-height: 1.5;
        }

        .quick-navigation {
          margin: 32px 0;
        }

        .nav-cards {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 16px;
        }

        .nav-card {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 20px;
          background: white;
          border: 2px solid #e2e8f0;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.2s;
          text-align: left;
        }

        .nav-card:hover {
          border-color: #3b82f6;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .nav-icon {
          width: 48px;
          height: 48px;
          background: #3b82f6;
          color: white;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
          flex-shrink: 0;
        }

        .nav-content {
          flex: 1;
        }

        .nav-content h4 {
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 4px 0;
        }

        .nav-content p {
          color: #64748b;
          margin: 0;
          font-size: 14px;
        }

        .nav-arrow {
          color: #94a3b8;
          font-size: 16px;
        }

        .bottom-section {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 24px;
          margin-top: 32px;
        }

        .activity-column,
        .actions-column {
          background: #f8fafc;
          padding: 20px;
          border-radius: 12px;
        }

        .quick-actions {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .action-btn {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 16px 20px;
          border: none;
          border-radius: 8px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          text-align: left;
        }

        .action-btn.primary {
          background: #3b82f6;
          color: white;
        }

        .action-btn.primary:hover {
          background: #2563eb;
          transform: translateY(-1px);
        }

        .action-btn.secondary {
          background: #10b981;
          color: white;
        }

        .action-btn.secondary:hover {
          background: #059669;
          transform: translateY(-1px);
        }

        .action-btn.tertiary {
          background: #f59e0b;
          color: white;
        }

        .action-btn.tertiary:hover {
          background: #d97706;
          transform: translateY(-1px);
        }

        @media (max-width: 768px) {
          .overview-section {
            padding: 16px;
          }

          .bottom-section {
            grid-template-columns: 1fr;
          }

          .nav-cards {
            grid-template-columns: 1fr;
          }

          .insights-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
};

export default OverviewSection;
