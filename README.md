# MasterBC - Master Business Case Management System

**Author: <PERSON><PERSON><PERSON>**  
**Repository: https://github.com/mahegyaneshpandey/spm**  
**Date: January 27, 2025**

## 🎯 **Overview**

MasterBC is a comprehensive business case management system designed for strategic planning and project portfolio management. It provides tools for creating, managing, and analyzing business cases with integrated financial modeling, project tracking, and stakeholder collaboration.

## ✨ **Key Features**

### 📊 **Business Case Management**
- Complete CRUD operations for business cases
- Financial modeling with IRR, NPV, Payback Period calculations
- Master Business Case aggregation and portfolio view
- Excel import/export functionality with financial formulas

### 🎯 **Project & Program Management**
- Projects and Epics management with milestone tracking
- Program-level organization and oversight
- Business case linking for strategic alignment
- Comprehensive relationship management

### 💡 **Idea Management**
- Idea creation and evaluation workflow
- Promotion from ideas to business cases
- Stakeholder feedback and collaboration
- Priority and status tracking

### 📈 **Strategic Dashboard**
- Real-time KPI monitoring and analytics
- Portfolio-level financial metrics
- Relationship visualization
- Business unit performance tracking

## 🏗️ **Architecture**

### **Frontend (React)**
- Modern React application with hooks and context
- Responsive design with Tailwind CSS
- Component-based architecture
- Real-time data synchronization

### **Backend (Node.js)**
- RESTful API with Express.js
- File-based data storage (MongoDB simulation)
- Comprehensive validation and error handling
- Modular service architecture

### **Data Management**
- Hybrid storage approach (JSON files + MongoDB ready)
- Data integrity validation
- Audit logging and change tracking
- Backup and recovery mechanisms

## 🚀 **Quick Start Guide**

### **Prerequisites**
Before launching the application, ensure you have:

- **Node.js** (version 16.0 or higher)
  - Download from: https://nodejs.org/
  - Verify: `node --version`
- **npm** (comes with Node.js)
  - Verify: `npm --version`
- **Web Browser** (Chrome, Firefox, Safari, or Edge)

### **Step-by-Step Launch Instructions**

#### **Step 1: Get the Application**
```bash
# Navigate to your project directory
cd spm
```

#### **Step 2: Navigate to Frontend Directory**
```bash
# Go to the React frontend application
cd apps/frontend
```

#### **Step 3: Install Dependencies (First Time Only)**
```bash
# Install all required packages (takes 2-3 minutes)
npm install

# You should see "added X packages" when complete
```

#### **Step 4: Launch the Frontend Application**
```bash
# Start the React development server
npm start

# Wait for "Compiled successfully!" message
# Browser will automatically open to http://localhost:3000
```

#### **Step 5: Access the Application**
- **Frontend URL**: http://localhost:3000
- **Auto-open**: Browser should open automatically
- **Manual**: If not, copy the URL to your browser

### **🔧 Backend Setup (Optional)**

The application works with JSON file storage by default. For full API functionality:

#### **Backend Launch Commands**
```bash
# In a new terminal window
cd spm/apps/backend

# Install backend dependencies
npm install

# Start the backend server
npm start
# Backend runs on: http://localhost:5000
```

### **🎉 Success Indicators**
You'll know it's working when you see:
- ✅ "Compiled successfully!" in terminal
- ✅ Browser opens to the application
- ✅ Master Business Case Management interface loads
- ✅ Navigation tabs are visible (Ideas, Business Cases, etc.)

### **⚡ Quick Commands**
```bash
# Start application
npm start

# Stop application (in terminal)
Ctrl+C (Windows/Linux) or Cmd+C (Mac)

# Restart application
Ctrl+C, then npm start again
```

## 📁 **Project Structure**

```
spm/
├── apps/
│   ├── frontend/          # React application (Main application)
│   │   ├── src/           # React source code
│   │   ├── public/        # Static assets
│   │   ├── package.json   # Frontend dependencies
│   │   └── README.md      # Frontend documentation
│   └── backend/           # Node.js API server (Optional)
│       ├── src/           # TypeScript source code
│       ├── data/          # JSON data files
│       ├── package.json   # Backend dependencies
│       └── server.js      # Main server file
├── packages/              # Shared packages
├── docs/                  # Documentation
├── scripts/               # Build and deployment scripts
├── tests/                 # Integration and E2E tests
├── tools/                 # Development utilities
├── package.json           # Root package configuration
└── README.md              # This file
```

### **📂 Key Directories**

- **`apps/frontend/`** - Main React application (Port 3000)
- **`apps/backend/`** - Optional API server (Port 5000)
- **`apps/frontend/src/data/`** - JSON data storage for development
- **`apps/backend/data/`** - Backend data files

## 🛠️ **Troubleshooting**

### **Common Issues & Solutions**

#### **1. Port Already in Use**
```bash
# Error: "Something is already running on port 3000"
# Solution: Kill the process or use different port
npx kill-port 3000
# Or start on different port
npm start -- --port 3001
```

#### **2. Module Not Found Errors**
```bash
# Error: "Module not found" or dependency issues
# Solution: Clean install
rm -rf node_modules package-lock.json
npm install
```

#### **3. Node.js Version Issues**
```bash
# Check your Node.js version
node --version

# If version is below 16.0, update Node.js
# Download latest from: https://nodejs.org/
```

#### **4. Application Won't Load**
- **Clear browser cache**: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)
- **Try incognito/private mode**
- **Check terminal for error messages**
- **Restart the development server**: Ctrl+C, then `npm start`

#### **5. Slow Performance**
- **Close other applications** to free up memory
- **Use Chrome DevTools** (F12) to check for errors
- **Restart your computer** if issues persist

### **Getting Help**
1. **Check the terminal** for error messages
2. **Open browser DevTools** (F12) and check Console tab
3. **Restart the application**: Stop (Ctrl+C) and start again (`npm start`)
4. **Verify prerequisites**: Ensure Node.js 16+ is installed

## 🎯 **Application Features & Usage**

### **Main Application Tabs**

#### **1. 💡 Ideas Tab**
- Create and manage business ideas
- Track idea status and priority
- Promote approved ideas to business cases
- Stakeholder feedback collection

#### **2. 📊 Business Cases Tab**
- Complete CRUD operations for business cases
- Financial modeling (CAPEX, OPEX, NPV, IRR, Payback Period)
- Excel export with financial formulas
- Link to projects and programs

#### **3. 🎯 Master Business Cases Tab**
- Portfolio-level business case aggregation
- Automatic financial metrics calculation
- Link multiple business cases to master cases
- Strategic portfolio overview

#### **4. 📈 Projects & Epics Tab**
- Project and epic management
- Milestone tracking and timeline visualization
- Link to business cases for strategic alignment
- Resource allocation and progress monitoring

#### **5. 🏗️ Programs Tab**
- Program-level management and oversight
- Link programs to master business cases
- Cross-project coordination
- Strategic initiative tracking

#### **6. 📋 Strategic Portfolio Tab**
- Executive dashboard with KPIs
- Portfolio performance analytics
- Risk assessment and profitability analysis
- Success rate and ROI metrics

#### **7. 🔧 Business Architecture Tab**
- Financial formulas documentation
- Calculation methodology reference
- Best practices and usage guidelines
- Downloadable documentation

### **Key Capabilities**
- **Real-time Calculations**: Automatic NPV, IRR, and risk scoring
- **Data Export**: Excel files with embedded formulas
- **Relationship Management**: Link entities across the portfolio
- **Sample Data**: Pre-loaded examples for immediate testing
- **Responsive Design**: Works on desktop, tablet, and mobile

## 🔐 **Authentication**

### **Demo Credentials**
- **Admin**: admin / admin
- **Test User**: test / test
- **Gyanesh**: gyanesh / gyanesh123

*Note: Authentication is optional for development mode*

## 📚 **Documentation**

- [API Documentation](./docs/api/README.md)
- [Architecture Guide](./docs/architecture/README.md)
- [User Guide](./docs/user-guide/README.md)
- [Development Guide](./docs/development/README.md)

## 🧪 **Testing**

```bash
# Run all tests
npm test

# Run integration tests
npm run test:integration

# Run E2E tests
npm run test:e2e
```

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 **License**

This project is part of the SPM (Strategic Portfolio Management) suite.

## 👨‍💻 **Author**

**Gyanesh K Pandey**  
- GitHub: [@mahegyaneshpandey](https://github.com/mahegyaneshpandey)
- Repository: [SPM](https://github.com/mahegyaneshpandey/spm)

---

*Built with ❤️ for strategic business management and portfolio optimization.*
![alt text](image.png)