import React from 'react';
import './RelationshipStats.css';

const RelationshipStats = ({ data }) => {
  if (!data || !data.nodes || !data.edges) {
    return null;
  }

  // Calculate entity type counts
  const entityCounts = data.nodes.reduce((counts, node) => {
    counts[node.type] = (counts[node.type] || 0) + 1;
    return counts;
  }, {});

  // Calculate connection type counts
  const connectionCounts = data.edges.reduce((counts, edge) => {
    counts[edge.type] = (counts[edge.type] || 0) + 1;
    return counts;
  }, {});

  // Calculate connectivity metrics
  const connectedNodes = new Set();
  data.edges.forEach(edge => {
    connectedNodes.add(edge.source);
    connectedNodes.add(edge.target);
  });

  const connectivityRate = data.nodes.length > 0 
    ? ((connectedNodes.size / data.nodes.length) * 100).toFixed(1)
    : 0;

  // Find most connected entities
  const nodeConnections = {};
  data.edges.forEach(edge => {
    nodeConnections[edge.source] = (nodeConnections[edge.source] || 0) + 1;
    nodeConnections[edge.target] = (nodeConnections[edge.target] || 0) + 1;
  });

  const mostConnectedNode = Object.entries(nodeConnections)
    .sort(([,a], [,b]) => b - a)[0];

  const mostConnectedEntity = mostConnectedNode 
    ? data.nodes.find(node => node.id === mostConnectedNode[0])
    : null;

  const getEntityTypeIcon = (type) => {
    const icons = {
      idea: 'fas fa-lightbulb',
      businessCase: 'fas fa-briefcase',
      project: 'fas fa-project-diagram',
      program: 'fas fa-layer-group',
      masterBusinessCase: 'fas fa-crown'
    };
    return icons[type] || 'fas fa-circle';
  };

  const getEntityTypeLabel = (type) => {
    const labels = {
      idea: 'Ideas',
      businessCase: 'Business Cases',
      project: 'Projects',
      program: 'Programs',
      masterBusinessCase: 'Master BCs'
    };
    return labels[type] || type;
  };

  const getConnectionTypeLabel = (type) => {
    const labels = {
      'idea-to-bc': 'Ideas → Business Cases',
      'bc-to-project': 'BCs → Projects',
      'project-to-program': 'Projects → Programs',
      'bc-to-mbc': 'BCs → Master BCs',
      'program-to-mbc': 'Programs → Master BCs'
    };
    return labels[type] || type;
  };

  return (
    <div className="relationship-stats">
      {/* Overview Stats */}
      <div className="stats-overview">
        <div className="stat-card primary">
          <div className="stat-icon">
            <i className="fas fa-network-wired"></i>
          </div>
          <div className="stat-content">
            <div className="stat-value">{data.nodes.length}</div>
            <div className="stat-label">Total Entities</div>
          </div>
        </div>

        <div className="stat-card secondary">
          <div className="stat-icon">
            <i className="fas fa-link"></i>
          </div>
          <div className="stat-content">
            <div className="stat-value">{data.edges.length}</div>
            <div className="stat-label">Total Connections</div>
          </div>
        </div>

        <div className="stat-card success">
          <div className="stat-icon">
            <i className="fas fa-percentage"></i>
          </div>
          <div className="stat-content">
            <div className="stat-value">{connectivityRate}%</div>
            <div className="stat-label">Connectivity Rate</div>
          </div>
        </div>

        <div className="stat-card warning">
          <div className="stat-icon">
            <i className="fas fa-unlink"></i>
          </div>
          <div className="stat-content">
            <div className="stat-value">{data.nodes.length - connectedNodes.size}</div>
            <div className="stat-label">Orphaned Entities</div>
          </div>
        </div>
      </div>

      {/* Detailed Breakdown */}
      <div className="stats-breakdown">
        {/* Entity Types */}
        <div className="breakdown-section">
          <h4>Entity Distribution</h4>
          <div className="breakdown-grid">
            {Object.entries(entityCounts).map(([type, count]) => (
              <div key={type} className="breakdown-item">
                <div className="breakdown-icon">
                  <i className={getEntityTypeIcon(type)}></i>
                </div>
                <div className="breakdown-content">
                  <div className="breakdown-value">{count}</div>
                  <div className="breakdown-label">{getEntityTypeLabel(type)}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Connection Types */}
        <div className="breakdown-section">
          <h4>Connection Types</h4>
          <div className="connection-list">
            {Object.entries(connectionCounts).map(([type, count]) => (
              <div key={type} className="connection-item">
                <div className="connection-label">{getConnectionTypeLabel(type)}</div>
                <div className="connection-count">{count}</div>
                <div className="connection-bar">
                  <div 
                    className="connection-fill"
                    style={{ 
                      width: `${(count / Math.max(...Object.values(connectionCounts))) * 100}%` 
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Most Connected Entity */}
        {mostConnectedEntity && (
          <div className="breakdown-section">
            <h4>Most Connected Entity</h4>
            <div className="most-connected">
              <div className="entity-info">
                <div className="entity-icon">
                  <i className={getEntityTypeIcon(mostConnectedEntity.type)}></i>
                </div>
                <div className="entity-details">
                  <div className="entity-name">{mostConnectedEntity.label}</div>
                  <div className="entity-type">{getEntityTypeLabel(mostConnectedEntity.type)}</div>
                </div>
              </div>
              <div className="connection-count-badge">
                {mostConnectedNode[1]} connections
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Health Indicators */}
      <div className="health-indicators">
        <div className="health-item">
          <div className="health-icon success">
            <i className="fas fa-check-circle"></i>
          </div>
          <div className="health-content">
            <div className="health-label">Well Connected</div>
            <div className="health-description">
              {connectedNodes.size} entities have relationships
            </div>
          </div>
        </div>

        {data.nodes.length - connectedNodes.size > 0 && (
          <div className="health-item">
            <div className="health-icon warning">
              <i className="fas fa-exclamation-triangle"></i>
            </div>
            <div className="health-content">
              <div className="health-label">Needs Attention</div>
              <div className="health-description">
                {data.nodes.length - connectedNodes.size} orphaned entities need linking
              </div>
            </div>
          </div>
        )}

        <div className="health-item">
          <div className="health-icon info">
            <i className="fas fa-info-circle"></i>
          </div>
          <div className="health-content">
            <div className="health-label">Relationship Density</div>
            <div className="health-description">
              {data.edges.length > 0 
                ? `${(data.edges.length / (data.nodes.length * (data.nodes.length - 1) / 2) * 100).toFixed(2)}% of possible connections`
                : 'No connections established'
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RelationshipStats;
