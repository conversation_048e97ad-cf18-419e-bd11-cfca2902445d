import React from 'react';
import PropTypes from 'prop-types';

const ChartsSection = ({ charts = {} }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Investment by Business Unit Chart */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          <i className="fas fa-chart-pie mr-2 text-blue-600"></i>
          Investment by Business Unit
        </h3>
        <div className="chart-container">
          <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
            <div className="text-center">
              <i className="fas fa-chart-pie text-4xl text-gray-400 mb-4"></i>
              <p className="text-gray-600">Chart will be rendered here</p>
              <p className="text-sm text-gray-500">Using Chart.js or similar library</p>
            </div>
          </div>
        </div>
      </div>

      {/* ROI Trends Chart */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          <i className="fas fa-chart-line mr-2 text-green-600"></i>
          ROI Trends
        </h3>
        <div className="chart-container">
          <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
            <div className="text-center">
              <i className="fas fa-chart-line text-4xl text-gray-400 mb-4"></i>
              <p className="text-gray-600">Chart will be rendered here</p>
              <p className="text-sm text-gray-500">Time series data visualization</p>
            </div>
          </div>
        </div>
      </div>

      {/* Project Status Distribution */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          <i className="fas fa-chart-bar mr-2 text-purple-600"></i>
          Project Status Distribution
        </h3>
        <div className="chart-container">
          <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
            <div className="text-center">
              <i className="fas fa-chart-bar text-4xl text-gray-400 mb-4"></i>
              <p className="text-gray-600">Chart will be rendered here</p>
              <p className="text-sm text-gray-500">Status breakdown visualization</p>
            </div>
          </div>
        </div>
      </div>

      {/* Financial Metrics Comparison */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          <i className="fas fa-chart-area mr-2 text-orange-600"></i>
          Financial Metrics Comparison
        </h3>
        <div className="chart-container">
          <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
            <div className="text-center">
              <i className="fas fa-chart-area text-4xl text-gray-400 mb-4"></i>
              <p className="text-gray-600">Chart will be rendered here</p>
              <p className="text-sm text-gray-500">IRR, NPV, Payback comparison</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

ChartsSection.propTypes = {
  charts: PropTypes.object
};

export default ChartsSection;
