@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS for Business Case Management */

/* Tab Styles */
.tab-content { 
  display: none; 
}

.tab-content.active { 
  display: block; 
}

.tab-button.active { 
  background-color: #3b82f6; 
  color: white; 
}

/* Tour Styles */
.tour-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9998;
  display: none;
}

.tour-highlight {
  position: relative;
  z-index: 9999;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 9999px rgba(0, 0, 0, 0.7);
  border-radius: 8px;
}

.tour-tooltip {
  position: absolute;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 350px;
  z-index: 10000;
  border: 2px solid #3b82f6;
}

.tour-tooltip::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

.tour-tooltip.top::before {
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 10px 10px 0 10px;
  border-color: #3b82f6 transparent transparent transparent;
}

.tour-tooltip.bottom::before {
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 10px 10px 10px;
  border-color: transparent transparent #3b82f6 transparent;
}

.tour-tooltip.left::before {
  right: -10px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 10px 0 10px 10px;
  border-color: transparent transparent transparent #3b82f6;
}

.tour-tooltip.right::before {
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 10px 10px 10px 0;
  border-color: transparent #3b82f6 transparent transparent;
}

/* Star Rating */
.star-rating {
  transition: color 0.2s ease;
  cursor: pointer;
}

.star-rating:hover {
  transform: scale(1.1);
}

/* Excel Drop Zone */
.excel-drop-zone {
  transition: all 0.3s ease;
}

.excel-drop-zone.dragover {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

/* Feedback Modal */
.feedback-modal {
  backdrop-filter: blur(4px);
}

/* Enhanced tour overlay styles */
.tour-overlay-enhanced {
  backdrop-filter: blur(2px);
}

/* Excel Preview Table */
.excel-preview-table {
  max-height: 300px;
  overflow-y: auto;
}

/* Bulk Operation Cards */
.bulk-operation-card {
  transition: all 0.2s ease;
}

.bulk-operation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Notification Styles */
.notification {
  animation: slideInRight 0.3s ease;
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Loading Spinner */
.loading-spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form Validation */
.form-error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-success {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Chart Container */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* Modal Backdrop */
.modal-backdrop {
  backdrop-filter: blur(4px);
  background-color: rgba(0, 0, 0, 0.5);
}

/* Responsive Table */
.responsive-table {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-active {
  background-color: #dcfce7;
  color: #166534;
}

.status-draft {
  background-color: #fef3c7;
  color: #92400e;
}

.status-completed {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-archived {
  background-color: #f3f4f6;
  color: #374151;
}
