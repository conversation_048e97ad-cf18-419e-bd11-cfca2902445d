import React, { useMemo, useEffect, useState } from 'react';
import { dashboardService } from '../../../services/dashboardService';

const BusinessUnitPortfolio = ({ masterBusinessCases, programs, projects, businessCases }) => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);

  // Load dashboard data with business unit information
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        const response = await dashboardService.getDashboardData();
        if (response.success) {
          setDashboardData(response.data);
          console.log('🔍 BusinessUnitPortfolio loaded dashboard data:', response.data.businessUnits);
        }
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  console.log('🔍 BusinessUnitPortfolio received data:', {
    masterBusinessCases: masterBusinessCases?.length,
    programs: programs?.length,
    projects: projects?.length,
    businessCases: businessCases?.length,
    dashboardData: !!dashboardData
  });

  // Calculate Business Unit portfolio metrics using dashboard data
  const businessUnitMetrics = useMemo(() => {
    if (!dashboardData || !dashboardData.businessUnits) {
      return {
        businessUnits: {},
        totalPortfolioValue: 0,
        mostProfitableBU: null,
        leastProfitableBU: null
      };
    }

    // Use the business unit data from dashboard API
    const businessUnits = {};
    const dashboardBUs = dashboardData.businessUnits.details || [];

    dashboardBUs.forEach(bu => {
      businessUnits[bu.name] = {
        name: bu.name,
        totalInvestment: bu.totalInvestment,
        totalNPV: bu.totalNPV,
        totalCapex: bu.totalInvestment * 0.6, // Estimate based on typical ratio
        totalOpex: bu.totalInvestment * 0.4,
        avgIRR: bu.avgIRR,
        irrCount: bu.irrCount,
        masterBCCount: bu.businessCaseCount,
        projectCount: 0, // Will be calculated below
        programCount: 0,
        profitMargin: bu.totalNPV > 0 ? (bu.totalNPV / bu.totalInvestment) * 100 : 0,
        roi: bu.totalNPV > 0 ? (bu.totalNPV / bu.totalInvestment) * 100 : 0,
        masterBCs: [],
        programs: [],
        projects: [],
        status: 'active'
      };
    });

    let totalPortfolioValue = dashboardData.businessUnits.summary?.totalPortfolioValue || 0;

    // Add project and program counts from the passed data
    if (programs && Array.isArray(programs)) {
      programs.forEach(program => {
        if (program.businessUnit && businessUnits[program.businessUnit]) {
          businessUnits[program.businessUnit].programCount++;
          businessUnits[program.businessUnit].programs.push({
            name: program.name,
            budget: program.budget || 0,
            status: program.status
          });
        }
      });
    }

    if (projects && Array.isArray(projects)) {
      projects.forEach(project => {
        if (project.businessUnit && businessUnits[project.businessUnit]) {
          businessUnits[project.businessUnit].projectCount++;
          businessUnits[project.businessUnit].projects.push({
            name: project.name,
            type: project.type,
            status: project.status
          });
        }
      });
    }

    // No additional processing needed - data comes from dashboard API

    // Find most and least profitable business units
    const buArray = Object.values(businessUnits);
    const mostProfitableBU = buArray.reduce((max, bu) => bu.totalNPV > max.totalNPV ? bu : max, buArray[0] || null);
    const leastProfitableBU = buArray.reduce((min, bu) => bu.totalNPV < min.totalNPV ? bu : min, buArray[0] || null);

    return {
      businessUnits,
      totalPortfolioValue,
      mostProfitableBU,
      leastProfitableBU
    };
  }, [dashboardData, programs, projects]);

  // Show loading state
  if (loading) {
    return (
      <div className="business-unit-portfolio">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading business unit portfolio data...</p>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${Number(value).toFixed(1)}%`;
  };

  const getProfitabilityColor = (npv) => {
    if (npv > 500000) return '#10b981'; // Green for high profit
    if (npv > 100000) return '#f59e0b'; // Orange for medium profit
    if (npv > 0) return '#3b82f6'; // Blue for low profit
    return '#ef4444'; // Red for loss
  };

  const getProfitabilityStatus = (npv) => {
    if (npv > 500000) return 'High Profit';
    if (npv > 100000) return 'Medium Profit';
    if (npv > 0) return 'Low Profit';
    return 'Loss';
  };

  const businessUnitArray = Object.values(businessUnitMetrics.businessUnits)
    .sort((a, b) => b.totalNPV - a.totalNPV);

  return (
    <div className="business-unit-portfolio">
      {/* Portfolio Summary Header */}
      <div className="portfolio-header">
        <h3 className="section-title">Business Unit Portfolio Summary</h3>
        <div className="portfolio-summary-cards">
          <div className="summary-card total-value">
            <h4>Total Portfolio Value</h4>
            <p className="summary-value">{formatCurrency(businessUnitMetrics.totalPortfolioValue)}</p>
            <span className="summary-detail">Across all Business Units</span>
          </div>

          {businessUnitMetrics.mostProfitableBU && (
            <div className="summary-card most-profitable">
              <h4>Most Profitable BU</h4>
              <p className="summary-value">{businessUnitMetrics.mostProfitableBU.name}</p>
              <span className="summary-detail">{formatCurrency(businessUnitMetrics.mostProfitableBU.totalNPV)} NPV</span>
            </div>
          )}

          <div className="summary-card bu-count">
            <h4>Active Business Units</h4>
            <p className="summary-value">{businessUnitArray.length}</p>
            <span className="summary-detail">With portfolio investments</span>
          </div>
        </div>
      </div>

      {/* Business Unit Performance Matrix */}
      <div className="bu-performance-section">
        <h3 className="section-title">Business Unit Performance Matrix</h3>
        <div className="performance-matrix">
          {businessUnitArray.map((bu, index) => (
            <div key={bu.name} className="bu-performance-card">
              <div className="bu-header">
                <div className="bu-rank">#{index + 1}</div>
                <div className="bu-info">
                  <h4>{bu.name}</h4>
                  <span className={`profit-status ${getProfitabilityStatus(bu.totalNPV).toLowerCase().replace(' ', '-')}`}>
                    {getProfitabilityStatus(bu.totalNPV)}
                  </span>
                </div>
                <div 
                  className="profit-indicator"
                  style={{ backgroundColor: getProfitabilityColor(bu.totalNPV) }}
                >
                  <i className={`fas ${bu.totalNPV > 0 ? 'fa-arrow-up' : 'fa-arrow-down'}`}></i>
                </div>
              </div>

              <div className="bu-metrics">
                <div className="metric-row">
                  <span className="metric-label">Total NPV:</span>
                  <span className="metric-value npv">{formatCurrency(bu.totalNPV)}</span>
                </div>
                <div className="metric-row">
                  <span className="metric-label">Investment:</span>
                  <span className="metric-value">{formatCurrency(bu.totalInvestment)}</span>
                </div>
                <div className="metric-row">
                  <span className="metric-label">ROI:</span>
                  <span className="metric-value roi">{formatPercentage(bu.roi)}</span>
                </div>
                <div className="metric-row">
                  <span className="metric-label">Avg IRR:</span>
                  <span className="metric-value">{formatPercentage(bu.avgIRR)}</span>
                </div>
                <div className="metric-row">
                  <span className="metric-label">Master BCs:</span>
                  <span className="metric-value">{bu.masterBCCount}</span>
                </div>
                <div className="metric-row">
                  <span className="metric-label">Programs:</span>
                  <span className="metric-value">{bu.programCount}</span>
                </div>
                <div className="metric-row">
                  <span className="metric-label">Projects/Epics:</span>
                  <span className="metric-value">{bu.projectCount}</span>
                </div>
              </div>

              <div className="bu-breakdown">
                <div className="breakdown-header">
                  <h5>CAPEX vs OPEX</h5>
                </div>
                <div className="breakdown-bars">
                  <div className="breakdown-bar">
                    <span className="bar-label">CAPEX</span>
                    <div className="bar-container">
                      <div 
                        className="bar-fill capex"
                        style={{ 
                          width: `${(bu.totalCapex / (bu.totalCapex + bu.totalOpex)) * 100}%` 
                        }}
                      ></div>
                    </div>
                    <span className="bar-value">{formatCurrency(bu.totalCapex)}</span>
                  </div>
                  <div className="breakdown-bar">
                    <span className="bar-label">OPEX</span>
                    <div className="bar-container">
                      <div 
                        className="bar-fill opex"
                        style={{ 
                          width: `${(bu.totalOpex / (bu.totalCapex + bu.totalOpex)) * 100}%` 
                        }}
                      ></div>
                    </div>
                    <span className="bar-value">{formatCurrency(bu.totalOpex)}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Top Performing Business Units */}
      <div className="top-performers-section">
        <h3 className="section-title">Top 5 Performing Business Units</h3>
        <div className="top-performers-list">
          {businessUnitArray.slice(0, 5).map((bu, index) => (
            <div key={bu.name} className="top-performer-item">
              <div className="performer-rank">
                <span className="rank-number">#{index + 1}</span>
                <div className="rank-medal">
                  <i className={`fas ${index === 0 ? 'fa-crown' : index === 1 ? 'fa-medal' : 'fa-star'}`}></i>
                </div>
              </div>
              <div className="performer-content">
                <h4>{bu.name}</h4>
                <div className="performer-metrics">
                  <span className="metric">NPV: {formatCurrency(bu.totalNPV)}</span>
                  <span className="metric">ROI: {formatPercentage(bu.roi)}</span>
                  <span className="metric">Master BCs: {bu.masterBCCount}</span>
                  <span className="metric">Programs: {bu.programCount}</span>
                  <span className="metric">Projects: {bu.projectCount}</span>
                </div>
              </div>
              <div className="performer-chart">
                <div className="mini-chart">
                  <div 
                    className="chart-bar"
                    style={{ 
                      height: `${Math.min((bu.totalNPV / businessUnitArray[0].totalNPV) * 100, 100)}%`,
                      backgroundColor: getProfitabilityColor(bu.totalNPV)
                    }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <style jsx>{`
        .business-unit-portfolio {
          padding: 24px;
        }

        .section-title {
          font-size: 20px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 20px 0;
        }

        .portfolio-header {
          margin-bottom: 32px;
        }

        .portfolio-summary-cards {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;
          margin-top: 16px;
        }

        .summary-card {
          padding: 20px;
          background: white;
          border-radius: 12px;
          border: 1px solid #e2e8f0;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .summary-card.total-value {
          border-left: 4px solid #3b82f6;
        }

        .summary-card.most-profitable {
          border-left: 4px solid #10b981;
        }

        .summary-card.bu-count {
          border-left: 4px solid #f59e0b;
        }

        .summary-card h4 {
          font-size: 14px;
          font-weight: 500;
          color: #64748b;
          margin: 0 0 12px 0;
        }

        .summary-value {
          font-size: 24px;
          font-weight: 700;
          color: #1e293b;
          margin: 0 0 4px 0;
        }

        .summary-detail {
          font-size: 12px;
          color: #94a3b8;
        }

        .bu-performance-section,
        .top-performers-section {
          margin-bottom: 32px;
        }

        .performance-matrix {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: 20px;
        }

        .bu-performance-card {
          background: white;
          border-radius: 12px;
          border: 1px solid #e2e8f0;
          padding: 20px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .bu-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 16px;
        }

        .bu-rank {
          width: 32px;
          height: 32px;
          background: #3b82f6;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 14px;
        }

        .bu-info {
          flex: 1;
        }

        .bu-info h4 {
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 4px 0;
        }

        .profit-status {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 12px;
          font-weight: 500;
        }

        .profit-status.high-profit {
          background: #dcfce7;
          color: #166534;
        }

        .profit-status.medium-profit {
          background: #fef3c7;
          color: #92400e;
        }

        .profit-status.low-profit {
          background: #dbeafe;
          color: #1e40af;
        }

        .profit-status.loss {
          background: #fee2e2;
          color: #dc2626;
        }

        .profit-indicator {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 14px;
        }

        .bu-metrics {
          margin-bottom: 16px;
        }

        .metric-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 4px 0;
        }

        .metric-label {
          font-size: 14px;
          color: #64748b;
        }

        .metric-value {
          font-size: 14px;
          font-weight: 600;
          color: #1e293b;
        }

        .metric-value.npv {
          color: #10b981;
        }

        .metric-value.roi {
          color: #3b82f6;
        }

        .bu-breakdown {
          border-top: 1px solid #e2e8f0;
          padding-top: 16px;
        }

        .breakdown-header h5 {
          font-size: 14px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 12px 0;
        }

        .breakdown-bar {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
        }

        .bar-label {
          font-size: 12px;
          color: #64748b;
          width: 50px;
        }

        .bar-container {
          flex: 1;
          height: 6px;
          background: #e2e8f0;
          border-radius: 3px;
          overflow: hidden;
        }

        .bar-fill.capex {
          background: #3b82f6;
          height: 100%;
        }

        .bar-fill.opex {
          background: #f59e0b;
          height: 100%;
        }

        .bar-value {
          font-size: 12px;
          color: #64748b;
          width: 80px;
          text-align: right;
        }

        .top-performers-list {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .top-performer-item {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 16px;
          background: white;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
        }

        .performer-rank {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .rank-number {
          font-size: 18px;
          font-weight: 700;
          color: #1e293b;
        }

        .rank-medal {
          color: #f59e0b;
          font-size: 16px;
        }

        .performer-content {
          flex: 1;
        }

        .performer-content h4 {
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 8px 0;
        }

        .performer-metrics {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;
        }

        .performer-metrics .metric {
          font-size: 12px;
          color: #64748b;
          background: #f1f5f9;
          padding: 4px 8px;
          border-radius: 4px;
        }

        .performer-chart {
          width: 60px;
          height: 40px;
        }

        .mini-chart {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: end;
          justify-content: center;
        }

        .chart-bar {
          width: 20px;
          border-radius: 2px 2px 0 0;
          transition: height 0.3s ease;
        }

        @media (max-width: 768px) {
          .business-unit-portfolio {
            padding: 16px;
          }

          .portfolio-summary-cards {
            grid-template-columns: 1fr;
          }

          .performance-matrix {
            grid-template-columns: 1fr;
          }

          .top-performer-item {
            flex-direction: column;
            text-align: center;
          }

          .performer-metrics {
            justify-content: center;
          }
        }
      `}</style>
    </div>
  );
};

export default BusinessUnitPortfolio;
