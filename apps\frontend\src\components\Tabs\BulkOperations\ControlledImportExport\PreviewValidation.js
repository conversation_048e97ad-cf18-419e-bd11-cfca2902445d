import React, { useState } from 'react';

const PreviewValidation = ({ previewData, validationResults, operation }) => {
  const [activeTab, setActiveTab] = useState('preview');

  const renderImportPreview = () => (
    <div className="import-preview">
      <div className="preview-stats">
        <div className="stat-card">
          <i className="fas fa-file-alt text-blue-500"></i>
          <div>
            <span className="stat-number">{previewData.totalRows}</span>
            <span className="stat-label">Total Rows</span>
          </div>
        </div>
        <div className="stat-card">
          <i className="fas fa-check-circle text-green-500"></i>
          <div>
            <span className="stat-number">{previewData.validRows}</span>
            <span className="stat-label">Valid Rows</span>
          </div>
        </div>
        <div className="stat-card">
          <i className="fas fa-exclamation-triangle text-red-500"></i>
          <div>
            <span className="stat-number">{previewData.invalidRows}</span>
            <span className="stat-label">Invalid Rows</span>
          </div>
        </div>
      </div>

      <div className="data-preview">
        <h6>Data Sample (First 5 Rows)</h6>
        <div className="preview-table-container">
          <table className="preview-table">
            <thead>
              <tr>
                {previewData.columns.map(column => (
                  <th key={column}>{column}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {previewData.sampleData.map((row, index) => (
                <tr key={index}>
                  {previewData.columns.map(column => (
                    <td key={column}>{row[column]}</td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderExportPreview = () => (
    <div className="export-preview">
      <div className="preview-stats">
        <div className="stat-card">
          <i className="fas fa-database text-blue-500"></i>
          <div>
            <span className="stat-number">{previewData.totalRecords}</span>
            <span className="stat-label">Total Records</span>
          </div>
        </div>
        <div className="stat-card">
          <i className="fas fa-check-square text-green-500"></i>
          <div>
            <span className="stat-number">{previewData.selectedRecords}</span>
            <span className="stat-label">Selected Records</span>
          </div>
        </div>
        <div className="stat-card">
          <i className="fas fa-file-download text-purple-500"></i>
          <div>
            <span className="stat-number">{previewData.estimatedFileSize}</span>
            <span className="stat-label">Estimated Size</span>
          </div>
        </div>
      </div>

      <div className="export-details">
        <h6>Export Details</h6>
        <div className="detail-grid">
          <div className="detail-item">
            <span className="detail-label">Data Types:</span>
            <span className="detail-value">{previewData.dataTypes.join(', ')}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">Records to Export:</span>
            <span className="detail-value">{previewData.selectedRecords} of {previewData.totalRecords}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">Estimated File Size:</span>
            <span className="detail-value">{previewData.estimatedFileSize}</span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderValidationResults = () => {
    if (!validationResults) {
      return (
        <div className="no-validation">
          <i className="fas fa-info-circle text-blue-500"></i>
          <p>No validation issues found.</p>
        </div>
      );
    }

    const { errors = [], warnings = [] } = validationResults;

    return (
      <div className="validation-results">
        {errors.length > 0 && (
          <div className="validation-section">
            <div className="section-header error">
              <i className="fas fa-times-circle"></i>
              <h6>Errors ({errors.length})</h6>
            </div>
            <div className="validation-list">
              {errors.map((error, index) => (
                <div key={index} className="validation-item error">
                  <div className="validation-icon">
                    <i className="fas fa-times-circle"></i>
                  </div>
                  <div className="validation-content">
                    <span className="validation-location">Row {error.row}, Column: {error.column}</span>
                    <span className="validation-message">{error.message}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {warnings.length > 0 && (
          <div className="validation-section">
            <div className="section-header warning">
              <i className="fas fa-exclamation-triangle"></i>
              <h6>Warnings ({warnings.length})</h6>
            </div>
            <div className="validation-list">
              {warnings.map((warning, index) => (
                <div key={index} className="validation-item warning">
                  <div className="validation-icon">
                    <i className="fas fa-exclamation-triangle"></i>
                  </div>
                  <div className="validation-content">
                    <span className="validation-location">Row {warning.row}, Column: {warning.column}</span>
                    <span className="validation-message">{warning.message}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {errors.length === 0 && warnings.length === 0 && (
          <div className="validation-success">
            <i className="fas fa-check-circle text-green-500"></i>
            <h6>Validation Passed</h6>
            <p>All data passed validation checks. Ready to proceed with {operation}.</p>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="preview-validation">
      {/* Tab Navigation */}
      <div className="preview-tabs">
        <button
          className={`tab-btn ${activeTab === 'preview' ? 'active' : ''}`}
          onClick={() => setActiveTab('preview')}
        >
          <i className="fas fa-eye"></i>
          Data Preview
        </button>
        <button
          className={`tab-btn ${activeTab === 'validation' ? 'active' : ''}`}
          onClick={() => setActiveTab('validation')}
        >
          <i className="fas fa-check-circle"></i>
          Validation Results
          {validationResults && (validationResults.errors?.length > 0 || validationResults.warnings?.length > 0) && (
            <span className="validation-badge">
              {(validationResults.errors?.length || 0) + (validationResults.warnings?.length || 0)}
            </span>
          )}
        </button>
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {activeTab === 'preview' && (
          <div className="preview-content">
            {operation === 'import' ? renderImportPreview() : renderExportPreview()}
          </div>
        )}

        {activeTab === 'validation' && (
          <div className="validation-content">
            {renderValidationResults()}
          </div>
        )}
      </div>

      {/* Action Recommendations */}
      <div className="action-recommendations">
        <h6>Recommendations</h6>
        <div className="recommendations-list">
          {operation === 'import' && validationResults?.errors?.length > 0 && (
            <div className="recommendation error">
              <i className="fas fa-exclamation-circle"></i>
              <span>Fix all errors before proceeding with import. Data with errors will be skipped.</span>
            </div>
          )}
          
          {operation === 'import' && validationResults?.warnings?.length > 0 && (
            <div className="recommendation warning">
              <i className="fas fa-exclamation-triangle"></i>
              <span>Review warnings. Import can proceed but data quality may be affected.</span>
            </div>
          )}
          
          {operation === 'import' && (!validationResults?.errors?.length) && (
            <div className="recommendation success">
              <i className="fas fa-check-circle"></i>
              <span>Data validation passed. Safe to proceed with import.</span>
            </div>
          )}
          
          {operation === 'export' && (
            <div className="recommendation info">
              <i className="fas fa-info-circle"></i>
              <span>Export will include {previewData.selectedRecords} records. Estimated file size: {previewData.estimatedFileSize}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PreviewValidation;
