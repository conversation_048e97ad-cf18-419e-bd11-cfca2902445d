import React, { useState, useEffect } from 'react';
import './LinkingModal.css';

const LinkingModal = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  masterBC, 
  linkingType, 
  businessCases = [], 
  programs = [] 
}) => {
  const [selectedItems, setSelectedItems] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (isOpen && masterBC) {
      // Pre-select already linked items
      if (linkingType === 'business-cases') {
        setSelectedItems(masterBC.linkedBCs || []);
      } else if (linkingType === 'programs') {
        // For programs, find which programs are linked to this Master BC
        const linkedPrograms = programs.filter(p => p.linkedMasterBC === masterBC.id);
        setSelectedItems(linkedPrograms.map(p => p.id));
      }
    }
  }, [isOpen, masterBC, linkingType, programs]);

  if (!isOpen) return null;

  const getAvailableItems = () => {
    if (linkingType === 'business-cases') {
      return businessCases.filter(bc => 
        bc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        bc.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    } else if (linkingType === 'programs') {
      return programs.filter(p => 
        p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        p.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    return [];
  };

  const handleItemToggle = (itemId) => {
    if (linkingType === 'programs') {
      // For programs, only allow one selection (one-to-one relationship)
      setSelectedItems([itemId]);
    } else {
      // For business cases, allow multiple selections
      setSelectedItems(prev => 
        prev.includes(itemId) 
          ? prev.filter(id => id !== itemId)
          : [...prev, itemId]
      );
    }
  };

  const handleConfirm = () => {
    onConfirm(selectedItems);
  };

  const availableItems = getAvailableItems();
  const title = linkingType === 'business-cases' 
    ? 'Link Business Cases' 
    : 'Link Program';

  return (
    <div className="modal-overlay">
      <div className="linking-modal">
        <div className="modal-header">
          <h3>{title}</h3>
          <button className="close-btn" onClick={onClose}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        <div className="modal-body">
          <div className="master-bc-info">
            <h4>Master Business Case: {masterBC?.name}</h4>
            <p>{masterBC?.description}</p>
          </div>

          <div className="search-section">
            <input
              type="text"
              placeholder={`Search ${linkingType === 'business-cases' ? 'business cases' : 'programs'}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>

          <div className="items-section">
            <h5>
              Available {linkingType === 'business-cases' ? 'Business Cases' : 'Programs'} 
              ({availableItems.length})
            </h5>
            
            {linkingType === 'programs' && (
              <div className="info-note">
                <i className="fas fa-info-circle"></i>
                Note: Each Master BC can only be linked to one Program
              </div>
            )}

            <div className="items-list">
              {availableItems.map(item => (
                <div key={item.id} className="item-row">
                  <label className="item-checkbox">
                    <input
                      type={linkingType === 'programs' ? 'radio' : 'checkbox'}
                      name={linkingType === 'programs' ? 'program-selection' : undefined}
                      checked={selectedItems.includes(item.id)}
                      onChange={() => handleItemToggle(item.id)}
                    />
                    <span className="checkmark"></span>
                  </label>
                  
                  <div className="item-info">
                    <div className="item-name">{item.name}</div>
                    <div className="item-description">{item.description}</div>
                    {linkingType === 'business-cases' && item.calculatedMetrics && (
                      <div className="item-metrics">
                        NPV: ${item.calculatedMetrics.npv?.toLocaleString() || 0} | 
                        IRR: {item.calculatedMetrics.irr || 0}%
                      </div>
                    )}
                    {linkingType === 'programs' && item.linkedMasterBC && item.linkedMasterBC !== masterBC?.id && (
                      <div className="item-warning">
                        <i className="fas fa-exclamation-triangle"></i>
                        Already linked to another Master BC
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {availableItems.length === 0 && (
              <div className="empty-state">
                <i className="fas fa-search"></i>
                <p>No {linkingType === 'business-cases' ? 'business cases' : 'programs'} found</p>
              </div>
            )}
          </div>

          <div className="selection-summary">
            <strong>Selected: {selectedItems.length}</strong>
            {linkingType === 'business-cases' && selectedItems.length > 0 && (
              <span> business case{selectedItems.length > 1 ? 's' : ''}</span>
            )}
            {linkingType === 'programs' && selectedItems.length > 0 && (
              <span> program</span>
            )}
          </div>
        </div>

        <div className="modal-footer">
          <button className="btn btn-secondary" onClick={onClose}>
            Cancel
          </button>
          <button 
            className="btn btn-primary" 
            onClick={handleConfirm}
            disabled={selectedItems.length === 0}
          >
            {linkingType === 'business-cases' ? 'Link Business Cases' : 'Link Program'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default LinkingModal;
