import React, { useState } from 'react';

const JiraIntegration = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [syncEnabled, setSyncEnabled] = useState(true);
  const [mappingConfig, setMappingConfig] = useState({
    businessUnit: 'project',
    project: 'epic',
    epic: 'story'
  });

  const tabs = [
    { id: 'overview', name: 'Overview', icon: 'fas fa-chart-pie' },
    { id: 'import', name: 'Import from JIRA', icon: 'fas fa-download' },
    { id: 'export', name: 'Export to JIRA', icon: 'fas fa-upload' },
    { id: 'mapping', name: 'Field Mapping', icon: 'fas fa-exchange-alt' },
    { id: 'sync', name: 'Bi-directional Sync', icon: 'fas fa-sync-alt' }
  ];

  const renderOverview = () => (
    <div className="jira-overview">
      <div className="connection-status">
        <div className="status-header">
          <i className="fab fa-jira text-blue-500"></i>
          <div>
            <h5>JIRA Connection Status</h5>
            <p>Connected to: company.atlassian.net</p>
          </div>
          <div className="status-indicator connected">
            <i className="fas fa-check-circle"></i>
            <span>Connected</span>
          </div>
        </div>
      </div>

      <div className="sync-summary">
        <h6>Sync Summary</h6>
        <div className="summary-grid">
          <div className="summary-item">
            <span className="summary-label">Last Sync:</span>
            <span className="summary-value">2 hours ago</span>
          </div>
          <div className="summary-item">
            <span className="summary-label">Epics Synced:</span>
            <span className="summary-value">15</span>
          </div>
          <div className="summary-item">
            <span className="summary-label">Status Updates:</span>
            <span className="summary-value">8</span>
          </div>
          <div className="summary-item">
            <span className="summary-label">Sync Frequency:</span>
            <span className="summary-value">Every 4 hours</span>
          </div>
        </div>
      </div>

      <div className="data-flow">
        <h6>Data Flow Configuration</h6>
        <div className="flow-diagram">
          <div className="flow-item">
            <div className="flow-box internal">
              <i className="fas fa-briefcase"></i>
              <span>Business Cases</span>
            </div>
            <div className="flow-arrow">
              <i className="fas fa-arrow-right"></i>
            </div>
            <div className="flow-box external">
              <i className="fab fa-jira"></i>
              <span>JIRA Projects</span>
            </div>
          </div>
          
          <div className="flow-item">
            <div className="flow-box internal">
              <i className="fas fa-project-diagram"></i>
              <span>Projects/Epics</span>
            </div>
            <div className="flow-arrow bidirectional">
              <i className="fas fa-exchange-alt"></i>
            </div>
            <div className="flow-box external">
              <i className="fab fa-jira"></i>
              <span>JIRA Epics</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderImport = () => (
    <div className="jira-import">
      <div className="import-header">
        <h6>Import Data from JIRA</h6>
        <p>Select JIRA data to import into the system</p>
      </div>

      <div className="import-options">
        <div className="option-card">
          <div className="option-header">
            <i className="fas fa-layer-group text-blue-500"></i>
            <h7>Epic Status Updates</h7>
          </div>
          <p>Import latest epic status and progress from JIRA</p>
          <div className="option-actions">
            <button className="btn btn-primary">
              <i className="fas fa-download"></i>
              Import Epic Updates
            </button>
          </div>
        </div>

        <div className="option-card">
          <div className="option-header">
            <i className="fas fa-tasks text-green-500"></i>
            <h7>Project Progress</h7>
          </div>
          <p>Import project completion percentages and milestone updates</p>
          <div className="option-actions">
            <button className="btn btn-primary">
              <i className="fas fa-download"></i>
              Import Progress Data
            </button>
          </div>
        </div>

        <div className="option-card">
          <div className="option-header">
            <i className="fas fa-users text-purple-500"></i>
            <h7>Resource Assignments</h7>
          </div>
          <p>Import team member assignments and workload data</p>
          <div className="option-actions">
            <button className="btn btn-primary">
              <i className="fas fa-download"></i>
              Import Assignments
            </button>
          </div>
        </div>
      </div>

      <div className="import-history">
        <h6>Recent Imports</h6>
        <div className="history-list">
          <div className="history-item">
            <div className="history-icon">
              <i className="fas fa-download text-blue-500"></i>
            </div>
            <div className="history-content">
              <span className="history-title">Epic Status Updates</span>
              <span className="history-description">15 epics updated</span>
              <span className="history-time">2 hours ago</span>
            </div>
            <div className="history-status success">
              <i className="fas fa-check-circle"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderExport = () => (
    <div className="jira-export">
      <div className="export-header">
        <h6>Export Data to JIRA</h6>
        <p>Send internal data to JIRA for project management</p>
      </div>

      <div className="export-options">
        <div className="option-card">
          <div className="option-header">
            <i className="fas fa-building text-blue-500"></i>
            <h7>Business Unit Information</h7>
          </div>
          <p>Export business unit details to JIRA project structure</p>
          <div className="option-actions">
            <button className="btn btn-primary">
              <i className="fas fa-upload"></i>
              Export BU Data
            </button>
          </div>
        </div>

        <div className="option-card">
          <div className="option-header">
            <i className="fas fa-project-diagram text-green-500"></i>
            <h7>Project Information</h7>
          </div>
          <p>Export project details and milestones to JIRA</p>
          <div className="option-actions">
            <button className="btn btn-primary">
              <i className="fas fa-upload"></i>
              Export Projects
            </button>
          </div>
        </div>

        <div className="option-card">
          <div className="option-header">
            <i className="fas fa-layer-group text-purple-500"></i>
            <h7>Epic Information</h7>
          </div>
          <p>Export epic details and requirements to JIRA</p>
          <div className="option-actions">
            <button className="btn btn-primary">
              <i className="fas fa-upload"></i>
              Export Epics
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderMapping = () => (
    <div className="jira-mapping">
      <div className="mapping-header">
        <h6>Field Mapping Configuration</h6>
        <p>Configure how internal fields map to JIRA fields</p>
      </div>

      <div className="mapping-config">
        <div className="mapping-section">
          <h7>Entity Mapping</h7>
          <div className="mapping-grid">
            <div className="mapping-row">
              <span className="internal-field">Business Unit</span>
              <i className="fas fa-arrow-right"></i>
              <select 
                value={mappingConfig.businessUnit}
                onChange={(e) => setMappingConfig(prev => ({...prev, businessUnit: e.target.value}))}
              >
                <option value="project">JIRA Project</option>
                <option value="component">JIRA Component</option>
                <option value="label">JIRA Label</option>
              </select>
            </div>
            
            <div className="mapping-row">
              <span className="internal-field">Project</span>
              <i className="fas fa-arrow-right"></i>
              <select 
                value={mappingConfig.project}
                onChange={(e) => setMappingConfig(prev => ({...prev, project: e.target.value}))}
              >
                <option value="epic">JIRA Epic</option>
                <option value="initiative">JIRA Initiative</option>
                <option value="project">JIRA Project</option>
              </select>
            </div>
            
            <div className="mapping-row">
              <span className="internal-field">Epic</span>
              <i className="fas fa-arrow-right"></i>
              <select 
                value={mappingConfig.epic}
                onChange={(e) => setMappingConfig(prev => ({...prev, epic: e.target.value}))}
              >
                <option value="story">JIRA Story</option>
                <option value="epic">JIRA Epic</option>
                <option value="task">JIRA Task</option>
              </select>
            </div>
          </div>
        </div>

        <div className="mapping-actions">
          <button className="btn btn-outline">
            <i className="fas fa-undo"></i>
            Reset to Default
          </button>
          <button className="btn btn-primary">
            <i className="fas fa-save"></i>
            Save Mapping
          </button>
        </div>
      </div>
    </div>
  );

  const renderSync = () => (
    <div className="jira-sync">
      <div className="sync-header">
        <h6>Bi-directional Sync Configuration</h6>
        <p>Configure automatic synchronization between systems</p>
      </div>

      <div className="sync-toggle">
        <div className="toggle-header">
          <h7>Enable Bi-directional Sync</h7>
          <label className="toggle-switch">
            <input 
              type="checkbox" 
              checked={syncEnabled}
              onChange={(e) => setSyncEnabled(e.target.checked)}
            />
            <span className="toggle-slider"></span>
          </label>
        </div>
        <p>Automatically sync data changes between internal system and JIRA</p>
      </div>

      {syncEnabled && (
        <div className="sync-configuration">
          <div className="sync-options">
            <div className="sync-option">
              <label>
                <input type="checkbox" defaultChecked />
                <span>Sync epic status changes from JIRA</span>
              </label>
            </div>
            <div className="sync-option">
              <label>
                <input type="checkbox" defaultChecked />
                <span>Sync progress updates from JIRA</span>
              </label>
            </div>
            <div className="sync-option">
              <label>
                <input type="checkbox" />
                <span>Push business case updates to JIRA</span>
              </label>
            </div>
            <div className="sync-option">
              <label>
                <input type="checkbox" />
                <span>Push milestone changes to JIRA</span>
              </label>
            </div>
          </div>

          <div className="sync-frequency">
            <h7>Sync Frequency</h7>
            <select defaultValue="4">
              <option value="1">Every hour</option>
              <option value="4">Every 4 hours</option>
              <option value="12">Every 12 hours</option>
              <option value="24">Daily</option>
            </select>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className="jira-integration">
      {/* Tab Navigation */}
      <div className="integration-tabs">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <i className={tab.icon}></i>
            <span>{tab.name}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'import' && renderImport()}
        {activeTab === 'export' && renderExport()}
        {activeTab === 'mapping' && renderMapping()}
        {activeTab === 'sync' && renderSync()}
      </div>
    </div>
  );
};

export default JiraIntegration;
