import axios from 'axios';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    
    if (error.response?.status >= 500) {
      console.error('Server error:', error.response.data);
    }
    
    return Promise.reject(error);
  }
);

// API helper functions
export const apiRequest = async (method, url, data = null, config = {}) => {
  try {
    const response = await api({
      method,
      url,
      data,
      ...config,
    });
    
    return {
      success: true,
      data: response.data.data || response.data,
      message: response.data.message,
      status: response.status,
    };
  } catch (error) {
    console.error(`API ${method.toUpperCase()} ${url} failed:`, error);
    
    const errorMessage = error.response?.data?.error || 
                        error.response?.data?.message || 
                        error.message || 
                        'An unexpected error occurred';
    
    return {
      success: false,
      error: errorMessage,
      status: error.response?.status || 500,
    };
  }
};

// Specific API methods
export const apiGet = (url, config = {}) => apiRequest('get', url, null, config);
export const apiPost = (url, data, config = {}) => {
  console.log('🌍 apiPost called:', { url, data, config });
  const result = apiRequest('post', url, data, config);
  console.log('🌍 apiPost result:', result);
  return result;
};
export const apiPut = (url, data, config = {}) => apiRequest('put', url, data, config);
export const apiPatch = (url, data, config = {}) => apiRequest('patch', url, data, config);
export const apiDelete = (url, config = {}) => apiRequest('delete', url, null, config);

// File upload helper
export const uploadFile = async (url, file, onProgress = null) => {
  const formData = new FormData();
  formData.append('file', file);
  
  const config = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  };
  
  if (onProgress) {
    config.onUploadProgress = (progressEvent) => {
      const percentCompleted = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      onProgress(percentCompleted);
    };
  }
  
  return apiRequest('post', url, formData, config);
};

// Download helper
export const downloadFile = async (url, filename = null) => {
  try {
    const response = await api({
      method: 'get',
      url,
      responseType: 'blob',
    });
    
    // Create blob link to download
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    
    // Set filename from response headers or use provided filename
    const contentDisposition = response.headers['content-disposition'];
    if (contentDisposition && !filename) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }
    
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(downloadUrl);
    
    return { success: true };
  } catch (error) {
    console.error('Download failed:', error);
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Download failed',
    };
  }
};

// Health check
export const healthCheck = () => apiGet('/health');

// Export the axios instance for direct use if needed
export default api;
