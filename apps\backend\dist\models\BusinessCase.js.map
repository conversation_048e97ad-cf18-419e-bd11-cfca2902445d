{"version": 3, "file": "BusinessCase.js", "sourceRoot": "", "sources": ["../../src/models/BusinessCase.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAAsD;AA4CtD,MAAM,kBAAkB,GAAG,IAAI,iBAAM,CAAgB;IACnD,IAAI,EAAE;QACJ,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,CAAC,IAAI,EAAE,gCAAgC,CAAC;QAClD,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,CAAC,GAAG,EAAE,mCAAmC,CAAC;KACtD;IACD,WAAW,EAAE;QACX,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,CAAC,IAAI,EAAE,2CAA2C,CAAC;KAC/D;IACD,IAAI,EAAE,CAAC;YACL,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,IAAI;SACX,CAAC;IACF,YAAY,EAAE;QACZ,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,CAAC,GAAG,EAAE,4CAA4C,CAAC;KAC/D;IACD,SAAS,EAAE;QACT,SAAS,EAAE;YACT,IAAI,EAAE,MAAM;YACZ,QAAQ,EAAE,CAAC,IAAI,EAAE,wBAAwB,CAAC;YAC1C,GAAG,EAAE,CAAC,IAAI,EAAE,kCAAkC,CAAC;YAC/C,GAAG,EAAE,CAAC,IAAI,EAAE,+BAA+B,CAAC;SAC7C;QACD,OAAO,EAAE;YACP,IAAI,EAAE,MAAM;YACZ,QAAQ,EAAE,CAAC,IAAI,EAAE,sBAAsB,CAAC;YACxC,GAAG,EAAE,CAAC,IAAI,EAAE,gCAAgC,CAAC;YAC7C,GAAG,EAAE,CAAC,IAAI,EAAE,6BAA6B,CAAC;SAC3C;KACF;IACD,aAAa,EAAE;QACb,KAAK,EAAE,CAAC;gBACN,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,QAAQ,EAAE,IAAI;iBACf;gBACD,MAAM,EAAE;oBACN,IAAI,EAAE,MAAM;oBACZ,QAAQ,EAAE,IAAI;oBACd,GAAG,EAAE,CAAC,CAAC,EAAE,iCAAiC,CAAC;iBAC5C;gBACD,WAAW,EAAE;oBACX,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,IAAI;iBACX;aACF,CAAC;QACF,IAAI,EAAE,CAAC;gBACL,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,QAAQ,EAAE,IAAI;iBACf;gBACD,MAAM,EAAE;oBACN,IAAI,EAAE,MAAM;oBACZ,QAAQ,EAAE,IAAI;oBACd,GAAG,EAAE,CAAC,CAAC,EAAE,gCAAgC,CAAC;iBAC3C;gBACD,WAAW,EAAE;oBACX,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,IAAI;iBACX;aACF,CAAC;QACF,OAAO,EAAE,CAAC;gBACR,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,QAAQ,EAAE,IAAI;iBACf;gBACD,MAAM,EAAE;oBACN,IAAI,EAAE,MAAM;oBACZ,QAAQ,EAAE,IAAI;oBACd,GAAG,EAAE,CAAC,CAAC,EAAE,mCAAmC,CAAC;iBAC9C;gBACD,WAAW,EAAE;oBACX,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,IAAI;iBACX;aACF,CAAC;QACF,UAAU,EAAE;YACV,IAAI,EAAE,MAAM;YACZ,QAAQ,EAAE,IAAI;YACd,GAAG,EAAE,CAAC,CAAC,EAAE,gCAAgC,CAAC;SAC3C;QACD,SAAS,EAAE;YACT,IAAI,EAAE,MAAM;YACZ,QAAQ,EAAE,IAAI;YACd,GAAG,EAAE,CAAC,CAAC,EAAE,+BAA+B,CAAC;SAC1C;KACF;IACD,iBAAiB,EAAE;QACjB,GAAG,EAAE,MAAM;QACX,GAAG,EAAE,MAAM;QACX,aAAa,EAAE,MAAM;QACrB,WAAW,EAAE,MAAM;QACnB,gBAAgB,EAAE,MAAM;KACzB;IACD,MAAM,EAAE;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC;QAClD,OAAO,EAAE,OAAO;KACjB;IACD,SAAS,EAAE;QACT,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,CAAC,IAAI,EAAE,qBAAqB,CAAC;KACxC;IACD,cAAc,EAAE;QACd,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,CAAC,IAAI,EAAE,2BAA2B,CAAC;KAC9C;CACF,EAAE;IACD,UAAU,EAAE,IAAI;CACjB,CAAC,CAAC;AAGH,kBAAkB,CAAC,GAAG,CAAC,MAAM,EAAE,UAAS,IAAI;IAC1C,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QACvD,IAAI,CAAC,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC,CAAC;IACvD,CAAC;IACD,IAAI,EAAE,CAAC;AACT,CAAC,CAAC,CAAC;AAGH,kBAAkB,CAAC,GAAG,CAAC,MAAM,EAAE,UAAS,IAAI;IAC1C,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACrG,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACnG,IAAI,EAAE,CAAC;AACT,CAAC,CAAC,CAAC;AAGH,kBAAkB,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;AAC9E,kBAAkB,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3C,kBAAkB,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;AACxC,kBAAkB,CAAC,KAAK,CAAC,EAAE,qBAAqB,EAAE,CAAC,EAAE,mBAAmB,EAAE,CAAC,EAAE,CAAC,CAAC;AAElE,QAAA,YAAY,GAAG,kBAAQ,CAAC,KAAK,CAAgB,cAAc,EAAE,kBAAkB,CAAC,CAAC"}