import express from 'express';
import {
  getBusinessCases,
  getBusinessCase,
  createBusinessCase,
  updateBusinessCase,
  deleteBusinessCase,
  exportBusinessCase,
  getBusinessCaseStats
} from '../controllers/businessCaseController';
import { authMiddleware, requireRole } from '../middleware/auth';
import { UserRole } from '../../../shared/src/types';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authMiddleware);

// @route   GET /api/business-cases/stats
// @desc    Get business case statistics
// @access  Private
router.get('/stats', getBusinessCaseStats);

// @route   GET /api/business-cases
// @desc    Get all business cases with filtering and pagination
// @access  Private
router.get('/', getBusinessCases);

// @route   GET /api/business-cases/:id
// @desc    Get single business case
// @access  Private
router.get('/:id', getBusinessCase);

// @route   POST /api/business-cases
// @desc    Create new business case
// @access  Private (Financial Analyst and Admin)
router.post('/', requireRole([UserRole.FINANCIAL_ANALYST, UserRole.ADMIN]), createBusinessCase);

// @route   PUT /api/business-cases/:id
// @desc    Update business case
// @access  Private (Financial Analyst and Admin)
router.put('/:id', requireRole([UserRole.FINANCIAL_ANALYST, UserRole.ADMIN]), updateBusinessCase);

// @route   DELETE /api/business-cases/:id
// @desc    Delete business case
// @access  Private (Admin only)
router.delete('/:id', requireRole([UserRole.ADMIN]), deleteBusinessCase);

// @route   GET /api/business-cases/:id/export
// @desc    Export business case to Excel
// @access  Private
router.get('/:id/export', exportBusinessCase);

export default router;
