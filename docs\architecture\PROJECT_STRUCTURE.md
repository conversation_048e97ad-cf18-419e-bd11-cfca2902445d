# MasterBC Project Structure

**Author: <PERSON><PERSON><PERSON>**  
**Repository: https://github.com/mahegyaneshpandey/spm**  
**Date: January 27, 2025**

## 📁 **Directory Structure**

```
MasterBC/
├── README.md                           # Main project documentation
├── package.json                        # Root package.json for workspace
├── .gitignore                          # Git ignore rules
├── .env.example                        # Environment variables template
├── docker-compose.yml                  # Docker setup for development
├── 
├── apps/                               # Applications directory
│   ├── frontend/                       # React frontend application
│   │   ├── src/
│   │   │   ├── components/             # React components
│   │   │   │   ├── common/             # Shared components
│   │   │   │   ├── layout/             # Layout components
│   │   │   │   └── Tabs/               # Tab-specific components
│   │   │   ├── contexts/               # React contexts
│   │   │   │   ├── AuthContext.js      # Authentication context
│   │   │   │   ├── DataContext.js      # Data management context
│   │   │   │   └── UIContext.js        # UI state context
│   │   │   ├── services/               # API services
│   │   │   │   ├── api.js              # Base API configuration
│   │   │   │   ├── authService.js      # Authentication service
│   │   │   │   ├── businessCaseService.js # Business case API
│   │   │   │   ├── projectService.js   # Project/Program API
│   │   │   │   └── ideaService.js      # Ideas API
│   │   │   ├── hooks/                  # Custom React hooks
│   │   │   │   ├── useAppInitialization.js
│   │   │   │   ├── useAuth.js
│   │   │   │   └── useData.js
│   │   │   ├── utils/                  # Utility functions
│   │   │   │   ├── constants.js        # Application constants
│   │   │   │   ├── helpers.js          # Helper functions
│   │   │   │   └── validators.js       # Validation utilities
│   │   │   └── types/                  # TypeScript type definitions
│   │   ├── public/                     # Static assets
│   │   ├── package.json                # Frontend dependencies
│   │   └── README.md                   # Frontend documentation
│   │
│   └── backend/                        # Node.js backend API
│       ├── src/
│       │   ├── controllers/            # API controllers
│       │   │   ├── authController.js   # Authentication endpoints
│       │   │   ├── businessCaseController.js # Business case endpoints
│       │   │   ├── projectController.js # Project/Program endpoints
│       │   │   └── dashboardController.js # Dashboard endpoints
│       │   ├── models/                 # Data models
│       │   │   ├── BusinessCase.js     # Business case model
│       │   │   ├── Project.js          # Project model
│       │   │   └── User.js             # User model
│       │   ├── routes/                 # API routes
│       │   │   ├── auth.js             # Authentication routes
│       │   │   ├── businessCases.js    # Business case routes
│       │   │   └── projects.js         # Project routes
│       │   ├── services/               # Business logic services
│       │   │   ├── authService.js      # Authentication logic
│       │   │   ├── businessCaseService.js # Business case logic
│       │   │   └── calculationService.js # Financial calculations
│       │   ├── middleware/             # Express middleware
│       │   │   ├── auth.js             # Authentication middleware
│       │   │   ├── validation.js       # Request validation
│       │   │   └── errorHandler.js     # Error handling
│       │   ├── utils/                  # Utility functions
│       │   │   ├── logger.js           # Logging utility
│       │   │   ├── fileUtils.js        # File operations
│       │   │   └── validators.js       # Data validation
│       │   └── types/                  # TypeScript type definitions
│       ├── data/                       # JSON data files
│       │   ├── businessCases.json      # Business case data
│       │   ├── projects.json           # Project data
│       │   └── users.json              # User data
│       ├── package.json                # Backend dependencies
│       └── README.md                   # Backend documentation
│
├── packages/                           # Shared packages
│   ├── shared-types/                   # TypeScript type definitions
│   │   ├── BusinessCase.ts             # Business case types
│   │   ├── Project.ts                  # Project types
│   │   └── index.ts                    # Type exports
│   ├── shared-utils/                   # Common utilities
│   │   ├── calculations.js             # Financial calculations
│   │   ├── formatters.js               # Data formatters
│   │   └── validators.js               # Shared validators
│   └── shared-constants/               # Application constants
│       ├── api.js                      # API constants
│       ├── business.js                 # Business constants
│       └── ui.js                       # UI constants
│
├── docs/                               # Documentation
│   ├── api/                           # API documentation
│   │   ├── README.md                   # API overview
│   │   ├── authentication.md           # Auth endpoints
│   │   ├── business-cases.md           # Business case endpoints
│   │   └── projects.md                 # Project endpoints
│   ├── architecture/                  # Architecture diagrams
│   │   ├── README.md                   # Architecture overview
│   │   ├── data-flow.md                # Data flow diagrams
│   │   └── component-hierarchy.md      # Component structure
│   ├── user-guide/                    # User documentation
│   │   ├── README.md                   # User guide overview
│   │   ├── getting-started.md          # Quick start guide
│   │   └── features.md                 # Feature documentation
│   └── development/                   # Development guides
│       ├── README.md                   # Development overview
│       ├── setup.md                    # Setup instructions
│       └── contributing.md             # Contribution guidelines
│
├── scripts/                            # Build and deployment scripts
│   ├── build.sh                        # Build script
│   ├── deploy.sh                       # Deployment script
│   ├── start-dev.sh                    # Development startup
│   └── setup.sh                        # Initial setup
│
├── tests/                              # Integration and E2E tests
│   ├── e2e/                           # End-to-end tests
│   │   ├── auth.spec.js                # Authentication tests
│   │   ├── business-cases.spec.js      # Business case tests
│   │   └── projects.spec.js            # Project tests
│   ├── integration/                   # Integration tests
│   │   ├── api.test.js                 # API integration tests
│   │   └── database.test.js            # Database tests
│   └── fixtures/                      # Test data
│       ├── businessCases.json          # Test business cases
│       └── projects.json               # Test projects
│
└── tools/                              # Development tools and utilities
    ├── data-migration/                 # Data migration scripts
    │   ├── migrate-v1-to-v2.js         # Version migration
    │   └── seed-data.js                # Data seeding
    ├── excel-templates/                # Excel templates
    │   ├── business-case-template.xlsx # BC template
    │   └── project-template.xlsx       # Project template
    └── test-data/                      # Test data generators
        ├── generate-business-cases.js  # BC data generator
        └── generate-projects.js        # Project data generator
```

## 🏗️ **Architecture Principles**

### **1. Separation of Concerns**
- **Frontend**: Pure React application focused on UI/UX
- **Backend**: RESTful API server handling business logic
- **Shared**: Common utilities and types used by both

### **2. Modular Design**
- **Components**: Reusable, single-responsibility components
- **Services**: Dedicated API interaction layers
- **Contexts**: Centralized state management
- **Hooks**: Custom logic encapsulation

### **3. Scalability**
- **Monorepo**: Easy dependency management and code sharing
- **Workspace**: Independent but connected applications
- **Packages**: Shared code to avoid duplication

### **4. Maintainability**
- **Documentation**: Comprehensive docs for all aspects
- **Testing**: Automated testing at multiple levels
- **Standards**: Consistent coding patterns and conventions

## 🔧 **Key Benefits**

1. **Clear Structure**: Easy to navigate and understand
2. **Reusable Code**: Shared packages reduce duplication
3. **Independent Deployment**: Apps can be deployed separately
4. **Comprehensive Testing**: Multiple testing strategies
5. **Rich Documentation**: Self-documenting codebase
6. **Developer Experience**: Optimized for productivity

## 📚 **Related Documentation**

- [API Documentation](../api/README.md)
- [Component Architecture](./component-hierarchy.md)
- [Data Flow](./data-flow.md)
- [Development Setup](../development/setup.md)
