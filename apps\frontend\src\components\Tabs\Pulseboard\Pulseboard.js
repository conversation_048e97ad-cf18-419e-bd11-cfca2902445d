import React, { useState, useEffect } from 'react';
import PulseboardGrid from './PulseboardGrid/PulseboardGrid';
import PulseboardForm from './PulseboardForm/PulseboardForm';
import PulseboardStats from './PulseboardStats/PulseboardStats';
import ProcessFlow from './ProcessFlow/ProcessFlow';
import './Pulseboard.css';

const Pulseboard = () => {
  const [activeView, setActiveView] = useState('grid'); // 'grid', 'create', 'edit', 'stats', 'process'
  const [pulseboardItems, setPulseboardItems] = useState([]);
  const [editingItem, setEditingItem] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Real data from APIs
  const [projects, setProjects] = useState([]);
  const [programs, setPrograms] = useState([]);
  const [businessCases, setBusinessCases] = useState([]);
  const [masterBusinessCases, setMasterBusinessCases] = useState([]);
  const [epics, setEpics] = useState([]);

  // Fetch real data from APIs
  useEffect(() => {
    fetchPulseboardData();
    fetchReferenceData();
  }, []);

  const fetchPulseboardData = async () => {
    setLoading(true);
    try {
      // For now, we'll use localStorage to persist pulseboard items
      // In a real application, this would be an API call
      const savedItems = localStorage.getItem('pulseboardItems');
      if (savedItems) {
        setPulseboardItems(JSON.parse(savedItems));
      }
    } catch (err) {
      setError('Failed to fetch pulseboard data');
      console.error('Error fetching pulseboard data:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchReferenceData = async () => {
    try {
      // Fetch real projects, programs, business cases, and master business cases
      const [projectsRes, programsRes, businessCasesRes, masterBCRes] = await Promise.all([
        fetch('http://localhost:5000/api/projects'),
        fetch('http://localhost:5000/api/programs'),
        fetch('http://localhost:5000/api/business-cases'),
        fetch('http://localhost:5000/api/master-bc')
      ]);

      if (projectsRes.ok) {
        const projectsData = await projectsRes.json();
        const allProjects = projectsData.data?.projects || [];

        // Separate projects and epics
        const projectsList = allProjects.filter(p => p.type === 'project');
        const epicsList = allProjects.filter(p => p.type === 'epic');

        setProjects(projectsList);
        setEpics(epicsList);
      }

      if (programsRes.ok) {
        const programsData = await programsRes.json();
        setPrograms(programsData.data?.programs || []);
      }

      if (businessCasesRes.ok) {
        const businessCasesData = await businessCasesRes.json();
        setBusinessCases(businessCasesData.data?.businessCases || []);
      }

      if (masterBCRes.ok) {
        const masterBCData = await masterBCRes.json();
        setMasterBusinessCases(masterBCData.data?.masterBusinessCases || []);
      }
    } catch (err) {
      console.error('Error fetching reference data:', err);
    }
  };

  const handleViewChange = (view) => {
    setActiveView(view);
    setEditingItem(null);
    setError(null);
  };

  const handleCreateItem = () => {
    setEditingItem(null);
    setActiveView('create');
  };

  const handleEditItem = (item) => {
    setEditingItem(item);
    setActiveView('edit');
  };

  const handleSaveItem = async (itemData) => {
    try {
      setLoading(true);
      
      let updatedItems;
      if (editingItem) {
        // Update existing item
        updatedItems = pulseboardItems.map(item => 
          item.id === editingItem.id ? { ...itemData, id: editingItem.id, updatedAt: new Date().toISOString() } : item
        );
      } else {
        // Create new item
        const newItem = {
          ...itemData,
          id: `pulse_${Date.now()}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        updatedItems = [...pulseboardItems, newItem];
      }

      setPulseboardItems(updatedItems);
      localStorage.setItem('pulseboardItems', JSON.stringify(updatedItems));
      
      setActiveView('grid');
      setEditingItem(null);
    } catch (err) {
      setError('Failed to save item');
      console.error('Error saving item:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteItem = async (itemId) => {
    if (!window.confirm('Are you sure you want to delete this item?')) {
      return;
    }

    try {
      setLoading(true);
      const updatedItems = pulseboardItems.filter(item => item.id !== itemId);
      setPulseboardItems(updatedItems);
      localStorage.setItem('pulseboardItems', JSON.stringify(updatedItems));
    } catch (err) {
      setError('Failed to delete item');
      console.error('Error deleting item:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkAction = async (action, selectedIds) => {
    try {
      setLoading(true);
      
      switch (action) {
        case 'delete':
          if (window.confirm(`Are you sure you want to delete ${selectedIds.length} items?`)) {
            const updatedItems = pulseboardItems.filter(item => !selectedIds.includes(item.id));
            setPulseboardItems(updatedItems);
            localStorage.setItem('pulseboardItems', JSON.stringify(updatedItems));
          }
          break;
        case 'export':
          // Export functionality would be implemented here
          console.log('Exporting items:', selectedIds);
          break;
        default:
          console.log('Unknown bulk action:', action);
      }
    } catch (err) {
      setError(`Failed to perform bulk action: ${action}`);
      console.error('Error performing bulk action:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading && pulseboardItems.length === 0) {
    return (
      <div className="pulseboard-loading">
        <div className="loading-spinner"></div>
        <p>Loading Pulseboard...</p>
      </div>
    );
  }

  return (
    <div className="pulseboard">
      <div className="pulseboard-header">
        <div className="header-content">
          <h1>📊 Pulseboard</h1>
          <p>Excel-like interface for comprehensive request tracking and validation</p>
        </div>
        
        {error && (
          <div className="error-banner">
            <i className="fas fa-exclamation-triangle"></i>
            {error}
            <button onClick={() => setError(null)} className="close-error">
              <i className="fas fa-times"></i>
            </button>
          </div>
        )}

        <div className="header-actions">
          <button
            className={`view-btn ${activeView === 'grid' ? 'active' : ''}`}
            onClick={() => handleViewChange('grid')}
          >
            <i className="fas fa-table"></i>
            Grid View
          </button>
          <button
            className={`view-btn ${activeView === 'process' ? 'active' : ''}`}
            onClick={() => handleViewChange('process')}
          >
            <i className="fas fa-project-diagram"></i>
            Process Flow
          </button>
          <button
            className={`view-btn ${activeView === 'stats' ? 'active' : ''}`}
            onClick={() => handleViewChange('stats')}
          >
            <i className="fas fa-chart-bar"></i>
            Analytics
          </button>
          <button
            className="create-btn"
            onClick={handleCreateItem}
          >
            <i className="fas fa-plus"></i>
            Add Item
          </button>
        </div>
      </div>

      <div className="pulseboard-content">
        {activeView === 'grid' && (
          <PulseboardGrid
            items={pulseboardItems}
            projects={projects}
            programs={programs}
            businessCases={businessCases}
            onEdit={handleEditItem}
            onDelete={handleDeleteItem}
            onBulkAction={handleBulkAction}
            loading={loading}
          />
        )}

        {(activeView === 'create' || activeView === 'edit') && (
          <PulseboardForm
            item={editingItem}
            projects={projects}
            programs={programs}
            businessCases={businessCases}
            onSave={handleSaveItem}
            onCancel={() => handleViewChange('grid')}
            loading={loading}
          />
        )}

        {activeView === 'stats' && (
          <PulseboardStats
            items={pulseboardItems}
            projects={projects}
            programs={programs}
            businessCases={businessCases}
          />
        )}

        {activeView === 'process' && (
          <ProcessFlow />
        )}
      </div>
    </div>
  );
};

export default Pulseboard;
