import React, { useState, useEffect } from 'react';
import './PulseboardForm.css';

const PulseboardForm = ({ 
  item, 
  projects, 
  programs, 
  businessCases, 
  masterBusinessCases,
  epics,
  onSave, 
  onCancel, 
  loading 
}) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'feature',
    status: 'new',
    priority: 'medium',
    requestor: '',
    assignee: '',
    dueDate: '',
    businessValue: 'medium',
    effort: '',
    progress: 0,
    category: '',
    department: '',
    businessUnit: '',
    impactLevel: 'medium',
    complexity: 'medium',
    riskLevel: 'medium',
    estimatedCost: '',
    expectedBenefit: '',
    stakeholders: '',
    dependencies: '',
    acceptanceCriteria: '',
    notes: '',
    tags: '',
    // Real data linking fields
    linkedProjectId: '',
    linkedEpicId: '',
    linkedProgramId: '',
    linkedBusinessCaseId: '',
    linkedMasterBusinessCaseId: ''
  });

  const [errors, setErrors] = useState({});
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;

  useEffect(() => {
    if (item) {
      setFormData({
        title: item.title || '',
        description: item.description || '',
        type: item.type || 'feature',
        status: item.status || 'new',
        priority: item.priority || 'medium',
        requestor: item.requestor || '',
        assignee: item.assignee || '',
        dueDate: item.dueDate || '',
        businessValue: item.businessValue || 'medium',
        effort: item.effort || '',
        progress: item.progress || 0,
        category: item.category || '',
        department: item.department || '',
        businessUnit: item.businessUnit || '',
        impactLevel: item.impactLevel || 'medium',
        complexity: item.complexity || 'medium',
        riskLevel: item.riskLevel || 'medium',
        estimatedCost: item.estimatedCost || '',
        expectedBenefit: item.expectedBenefit || '',
        stakeholders: item.stakeholders || '',
        dependencies: item.dependencies || '',
        acceptanceCriteria: item.acceptanceCriteria || '',
        notes: item.notes || '',
        tags: item.tags || '',
        linkedProjectId: item.linkedProjectId || '',
        linkedEpicId: item.linkedEpicId || '',
        linkedProgramId: item.linkedProgramId || '',
        linkedBusinessCaseId: item.linkedBusinessCaseId || '',
        linkedMasterBusinessCaseId: item.linkedMasterBusinessCaseId || ''
      });
    }
  }, [item]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const validateStep = (step) => {
    const newErrors = {};
    
    switch (step) {
      case 1: // Basic Information
        if (!formData.title.trim()) newErrors.title = 'Title is required';
        if (!formData.description.trim()) newErrors.description = 'Description is required';
        if (!formData.requestor.trim()) newErrors.requestor = 'Requestor is required';
        break;
      case 2: // Classification & Priority
        if (!formData.category.trim()) newErrors.category = 'Category is required';
        if (!formData.department.trim()) newErrors.department = 'Department is required';
        break;
      case 3: // Linking & Dependencies
        // Optional validation for linking
        break;
      case 4: // Additional Details
        if (formData.effort && isNaN(formData.effort)) {
          newErrors.effort = 'Effort must be a number';
        }
        if (formData.estimatedCost && isNaN(formData.estimatedCost)) {
          newErrors.estimatedCost = 'Estimated cost must be a number';
        }
        break;
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate all steps
    let isValid = true;
    for (let step = 1; step <= totalSteps; step++) {
      if (!validateStep(step)) {
        isValid = false;
        setCurrentStep(step);
        break;
      }
    }
    
    if (isValid) {
      onSave(formData);
    }
  };

  const getLinkedEntityInfo = (entityType, entityId) => {
    let entity = null;
    switch (entityType) {
      case 'project':
        entity = projects.find(p => p.id === entityId);
        break;
      case 'epic':
        entity = epics.find(e => e.id === entityId);
        break;
      case 'program':
        entity = programs.find(p => p.id === entityId);
        break;
      case 'businessCase':
        entity = businessCases.find(bc => bc.id === entityId);
        break;
      case 'masterBusinessCase':
        entity = masterBusinessCases.find(mbc => mbc.id === entityId);
        break;
    }
    
    if (!entity) return null;
    
    return {
      name: entity.name,
      businessUnit: entity.businessUnit,
      status: entity.status,
      owner: entity.owner || entity.createdBy
    };
  };

  const renderStepIndicator = () => (
    <div className="step-indicator">
      {Array.from({ length: totalSteps }, (_, index) => (
        <div 
          key={index + 1}
          className={`step ${currentStep === index + 1 ? 'active' : ''} ${currentStep > index + 1 ? 'completed' : ''}`}
          onClick={() => setCurrentStep(index + 1)}
        >
          <div className="step-number">
            {currentStep > index + 1 ? <i className="fas fa-check"></i> : index + 1}
          </div>
          <div className="step-label">
            {['Basic Info', 'Classification', 'Linking', 'Details'][index]}
          </div>
        </div>
      ))}
    </div>
  );

  const renderStep1 = () => (
    <div className="form-step">
      <h3>📋 Basic Information</h3>
      
      <div className="form-grid">
        <div className="form-group">
          <label>Title *</label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            className={errors.title ? 'error' : ''}
            placeholder="Enter item title"
          />
          {errors.title && <span className="error-text">{errors.title}</span>}
        </div>

        <div className="form-group">
          <label>Type</label>
          <select
            value={formData.type}
            onChange={(e) => handleInputChange('type', e.target.value)}
          >
            <option value="feature">Feature</option>
            <option value="enhancement">Enhancement</option>
            <option value="bug">Bug</option>
            <option value="change">Change Request</option>
            <option value="support">Support</option>
            <option value="research">Research</option>
            <option value="maintenance">Maintenance</option>
            <option value="integration">Integration</option>
          </select>
        </div>

        <div className="form-group full-width">
          <label>Description *</label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            className={errors.description ? 'error' : ''}
            placeholder="Describe the request in detail"
            rows="4"
          />
          {errors.description && <span className="error-text">{errors.description}</span>}
        </div>

        <div className="form-group">
          <label>Requestor *</label>
          <input
            type="text"
            value={formData.requestor}
            onChange={(e) => handleInputChange('requestor', e.target.value)}
            className={errors.requestor ? 'error' : ''}
            placeholder="Who is requesting this?"
          />
          {errors.requestor && <span className="error-text">{errors.requestor}</span>}
        </div>

        <div className="form-group">
          <label>Assignee</label>
          <input
            type="text"
            value={formData.assignee}
            onChange={(e) => handleInputChange('assignee', e.target.value)}
            placeholder="Who will work on this?"
          />
        </div>

        <div className="form-group">
          <label>Due Date</label>
          <input
            type="date"
            value={formData.dueDate}
            onChange={(e) => handleInputChange('dueDate', e.target.value)}
          />
        </div>

        <div className="form-group">
          <label>Status</label>
          <select
            value={formData.status}
            onChange={(e) => handleInputChange('status', e.target.value)}
          >
            <option value="new">New</option>
            <option value="in-progress">In Progress</option>
            <option value="review">Review</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
            <option value="completed">Completed</option>
            <option value="on-hold">On Hold</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="form-step">
      <h3>🏷️ Classification & Priority</h3>

      <div className="form-grid">
        <div className="form-group">
          <label>Category *</label>
          <input
            type="text"
            value={formData.category}
            onChange={(e) => handleInputChange('category', e.target.value)}
            className={errors.category ? 'error' : ''}
            placeholder="e.g., UI/UX, Backend, Infrastructure"
          />
          {errors.category && <span className="error-text">{errors.category}</span>}
        </div>

        <div className="form-group">
          <label>Department *</label>
          <input
            type="text"
            value={formData.department}
            onChange={(e) => handleInputChange('department', e.target.value)}
            className={errors.department ? 'error' : ''}
            placeholder="e.g., IT, Marketing, Operations"
          />
          {errors.department && <span className="error-text">{errors.department}</span>}
        </div>

        <div className="form-group">
          <label>Business Unit</label>
          <select
            value={formData.businessUnit}
            onChange={(e) => handleInputChange('businessUnit', e.target.value)}
          >
            <option value="">Select Business Unit</option>
            <option value="Technology">Technology</option>
            <option value="Customer Service">Customer Service</option>
            <option value="Operations">Operations</option>
            <option value="Supply Chain">Supply Chain</option>
            <option value="Manufacturing">Manufacturing</option>
            <option value="Analytics">Analytics</option>
            <option value="Infrastructure">Infrastructure</option>
            <option value="Security">Security</option>
            <option value="Human Resources">Human Resources</option>
          </select>
        </div>

        <div className="form-group">
          <label>Priority</label>
          <select
            value={formData.priority}
            onChange={(e) => handleInputChange('priority', e.target.value)}
          >
            <option value="critical">Critical</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>

        <div className="form-group">
          <label>Business Value</label>
          <select
            value={formData.businessValue}
            onChange={(e) => handleInputChange('businessValue', e.target.value)}
          >
            <option value="critical">Critical</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>

        <div className="form-group">
          <label>Impact Level</label>
          <select
            value={formData.impactLevel}
            onChange={(e) => handleInputChange('impactLevel', e.target.value)}
          >
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>

        <div className="form-group">
          <label>Complexity</label>
          <select
            value={formData.complexity}
            onChange={(e) => handleInputChange('complexity', e.target.value)}
          >
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>

        <div className="form-group">
          <label>Risk Level</label>
          <select
            value={formData.riskLevel}
            onChange={(e) => handleInputChange('riskLevel', e.target.value)}
          >
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="form-step">
      <h3>🔗 Real Data Linking & Dependencies</h3>

      <div className="form-grid">
        <div className="form-group">
          <label>Link to Project</label>
          <select
            value={formData.linkedProjectId}
            onChange={(e) => handleInputChange('linkedProjectId', e.target.value)}
          >
            <option value="">Select Project</option>
            {projects.map(project => (
              <option key={project.id} value={project.id}>
                {project.name} ({project.businessUnit})
              </option>
            ))}
          </select>
          {formData.linkedProjectId && (
            <div className="linked-info">
              {(() => {
                const info = getLinkedEntityInfo('project', formData.linkedProjectId);
                return info ? (
                  <div className="entity-info">
                    <span className="entity-name">{info.name}</span>
                    <span className="entity-details">{info.businessUnit} • {info.status}</span>
                    <span className="entity-owner">Owner: {info.owner}</span>
                  </div>
                ) : null;
              })()}
            </div>
          )}
        </div>

        <div className="form-group">
          <label>Link to Epic</label>
          <select
            value={formData.linkedEpicId}
            onChange={(e) => handleInputChange('linkedEpicId', e.target.value)}
          >
            <option value="">Select Epic</option>
            {epics.map(epic => (
              <option key={epic.id} value={epic.id}>
                {epic.name} ({epic.businessUnit})
              </option>
            ))}
          </select>
          {formData.linkedEpicId && (
            <div className="linked-info">
              {(() => {
                const info = getLinkedEntityInfo('epic', formData.linkedEpicId);
                return info ? (
                  <div className="entity-info">
                    <span className="entity-name">{info.name}</span>
                    <span className="entity-details">{info.businessUnit} • {info.status}</span>
                    <span className="entity-owner">Owner: {info.owner}</span>
                  </div>
                ) : null;
              })()}
            </div>
          )}
        </div>

        <div className="form-group">
          <label>Link to Program</label>
          <select
            value={formData.linkedProgramId}
            onChange={(e) => handleInputChange('linkedProgramId', e.target.value)}
          >
            <option value="">Select Program</option>
            {programs.map(program => (
              <option key={program.id} value={program.id}>
                {program.name} ({program.businessUnit})
              </option>
            ))}
          </select>
          {formData.linkedProgramId && (
            <div className="linked-info">
              {(() => {
                const info = getLinkedEntityInfo('program', formData.linkedProgramId);
                return info ? (
                  <div className="entity-info">
                    <span className="entity-name">{info.name}</span>
                    <span className="entity-details">{info.businessUnit} • {info.status}</span>
                    <span className="entity-owner">Owner: {info.owner}</span>
                  </div>
                ) : null;
              })()}
            </div>
          )}
        </div>

        <div className="form-group">
          <label>Link to Business Case</label>
          <select
            value={formData.linkedBusinessCaseId}
            onChange={(e) => handleInputChange('linkedBusinessCaseId', e.target.value)}
          >
            <option value="">Select Business Case</option>
            {businessCases.map(bc => (
              <option key={bc.id} value={bc.id}>
                {bc.name} ({bc.businessUnit})
              </option>
            ))}
          </select>
          {formData.linkedBusinessCaseId && (
            <div className="linked-info">
              {(() => {
                const info = getLinkedEntityInfo('businessCase', formData.linkedBusinessCaseId);
                return info ? (
                  <div className="entity-info">
                    <span className="entity-name">{info.name}</span>
                    <span className="entity-details">{info.businessUnit} • {info.status}</span>
                    <span className="entity-owner">Created by: {info.owner}</span>
                  </div>
                ) : null;
              })()}
            </div>
          )}
        </div>

        <div className="form-group">
          <label>Link to Master Business Case</label>
          <select
            value={formData.linkedMasterBusinessCaseId}
            onChange={(e) => handleInputChange('linkedMasterBusinessCaseId', e.target.value)}
          >
            <option value="">Select Master Business Case</option>
            {masterBusinessCases.map(mbc => (
              <option key={mbc.id} value={mbc.id}>
                {mbc.name} ({mbc.businessUnit})
              </option>
            ))}
          </select>
          {formData.linkedMasterBusinessCaseId && (
            <div className="linked-info">
              {(() => {
                const info = getLinkedEntityInfo('masterBusinessCase', formData.linkedMasterBusinessCaseId);
                return info ? (
                  <div className="entity-info">
                    <span className="entity-name">{info.name}</span>
                    <span className="entity-details">{info.businessUnit} • {info.status}</span>
                    <span className="entity-owner">Created by: {info.owner}</span>
                  </div>
                ) : null;
              })()}
            </div>
          )}
        </div>

        <div className="form-group full-width">
          <label>Dependencies</label>
          <textarea
            value={formData.dependencies}
            onChange={(e) => handleInputChange('dependencies', e.target.value)}
            placeholder="List any dependencies or prerequisites"
            rows="3"
          />
        </div>

        <div className="form-group full-width">
          <label>Stakeholders</label>
          <textarea
            value={formData.stakeholders}
            onChange={(e) => handleInputChange('stakeholders', e.target.value)}
            placeholder="List key stakeholders and their roles"
            rows="3"
          />
        </div>
      </div>
    </div>
  );

  const renderStep4 = () => (
    <div className="form-step">
      <h3>📊 Additional Details</h3>

      <div className="form-grid">
        <div className="form-group">
          <label>Effort (Hours)</label>
          <input
            type="number"
            value={formData.effort}
            onChange={(e) => handleInputChange('effort', e.target.value)}
            className={errors.effort ? 'error' : ''}
            placeholder="Estimated effort in hours"
            min="0"
          />
          {errors.effort && <span className="error-text">{errors.effort}</span>}
        </div>

        <div className="form-group">
          <label>Progress (%)</label>
          <input
            type="range"
            min="0"
            max="100"
            value={formData.progress}
            onChange={(e) => handleInputChange('progress', parseInt(e.target.value))}
            className="progress-slider"
          />
          <div className="progress-display">{formData.progress}%</div>
        </div>

        <div className="form-group">
          <label>Estimated Cost ($)</label>
          <input
            type="number"
            value={formData.estimatedCost}
            onChange={(e) => handleInputChange('estimatedCost', e.target.value)}
            className={errors.estimatedCost ? 'error' : ''}
            placeholder="Estimated cost in USD"
            min="0"
            step="0.01"
          />
          {errors.estimatedCost && <span className="error-text">{errors.estimatedCost}</span>}
        </div>

        <div className="form-group">
          <label>Expected Benefit</label>
          <input
            type="text"
            value={formData.expectedBenefit}
            onChange={(e) => handleInputChange('expectedBenefit', e.target.value)}
            placeholder="Expected business benefit"
          />
        </div>

        <div className="form-group full-width">
          <label>Acceptance Criteria</label>
          <textarea
            value={formData.acceptanceCriteria}
            onChange={(e) => handleInputChange('acceptanceCriteria', e.target.value)}
            placeholder="Define what constitutes completion"
            rows="4"
          />
        </div>

        <div className="form-group full-width">
          <label>Notes</label>
          <textarea
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="Additional notes or comments"
            rows="3"
          />
        </div>

        <div className="form-group">
          <label>Tags</label>
          <input
            type="text"
            value={formData.tags}
            onChange={(e) => handleInputChange('tags', e.target.value)}
            placeholder="Comma-separated tags"
          />
        </div>
      </div>
    </div>
  );

  return (
    <div className="pulseboard-form">
      <div className="form-header">
        <h2>{item ? 'Edit Item' : 'Create New Item'}</h2>
        {renderStepIndicator()}
      </div>

      <form onSubmit={handleSubmit} className="form-content">
        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}
        {currentStep === 4 && renderStep4()}

        <div className="form-actions">
          <div className="actions-left">
            {currentStep > 1 && (
              <button type="button" onClick={handlePrevious} className="btn-secondary">
                <i className="fas fa-arrow-left"></i>
                Previous
              </button>
            )}
          </div>
          
          <div className="actions-right">
            <button type="button" onClick={onCancel} className="btn-cancel">
              Cancel
            </button>
            
            {currentStep < totalSteps ? (
              <button type="button" onClick={handleNext} className="btn-primary">
                Next
                <i className="fas fa-arrow-right"></i>
              </button>
            ) : (
              <button type="submit" className="btn-primary" disabled={loading}>
                {loading ? (
                  <>
                    <div className="spinner"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <i className="fas fa-save"></i>
                    {item ? 'Update' : 'Create'}
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default PulseboardForm;
