"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExcelExportService = void 0;
const XLSX = __importStar(require("xlsx"));
class ExcelExportService {
    static async exportBusinessCase(businessCase) {
        const workbook = XLSX.utils.book_new();
        const summaryData = this.createSummarySheet(businessCase);
        const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
        XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
        const financialData = this.createFinancialDataSheet(businessCase);
        const financialSheet = XLSX.utils.aoa_to_sheet(financialData);
        XLSX.utils.book_append_sheet(workbook, financialSheet, 'Financial Data');
        const cashFlowData = this.createCashFlowAnalysisSheet(businessCase);
        const cashFlowSheet = XLSX.utils.aoa_to_sheet(cashFlowData);
        XLSX.utils.book_append_sheet(workbook, cashFlowSheet, 'Cash Flow Analysis');
        const metricsData = this.createMetricsCalculationSheet(businessCase);
        const metricsSheet = XLSX.utils.aoa_to_sheet(metricsData);
        XLSX.utils.book_append_sheet(workbook, metricsSheet, 'Financial Metrics');
        const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
        return excelBuffer;
    }
    static createSummarySheet(businessCase) {
        return [
            ['Business Case Financial Analysis'],
            [''],
            ['Business Case Name:', businessCase.name],
            ['Description:', businessCase.description || 'N/A'],
            ['Business Unit:', businessCase.businessUnit || 'N/A'],
            ['Status:', businessCase.status],
            ['Created By:', businessCase.createdBy],
            ['Created Date:', businessCase.createdAt.toLocaleDateString()],
            ['Last Modified:', businessCase.updatedAt.toLocaleDateString()],
            [''],
            ['Timeframe'],
            ['Start Year:', businessCase.timeframe.startYear],
            ['End Year:', businessCase.timeframe.endYear],
            ['Duration (Years):', businessCase.timeframe.endYear - businessCase.timeframe.startYear + 1],
            [''],
            ['Financial Summary'],
            ['Total CAPEX:', businessCase.financialData.totalCapex],
            ['Total OPEX:', businessCase.financialData.totalOpex],
            ['Total Investment:', businessCase.financialData.totalCapex + businessCase.financialData.totalOpex],
            [''],
            ['Calculated Metrics'],
            ['IRR (%):', businessCase.calculatedMetrics?.irr || 'N/A'],
            ['NPV ($):', businessCase.calculatedMetrics?.npv || 'N/A'],
            ['Payback Period (Years):', businessCase.calculatedMetrics?.paybackPeriod || 'N/A'],
            ['Gross Margin (%):', businessCase.calculatedMetrics?.grossMargin || 'N/A'],
            ['Commercial Margin (%):', businessCase.calculatedMetrics?.commercialMargin || 'N/A'],
            [''],
            ['Tags:', businessCase.tags?.join(', ') || 'None']
        ];
    }
    static createFinancialDataSheet(businessCase) {
        const data = [
            ['Financial Data Breakdown'],
            [''],
            ['CAPEX by Year'],
            ['Year', 'Amount ($)', 'Description']
        ];
        businessCase.financialData.capex.forEach(item => {
            data.push([item.year, item.amount, item.description || '']);
        });
        data.push(['', '', '']);
        data.push(['Total CAPEX:', businessCase.financialData.totalCapex, '']);
        data.push(['', '', '']);
        data.push(['OPEX by Year']);
        data.push(['Year', 'Amount ($)', 'Description']);
        businessCase.financialData.opex.forEach(item => {
            data.push([item.year, item.amount, item.description || '']);
        });
        data.push(['', '', '']);
        data.push(['Total OPEX:', businessCase.financialData.totalOpex, '']);
        if (businessCase.financialData.revenue && businessCase.financialData.revenue.length > 0) {
            data.push(['', '', '']);
            data.push(['Revenue by Year']);
            data.push(['Year', 'Amount ($)', 'Description']);
            businessCase.financialData.revenue.forEach(item => {
                data.push([item.year, item.amount, item.description || '']);
            });
            const totalRevenue = businessCase.financialData.revenue.reduce((sum, item) => sum + item.amount, 0);
            data.push(['', '', '']);
            data.push(['Total Revenue:', totalRevenue, '']);
        }
        return data;
    }
    static createCashFlowAnalysisSheet(businessCase) {
        const startYear = businessCase.timeframe.startYear;
        const endYear = businessCase.timeframe.endYear;
        const years = [];
        for (let year = startYear; year <= endYear; year++) {
            years.push(year);
        }
        const data = [
            ['Cash Flow Analysis'],
            [''],
            ['Year', ...years],
            ['']
        ];
        const capexRow = ['CAPEX'];
        years.forEach(year => {
            const capexItem = businessCase.financialData.capex.find(item => item.year === year);
            capexRow.push(capexItem ? -capexItem.amount : 0);
        });
        data.push(capexRow);
        const opexRow = ['OPEX'];
        years.forEach(year => {
            const opexItem = businessCase.financialData.opex.find(item => item.year === year);
            opexRow.push(opexItem ? -opexItem.amount : 0);
        });
        data.push(opexRow);
        if (businessCase.financialData.revenue && businessCase.financialData.revenue.length > 0) {
            const revenueRow = ['Revenue'];
            years.forEach(year => {
                const revenueItem = businessCase.financialData.revenue.find(item => item.year === year);
                revenueRow.push(revenueItem ? revenueItem.amount : 0);
            });
            data.push(revenueRow);
        }
        const netCashFlowRow = ['Net Cash Flow'];
        years.forEach((year, index) => {
            const col = this.getExcelColumn(index + 2);
            if (businessCase.financialData.revenue && businessCase.financialData.revenue.length > 0) {
                netCashFlowRow.push(`=C${data.length + 1}+D${data.length + 1}+E${data.length + 1}`);
            }
            else {
                netCashFlowRow.push(`=C${data.length + 1}+D${data.length + 1}`);
            }
        });
        data.push(netCashFlowRow);
        const cumulativeCashFlowRow = ['Cumulative Cash Flow'];
        years.forEach((year, index) => {
            const col = this.getExcelColumn(index + 2);
            if (index === 0) {
                cumulativeCashFlowRow.push(`=${col}${data.length}`);
            }
            else {
                const prevCol = this.getExcelColumn(index + 1);
                cumulativeCashFlowRow.push(`=${prevCol}${data.length + 1}+${col}${data.length}`);
            }
        });
        data.push(cumulativeCashFlowRow);
        return data;
    }
    static createMetricsCalculationSheet(businessCase) {
        const startYear = businessCase.timeframe.startYear;
        const endYear = businessCase.timeframe.endYear;
        const years = [];
        for (let year = startYear; year <= endYear; year++) {
            years.push(year);
        }
        const data = [
            ['Financial Metrics Calculations'],
            [''],
            ['Discount Rate (%):', 10],
            [''],
            ['Cash Flow Data for Calculations'],
            ['Year', ...years],
            ['']
        ];
        const netCashFlowRow = ['Net Cash Flow'];
        years.forEach(year => {
            const capexItem = businessCase.financialData.capex.find(item => item.year === year);
            const opexItem = businessCase.financialData.opex.find(item => item.year === year);
            const revenueItem = businessCase.financialData.revenue?.find(item => item.year === year);
            const capex = capexItem ? capexItem.amount : 0;
            const opex = opexItem ? opexItem.amount : 0;
            const revenue = revenueItem ? revenueItem.amount : 0;
            netCashFlowRow.push(revenue - capex - opex);
        });
        data.push(netCashFlowRow);
        data.push(['']);
        data.push(['Financial Metrics']);
        data.push(['']);
        const cashFlowRange = `B${data.length - 3}:${this.getExcelColumn(years.length + 1)}${data.length - 3}`;
        data.push(['IRR (%):', `=IRR(${cashFlowRange})*100`]);
        const discountRate = 'B3';
        data.push(['NPV ($):', `=NPV(${discountRate}/100,${cashFlowRange})`]);
        data.push(['Payback Period (Years):', this.createPaybackFormula(years.length)]);
        if (businessCase.financialData.revenue && businessCase.financialData.revenue.length > 0) {
            const totalRevenue = businessCase.financialData.revenue.reduce((sum, item) => sum + item.amount, 0);
            const totalCosts = businessCase.financialData.totalCapex + businessCase.financialData.totalOpex;
            data.push(['Gross Margin (%):', `=(${totalRevenue}-${totalCosts})/${totalRevenue}*100`]);
        }
        else {
            data.push(['Gross Margin (%):', 'N/A - No revenue data']);
        }
        data.push(['Commercial Margin (%):', 'Custom calculation based on business logic']);
        data.push(['']);
        data.push(['Notes:']);
        data.push(['- IRR: Internal Rate of Return']);
        data.push(['- NPV: Net Present Value']);
        data.push(['- Payback Period: Time to recover initial investment']);
        data.push(['- Gross Margin: (Revenue - Total Costs) / Revenue']);
        data.push(['- Commercial Margin: Custom calculation based on specific business rules']);
        return data;
    }
    static getExcelColumn(index) {
        let column = '';
        while (index > 0) {
            index--;
            column = String.fromCharCode(65 + (index % 26)) + column;
            index = Math.floor(index / 26);
        }
        return column;
    }
    static createPaybackFormula(yearCount) {
        return `"Calculated based on cumulative cash flow"`;
    }
}
exports.ExcelExportService = ExcelExportService;
//# sourceMappingURL=excelExport.js.map