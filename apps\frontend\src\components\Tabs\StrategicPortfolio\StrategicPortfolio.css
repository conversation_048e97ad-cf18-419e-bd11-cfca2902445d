.strategic-portfolio {
  background: #f8fafc;
  min-height: 100vh;
  padding: 24px;
}

.portfolio-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.header-content {
  flex: 1;
}

.portfolio-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.portfolio-subtitle {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.refresh-btn:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.view-navigation {
  display: flex;
  gap: 4px;
  margin-bottom: 24px;
  background: white;
  padding: 8px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.view-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: transparent;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.view-btn:hover {
  background: #f1f5f9;
  color: #334155;
}

.view-btn.active {
  background: #3b82f6;
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.view-btn i {
  font-size: 1rem;
}

.portfolio-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

/* Loading Spinner */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .strategic-portfolio {
    padding: 16px;
  }
  
  .portfolio-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .portfolio-title {
    font-size: 1.5rem;
  }
  
  .view-navigation {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .view-btn {
    flex: 1;
    min-width: 140px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .view-btn {
    padding: 10px 12px;
    font-size: 0.9rem;
  }
  
  .view-btn i {
    font-size: 0.9rem;
  }
  
  .portfolio-title {
    font-size: 1.25rem;
  }
  
  .portfolio-subtitle {
    font-size: 0.9rem;
  }
}
