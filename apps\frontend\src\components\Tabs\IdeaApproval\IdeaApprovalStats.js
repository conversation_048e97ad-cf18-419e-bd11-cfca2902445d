import React from 'react';

const IdeaApprovalStats = ({ ideas = [] }) => {
  // Calculate statistics
  const totalIdeas = ideas.length;
  const submittedCount = ideas.filter(idea => idea.status === 'submitted').length;
  const underReviewCount = ideas.filter(idea => idea.status === 'under-review').length;
  const approvedCount = ideas.filter(idea => idea.status === 'approved').length;
  const rejectedCount = ideas.filter(idea => idea.status === 'rejected').length;
  const onHoldCount = ideas.filter(idea => idea.status === 'on-hold').length;

  const pendingCount = submittedCount + underReviewCount;
  const processedCount = approvedCount + rejectedCount;

  // Calculate approval rate
  const approvalRate = processedCount > 0 ? ((approvedCount / processedCount) * 100).toFixed(1) : 0;

  // Priority breakdown
  const priorityStats = {
    critical: ideas.filter(idea => idea.priority === 'critical').length,
    high: ideas.filter(idea => idea.priority === 'high').length,
    medium: ideas.filter(idea => idea.priority === 'medium').length,
    low: ideas.filter(idea => idea.priority === 'low').length
  };

  // Business unit breakdown
  const businessUnitStats = ideas.reduce((acc, idea) => {
    const unit = idea.businessUnitId || 'unknown';
    acc[unit] = (acc[unit] || 0) + 1;
    return acc;
  }, {});

  const businessUnitNames = {
    'bu1': 'Technology',
    'bu2': 'Marketing',
    'bu3': 'Operations', 
    'bu4': 'Finance',
    'bu5': 'Human Resources',
    'bu6': 'Sales',
    'unknown': 'Unknown'
  };

  // Recent activity (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  const recentIdeas = ideas.filter(idea => {
    const ideaDate = new Date(idea.submissionDate || idea.createdAt);
    return ideaDate >= thirtyDaysAgo;
  });

  const StatCard = ({ title, value, icon, color, subtitle, trend }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className={`text-3xl font-bold ${color}`}>{value}</p>
          {subtitle && <p className="text-sm text-gray-500 mt-1">{subtitle}</p>}
        </div>
        <div className={`p-3 rounded-full ${color.replace('text-', 'bg-').replace('-600', '-100')}`}>
          <i className={`${icon} text-xl ${color}`}></i>
        </div>
      </div>
      {trend && (
        <div className="mt-4 flex items-center">
          <span className="text-sm text-gray-600">{trend}</span>
        </div>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Ideas"
          value={totalIdeas}
          icon="fas fa-lightbulb"
          color="text-blue-600"
          subtitle="All time"
        />
        <StatCard
          title="Pending Review"
          value={pendingCount}
          icon="fas fa-clock"
          color="text-orange-600"
          subtitle={`${submittedCount} submitted, ${underReviewCount} under review`}
        />
        <StatCard
          title="Approved"
          value={approvedCount}
          icon="fas fa-check-circle"
          color="text-green-600"
          subtitle={`${approvalRate}% approval rate`}
        />
        <StatCard
          title="Recent Activity"
          value={recentIdeas.length}
          icon="fas fa-calendar"
          color="text-purple-600"
          subtitle="Last 30 days"
        />
      </div>

      {/* Status Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Status Breakdown</h3>
          <div className="space-y-4">
            {[
              { status: 'submitted', count: submittedCount, color: 'bg-blue-500', label: 'Submitted' },
              { status: 'under-review', count: underReviewCount, color: 'bg-yellow-500', label: 'Under Review' },
              { status: 'approved', count: approvedCount, color: 'bg-green-500', label: 'Approved' },
              { status: 'rejected', count: rejectedCount, color: 'bg-red-500', label: 'Rejected' },
              { status: 'on-hold', count: onHoldCount, color: 'bg-gray-500', label: 'On Hold' }
            ].map(item => (
              <div key={item.status} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full ${item.color} mr-3`}></div>
                  <span className="text-sm font-medium text-gray-700">{item.label}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-sm font-bold text-gray-900 mr-2">{item.count}</span>
                  <div className="w-20 bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${item.color}`}
                      style={{ width: `${totalIdeas > 0 ? (item.count / totalIdeas) * 100 : 0}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Priority Breakdown */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Priority Distribution</h3>
          <div className="space-y-4">
            {[
              { priority: 'critical', count: priorityStats.critical, color: 'bg-red-500', label: 'Critical' },
              { priority: 'high', count: priorityStats.high, color: 'bg-orange-500', label: 'High' },
              { priority: 'medium', count: priorityStats.medium, color: 'bg-yellow-500', label: 'Medium' },
              { priority: 'low', count: priorityStats.low, color: 'bg-green-500', label: 'Low' }
            ].map(item => (
              <div key={item.priority} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full ${item.color} mr-3`}></div>
                  <span className="text-sm font-medium text-gray-700">{item.label}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-sm font-bold text-gray-900 mr-2">{item.count}</span>
                  <div className="w-20 bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${item.color}`}
                      style={{ width: `${totalIdeas > 0 ? (item.count / totalIdeas) * 100 : 0}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Business Unit Breakdown */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Ideas by Business Unit</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(businessUnitStats).map(([unitId, count]) => (
            <div key={unitId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <i className="fas fa-building text-gray-600 mr-3"></i>
                <span className="text-sm font-medium text-gray-700">
                  {businessUnitNames[unitId] || unitId}
                </span>
              </div>
              <span className="text-sm font-bold text-gray-900">{count}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
          <div className="text-3xl font-bold text-green-600 mb-2">{approvalRate}%</div>
          <div className="text-sm font-medium text-gray-700">Approval Rate</div>
          <div className="text-xs text-gray-500 mt-1">
            {approvedCount} approved of {processedCount} processed
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
          <div className="text-3xl font-bold text-blue-600 mb-2">
            {totalIdeas > 0 ? Math.round((processedCount / totalIdeas) * 100) : 0}%
          </div>
          <div className="text-sm font-medium text-gray-700">Processing Rate</div>
          <div className="text-xs text-gray-500 mt-1">
            {processedCount} processed of {totalIdeas} total
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
          <div className="text-3xl font-bold text-orange-600 mb-2">{pendingCount}</div>
          <div className="text-sm font-medium text-gray-700">Pending Review</div>
          <div className="text-xs text-gray-500 mt-1">
            Requires attention
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      {pendingCount > 0 && (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-orange-900">Action Required</h3>
              <p className="text-orange-700 mt-1">
                You have {pendingCount} idea{pendingCount !== 1 ? 's' : ''} waiting for review.
              </p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => window.location.hash = '#pending'}
                className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors"
              >
                <i className="fas fa-eye mr-2"></i>
                Review Pending
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default IdeaApprovalStats;
