"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SensitivityAnalysisService = void 0;
const types_1 = require("../../../shared/src/types");
const financialCalculations_1 = require("./financialCalculations");
class SensitivityAnalysisService {
    static performSensitivityAnalysis(baseModel, variables) {
        const scenarios = this.generateScenarios(baseModel, variables);
        const results = this.calculateSensitivityResults(scenarios, variables);
        return {
            id: this.generateId(),
            name: `Sensitivity Analysis - ${baseModel.name}`,
            variables,
            scenarios,
            results,
            createdBy: baseModel.createdBy,
            createdAt: new Date(),
            updatedAt: new Date()
        };
    }
    static generateScenarios(baseModel, variables) {
        const scenarios = [];
        const baseVariables = {};
        variables.forEach(variable => {
            baseVariables[variable.id] = variable.baseValue;
        });
        const baseResults = financialCalculations_1.FinancialCalculationService.calculateFinancialResults(this.applyVariablesToModel(baseModel, baseVariables));
        scenarios.push({
            id: this.generateId(),
            name: 'Base Case',
            type: types_1.ScenarioType.BASE_CASE,
            variables: baseVariables,
            results: baseResults
        });
        const bestVariables = {};
        variables.forEach(variable => {
            bestVariables[variable.id] = this.getBestCaseValue(variable);
        });
        const bestResults = financialCalculations_1.FinancialCalculationService.calculateFinancialResults(this.applyVariablesToModel(baseModel, bestVariables));
        scenarios.push({
            id: this.generateId(),
            name: 'Best Case',
            type: types_1.ScenarioType.BEST_CASE,
            variables: bestVariables,
            results: bestResults
        });
        const worstVariables = {};
        variables.forEach(variable => {
            worstVariables[variable.id] = this.getWorstCaseValue(variable);
        });
        const worstResults = financialCalculations_1.FinancialCalculationService.calculateFinancialResults(this.applyVariablesToModel(baseModel, worstVariables));
        scenarios.push({
            id: this.generateId(),
            name: 'Worst Case',
            type: types_1.ScenarioType.WORST_CASE,
            variables: worstVariables,
            results: worstResults
        });
        variables.forEach(variable => {
            const highVariables = { ...baseVariables };
            highVariables[variable.id] = variable.maxValue;
            const highResults = financialCalculations_1.FinancialCalculationService.calculateFinancialResults(this.applyVariablesToModel(baseModel, highVariables));
            scenarios.push({
                id: this.generateId(),
                name: `${variable.name} - High`,
                type: types_1.ScenarioType.CUSTOM,
                variables: highVariables,
                results: highResults
            });
            const lowVariables = { ...baseVariables };
            lowVariables[variable.id] = variable.minValue;
            const lowResults = financialCalculations_1.FinancialCalculationService.calculateFinancialResults(this.applyVariablesToModel(baseModel, lowVariables));
            scenarios.push({
                id: this.generateId(),
                name: `${variable.name} - Low`,
                type: types_1.ScenarioType.CUSTOM,
                variables: lowVariables,
                results: lowResults
            });
        });
        return scenarios;
    }
    static calculateSensitivityResults(scenarios, variables) {
        const baseScenario = scenarios.find(s => s.type === types_1.ScenarioType.BASE_CASE);
        if (!baseScenario)
            throw new Error('Base case scenario not found');
        const results = [];
        variables.forEach(variable => {
            const highScenario = scenarios.find(s => s.name === `${variable.name} - High` && s.type === types_1.ScenarioType.CUSTOM);
            const lowScenario = scenarios.find(s => s.name === `${variable.name} - Low` && s.type === types_1.ScenarioType.CUSTOM);
            if (highScenario && lowScenario) {
                const impact = this.calculateImpact(baseScenario, highScenario, lowScenario);
                const correlation = this.calculateCorrelation(variable, baseScenario, highScenario, lowScenario);
                const elasticity = this.calculateElasticity(variable, baseScenario, highScenario);
                results.push({
                    variableId: variable.id,
                    variableName: variable.name,
                    impact,
                    correlation,
                    elasticity
                });
            }
        });
        return results.sort((a, b) => Math.abs(b.impact) - Math.abs(a.impact));
    }
    static calculateImpact(baseScenario, highScenario, lowScenario) {
        const baseNPV = baseScenario.results.npv;
        const highNPV = highScenario.results.npv;
        const lowNPV = lowScenario.results.npv;
        const highImpact = Math.abs(highNPV - baseNPV);
        const lowImpact = Math.abs(lowNPV - baseNPV);
        return Math.max(highImpact, lowImpact);
    }
    static calculateCorrelation(variable, baseScenario, highScenario, lowScenario) {
        const baseNPV = baseScenario.results.npv;
        const highNPV = highScenario.results.npv;
        const lowNPV = lowScenario.results.npv;
        const variableChange = variable.maxValue - variable.minValue;
        const npvChange = highNPV - lowNPV;
        return npvChange / Math.abs(variableChange);
    }
    static calculateElasticity(variable, baseScenario, highScenario) {
        const baseNPV = baseScenario.results.npv;
        const highNPV = highScenario.results.npv;
        if (baseNPV === 0 || variable.baseValue === 0)
            return 0;
        const percentChangeNPV = (highNPV - baseNPV) / baseNPV;
        const percentChangeVariable = (variable.maxValue - variable.baseValue) / variable.baseValue;
        return percentChangeNPV / percentChangeVariable;
    }
    static applyVariablesToModel(baseModel, variables) {
        const model = JSON.parse(JSON.stringify(baseModel));
        Object.entries(variables).forEach(([variableId, value]) => {
            const variable = this.findVariableById(variableId);
            if (variable) {
                this.applyVariableToModel(model, variable, value);
            }
        });
        return model;
    }
    static applyVariableToModel(model, variable, value) {
        switch (variable.category) {
            case types_1.VariableCategory.RATE:
                if (variable.name.toLowerCase().includes('discount')) {
                    model.parameters.discountRate = value;
                }
                else if (variable.name.toLowerCase().includes('tax')) {
                    model.parameters.taxRate = value;
                }
                break;
            case types_1.VariableCategory.COST:
                model.costStructure.opex.forEach(opex => {
                    opex.amount *= (value / variable.baseValue);
                });
                break;
            case types_1.VariableCategory.PRICE:
                model.salesStructure.regions.forEach(region => {
                    region.price *= (value / variable.baseValue);
                });
                model.salesStructure.offers.forEach(offer => {
                    offer.unitPrice *= (value / variable.baseValue);
                });
                break;
            case types_1.VariableCategory.VOLUME:
                model.salesStructure.regions.forEach(region => {
                    region.volume *= (value / variable.baseValue);
                });
                model.salesStructure.offers.forEach(offer => {
                    offer.volume *= (value / variable.baseValue);
                });
                break;
        }
    }
    static getBestCaseValue(variable) {
        switch (variable.category) {
            case types_1.VariableCategory.COST:
                return variable.minValue;
            case types_1.VariableCategory.PRICE:
            case types_1.VariableCategory.VOLUME:
                return variable.maxValue;
            case types_1.VariableCategory.RATE:
                return variable.name.toLowerCase().includes('tax') ? variable.minValue : variable.maxValue;
            default:
                return variable.maxValue;
        }
    }
    static getWorstCaseValue(variable) {
        switch (variable.category) {
            case types_1.VariableCategory.COST:
                return variable.maxValue;
            case types_1.VariableCategory.PRICE:
            case types_1.VariableCategory.VOLUME:
                return variable.minValue;
            case types_1.VariableCategory.RATE:
                return variable.name.toLowerCase().includes('tax') ? variable.maxValue : variable.minValue;
            default:
                return variable.minValue;
        }
    }
    static findVariableById(id) {
        return null;
    }
    static generateId() {
        return Math.random().toString(36).substr(2, 9);
    }
}
exports.SensitivityAnalysisService = SensitivityAnalysisService;
//# sourceMappingURL=sensitivityAnalysis.js.map