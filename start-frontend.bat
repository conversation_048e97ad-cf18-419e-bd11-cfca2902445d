@echo off
title Master Business Case Management - Frontend Launcher
color 0A

echo ========================================
echo  Master Business Case Management
echo  Frontend Application Launcher
echo ========================================
echo.

echo [INFO] Starting Frontend Application...
echo [INFO] This will open the React application on http://localhost:3000
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo [INFO] Please install Node.js from: https://nodejs.org/
    echo [INFO] Press any key to exit...
    pause >nul
    exit /b 1
)

echo [INFO] Node.js version:
node --version
echo.

REM Navigate to frontend directory
echo [INFO] Navigating to frontend directory...
cd /d "%~dp0apps\frontend"

if not exist "package.json" (
    echo [ERROR] Frontend directory not found or invalid
    echo [INFO] Make sure you're running this from the spm project root
    echo [INFO] Press any key to exit...
    pause >nul
    exit /b 1
)

REM Check if node_modules exists, if not install dependencies
if not exist "node_modules" (
    echo [INFO] Installing dependencies for the first time...
    echo [INFO] This may take 2-3 minutes...
    echo.
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install dependencies
        echo [INFO] Press any key to exit...
        pause >nul
        exit /b 1
    )
    echo.
    echo [SUCCESS] Dependencies installed successfully!
    echo.
)

echo [INFO] Starting React development server...
echo [INFO] The application will open automatically in your browser
echo [INFO] URL: http://localhost:3000
echo.
echo [INFO] To stop the application, press Ctrl+C in this window
echo.

REM Start the React application
npm start

REM If we reach here, the server has stopped
echo.
echo [INFO] Application has stopped
echo [INFO] Press any key to exit...
pause >nul
