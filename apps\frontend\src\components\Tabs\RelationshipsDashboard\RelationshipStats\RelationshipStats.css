.relationship-stats {
  margin-bottom: 24px;
}

/* Overview Stats */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
}

.stat-card.primary {
  border-left-color: #3b82f6;
}

.stat-card.secondary {
  border-left-color: #6b7280;
}

.stat-card.success {
  border-left-color: #10b981;
}

.stat-card.warning {
  border-left-color: #f59e0b;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 8px;
  font-size: 20px;
}

.stat-card.primary .stat-icon {
  background: #dbeafe;
  color: #1e40af;
}

.stat-card.secondary .stat-icon {
  background: #f3f4f6;
  color: #374151;
}

.stat-card.success .stat-icon {
  background: #d1fae5;
  color: #065f46;
}

.stat-card.warning .stat-icon {
  background: #fef3c7;
  color: #92400e;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

/* Detailed Breakdown */
.stats-breakdown {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.breakdown-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.breakdown-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

/* Entity Distribution */
.breakdown-grid {
  display: grid;
  gap: 12px;
}

.breakdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.breakdown-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: #e5e7eb;
  color: #374151;
  font-size: 14px;
}

.breakdown-content {
  flex: 1;
}

.breakdown-value {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.breakdown-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

/* Connection Types */
.connection-list {
  display: grid;
  gap: 12px;
}

.connection-item {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 8px;
  align-items: center;
}

.connection-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.connection-count {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  text-align: right;
  min-width: 30px;
}

.connection-bar {
  grid-column: 1 / -1;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 4px;
}

.connection-fill {
  height: 100%;
  background: #3b82f6;
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* Most Connected Entity */
.most-connected {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
}

.entity-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.entity-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #3b82f6;
  color: white;
  font-size: 16px;
}

.entity-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.entity-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.entity-type {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.connection-count-badge {
  padding: 4px 8px;
  background: #3b82f6;
  color: white;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* Health Indicators */
.health-indicators {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.health-indicators h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.health-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.health-item:last-child {
  border-bottom: none;
}

.health-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  font-size: 14px;
  flex-shrink: 0;
}

.health-icon.success {
  background: #d1fae5;
  color: #065f46;
}

.health-icon.warning {
  background: #fef3c7;
  color: #92400e;
}

.health-icon.info {
  background: #dbeafe;
  color: #1e40af;
}

.health-content {
  flex: 1;
}

.health-label {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.health-description {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .stats-breakdown {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .stat-card {
    padding: 16px;
    gap: 12px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  
  .stat-value {
    font-size: 20px;
  }
  
  .stats-breakdown {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .breakdown-section {
    padding: 16px;
  }
  
  .most-connected {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .entity-info {
    justify-content: center;
  }
  
  .connection-count-badge {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .stats-overview {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: 12px;
  }
  
  .breakdown-item {
    padding: 8px;
  }
  
  .health-item {
    padding: 8px 0;
  }
}
