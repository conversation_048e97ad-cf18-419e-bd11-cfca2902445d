import React, { useState } from 'react';

const RequestList = ({ 
  requests, 
  filters, 
  onFiltersChange, 
  onEditRequest, 
  onDeleteRequest, 
  onCreateNew, 
  loading 
}) => {
  const [sortBy, setSortBy] = useState('dateSubmitted');
  const [sortOrder, setSortOrder] = useState('desc');
  const [selectedRequests, setSelectedRequests] = useState([]);

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const handleSelectRequest = (requestId) => {
    setSelectedRequests(prev => 
      prev.includes(requestId)
        ? prev.filter(id => id !== requestId)
        : [...prev, requestId]
    );
  };

  const handleSelectAll = () => {
    if (selectedRequests.length === requests.length) {
      setSelectedRequests([]);
    } else {
      setSelectedRequests(requests.map(req => req.id));
    }
  };

  const getStatusBadge = (status) => {
    const badges = {
      'Open': { class: 'status-open', icon: 'fas fa-circle' },
      'In Review': { class: 'status-review', icon: 'fas fa-eye' },
      'Approved': { class: 'status-approved', icon: 'fas fa-check-circle' },
      'In Progress': { class: 'status-progress', icon: 'fas fa-spinner' },
      'Completed': { class: 'status-completed', icon: 'fas fa-check-circle' },
      'Rejected': { class: 'status-rejected', icon: 'fas fa-times-circle' },
      'On Hold': { class: 'status-hold', icon: 'fas fa-pause-circle' }
    };
    return badges[status] || badges.Open;
  };

  const getPriorityBadge = (priority) => {
    const badges = {
      'Critical': { class: 'priority-critical', icon: 'fas fa-exclamation-triangle' },
      'High': { class: 'priority-high', icon: 'fas fa-arrow-up' },
      'Medium': { class: 'priority-medium', icon: 'fas fa-minus' },
      'Low': { class: 'priority-low', icon: 'fas fa-arrow-down' }
    };
    return badges[priority] || badges.Medium;
  };

  const sortedRequests = [...requests].sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];

    // Handle date sorting
    if (sortBy === 'dateSubmitted') {
      aValue = new Date(aValue);
      bValue = new Date(bValue);
    }

    // Handle string sorting
    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  return (
    <div className="request-list">
      {/* Filters */}
      <div className="list-filters">
        <div className="filter-row">
          <div className="filter-group">
            <input
              type="text"
              placeholder="Search requests..."
              value={filters.search}
              onChange={(e) => onFiltersChange({ ...filters, search: e.target.value })}
              className="search-input"
            />
          </div>
          
          <div className="filter-group">
            <select
              value={filters.status}
              onChange={(e) => onFiltersChange({ ...filters, status: e.target.value })}
            >
              <option value="">All Statuses</option>
              <option value="Open">Open</option>
              <option value="In Review">In Review</option>
              <option value="Approved">Approved</option>
              <option value="In Progress">In Progress</option>
              <option value="Completed">Completed</option>
              <option value="Rejected">Rejected</option>
              <option value="On Hold">On Hold</option>
            </select>
          </div>
          
          <div className="filter-group">
            <select
              value={filters.priority}
              onChange={(e) => onFiltersChange({ ...filters, priority: e.target.value })}
            >
              <option value="">All Priorities</option>
              <option value="Critical">Critical</option>
              <option value="High">High</option>
              <option value="Medium">Medium</option>
              <option value="Low">Low</option>
            </select>
          </div>
          
          <div className="filter-group">
            <select
              value={filters.requestType}
              onChange={(e) => onFiltersChange({ ...filters, requestType: e.target.value })}
            >
              <option value="">All Types</option>
              <option value="Enhancement">Enhancement</option>
              <option value="Bug Fix">Bug Fix</option>
              <option value="New Feature">New Feature</option>
              <option value="Integration">Integration</option>
              <option value="Performance">Performance</option>
              <option value="Security">Security</option>
              <option value="Maintenance">Maintenance</option>
            </select>
          </div>
        </div>
      </div>

      {/* List Header */}
      <div className="list-header">
        <div className="header-info">
          <h3>Requests ({requests.length})</h3>
          {selectedRequests.length > 0 && (
            <span className="selected-count">{selectedRequests.length} selected</span>
          )}
        </div>
        <div className="header-actions">
          {selectedRequests.length > 0 && (
            <button className="bulk-action-btn">
              <i className="fas fa-edit"></i>
              Bulk Actions
            </button>
          )}
          <button className="create-btn" onClick={onCreateNew}>
            <i className="fas fa-plus"></i>
            New Request
          </button>
        </div>
      </div>

      {/* Requests Table */}
      <div className="requests-table-container">
        {loading ? (
          <div className="loading-state">
            <i className="fas fa-spinner fa-spin"></i>
            <p>Loading requests...</p>
          </div>
        ) : requests.length === 0 ? (
          <div className="empty-state">
            <i className="fas fa-clipboard-list"></i>
            <h4>No Requests Found</h4>
            <p>Create your first request to get started</p>
            <button className="create-btn" onClick={onCreateNew}>
              <i className="fas fa-plus"></i>
              Create Request
            </button>
          </div>
        ) : (
          <table className="requests-table">
            <thead>
              <tr>
                <th>
                  <input
                    type="checkbox"
                    checked={selectedRequests.length === requests.length}
                    onChange={handleSelectAll}
                  />
                </th>
                <th 
                  className={`sortable ${sortBy === 'requestId' ? sortOrder : ''}`}
                  onClick={() => handleSort('requestId')}
                >
                  Request ID
                  <i className="fas fa-sort"></i>
                </th>
                <th 
                  className={`sortable ${sortBy === 'title' ? sortOrder : ''}`}
                  onClick={() => handleSort('title')}
                >
                  Title
                  <i className="fas fa-sort"></i>
                </th>
                <th 
                  className={`sortable ${sortBy === 'requestType' ? sortOrder : ''}`}
                  onClick={() => handleSort('requestType')}
                >
                  Type
                  <i className="fas fa-sort"></i>
                </th>
                <th 
                  className={`sortable ${sortBy === 'priority' ? sortOrder : ''}`}
                  onClick={() => handleSort('priority')}
                >
                  Priority
                  <i className="fas fa-sort"></i>
                </th>
                <th 
                  className={`sortable ${sortBy === 'status' ? sortOrder : ''}`}
                  onClick={() => handleSort('status')}
                >
                  Status
                  <i className="fas fa-sort"></i>
                </th>
                <th 
                  className={`sortable ${sortBy === 'submittedBy' ? sortOrder : ''}`}
                  onClick={() => handleSort('submittedBy')}
                >
                  Submitted By
                  <i className="fas fa-sort"></i>
                </th>
                <th 
                  className={`sortable ${sortBy === 'dateSubmitted' ? sortOrder : ''}`}
                  onClick={() => handleSort('dateSubmitted')}
                >
                  Date Submitted
                  <i className="fas fa-sort"></i>
                </th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {sortedRequests.map(request => {
                const statusBadge = getStatusBadge(request.status);
                const priorityBadge = getPriorityBadge(request.priority);
                
                return (
                  <tr key={request.id} className={selectedRequests.includes(request.id) ? 'selected' : ''}>
                    <td>
                      <input
                        type="checkbox"
                        checked={selectedRequests.includes(request.id)}
                        onChange={() => handleSelectRequest(request.id)}
                      />
                    </td>
                    <td className="request-id">{request.requestId}</td>
                    <td className="request-title">
                      <div>
                        <span className="title">{request.title}</span>
                        <span className="description">{request.description.substring(0, 100)}...</span>
                      </div>
                    </td>
                    <td>
                      <span className="request-type">{request.requestType}</span>
                    </td>
                    <td>
                      <span className={`priority-badge ${priorityBadge.class}`}>
                        <i className={priorityBadge.icon}></i>
                        {request.priority}
                      </span>
                    </td>
                    <td>
                      <span className={`status-badge ${statusBadge.class}`}>
                        <i className={statusBadge.icon}></i>
                        {request.status}
                      </span>
                    </td>
                    <td>{request.submittedBy}</td>
                    <td>{new Date(request.dateSubmitted).toLocaleDateString()}</td>
                    <td>
                      <div className="action-buttons">
                        <button
                          className="action-btn edit"
                          onClick={() => onEditRequest(request)}
                          title="Edit Request"
                        >
                          <i className="fas fa-edit"></i>
                        </button>
                        <button
                          className="action-btn delete"
                          onClick={() => onDeleteRequest(request.id)}
                          title="Delete Request"
                        >
                          <i className="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
};

export default RequestList;
