import { apiGet, apiPost, apiPut, apiDelete } from './api';

class IdeaService {
  // Get all ideas with optional filtering and pagination
  async getAll(filters = {}) {
    const queryParams = new URLSearchParams();
    
    // Add filters to query params
    Object.keys(filters).forEach(key => {
      if (filters[key] !== undefined && filters[key] !== '') {
        queryParams.append(key, filters[key]);
      }
    });
    
    const queryString = queryParams.toString();
    const url = queryString ? `/api/ideas?${queryString}` : '/api/ideas';
    
    return await apiGet(url);
  }

  // Get a single idea by ID
  async getById(id) {
    return await apiGet(`/api/ideas/${id}`);
  }

  // Create a new idea
  async create(ideaData) {
    console.log('🌐 ideaService.create called with:', ideaData);
    const result = await apiPost('/api/ideas', ideaData);
    console.log('🌐 ideaService.create result:', result);
    return result;
  }

  // Update an existing idea
  async update(id, ideaData) {
    return await apiPut(`/api/ideas/${id}`, ideaData);
  }

  // Delete an idea
  async delete(id) {
    return await apiDelete(`/ideas/${id}`);
  }

  // Get ideas by status
  async getByStatus(status) {
    return await apiGet(`/ideas?status=${status}`);
  }

  // Get ideas by business unit
  async getByBusinessUnit(businessUnitId) {
    return await apiGet(`/ideas?businessUnitId=${businessUnitId}`);
  }

  // Get ideas by submitter
  async getBySubmitter(submitterId) {
    return await apiGet(`/ideas?submitterId=${submitterId}`);
  }

  // Search ideas
  async search(searchTerm) {
    return await apiGet(`/ideas/search?q=${encodeURIComponent(searchTerm)}`);
  }

  // Promote idea to business case
  async promoteToBusinessCase(ideaId, businessCaseData) {
    return await apiPost(`/ideas/${ideaId}/promote`, businessCaseData);
  }

  // Link idea to project/program
  async linkToEntity(ideaId, linkData) {
    return await apiPost(`/ideas/${ideaId}/link`, linkData);
  }

  // Get idea statistics
  async getStatistics() {
    return await apiGet('/ideas/statistics');
  }

  // Bulk operations
  async bulkCreate(ideasData) {
    return await apiPost('/ideas/bulk', { ideas: ideasData });
  }

  async bulkUpdate(updates) {
    return await apiPut('/ideas/bulk', { updates });
  }

  async bulkDelete(ids) {
    return await apiDelete('/ideas/bulk', { ids });
  }

  // Get idea categories/tags
  async getCategories() {
    return await apiGet('/ideas/categories');
  }

  // Get business units for dropdown
  async getBusinessUnits() {
    return await apiGet('/business-units');
  }

  // Export ideas
  async exportIdeas(format = 'excel', filters = {}) {
    const queryParams = new URLSearchParams({ format, ...filters });
    return await apiGet(`/ideas/export?${queryParams.toString()}`);
  }

  // Get idea templates
  async getTemplates() {
    return await apiGet('/ideas/templates');
  }

  // Validate idea data
  validateIdeaData(ideaData) {
    const errors = {};
    
    // Required fields validation
    if (!ideaData.title || ideaData.title.trim() === '') {
      errors.title = 'Idea title is required';
    }
    
    if (!ideaData.submitterName || ideaData.submitterName.trim() === '') {
      errors.submitterName = 'Submitter name is required';
    }
    
    if (!ideaData.businessUnitId) {
      errors.businessUnitId = 'Business unit is required';
    }
    
    if (!ideaData.problemStatement || ideaData.problemStatement.trim() === '') {
      errors.problemStatement = 'Problem statement is required';
    }
    
    if (!ideaData.opportunityDescription || ideaData.opportunityDescription.trim() === '') {
      errors.opportunityDescription = 'Opportunity description is required';
    }

    // Optional field validations
    if (ideaData.estimatedCost && isNaN(parseFloat(ideaData.estimatedCost))) {
      errors.estimatedCost = 'Estimated cost must be a valid number';
    }
    
    if (ideaData.expectedBenefit && isNaN(parseFloat(ideaData.expectedBenefit))) {
      errors.expectedBenefit = 'Expected benefit must be a valid number';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  // Format idea data for display
  formatIdeaForDisplay(idea) {
    return {
      ...idea,
      formattedSubmissionDate: new Date(idea.submissionDate).toLocaleDateString(),
      formattedEstimatedCost: idea.estimatedCost ? 
        new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(idea.estimatedCost) : 
        'Not specified',
      formattedExpectedBenefit: idea.expectedBenefit ? 
        new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(idea.expectedBenefit) : 
        'Not specified',
      statusBadgeColor: this.getStatusBadgeColor(idea.status),
      priorityBadgeColor: this.getPriorityBadgeColor(idea.priority)
    };
  }

  // Get status badge color
  getStatusBadgeColor(status) {
    const colors = {
      'submitted': 'bg-blue-100 text-blue-800',
      'under-review': 'bg-yellow-100 text-yellow-800',
      'approved': 'bg-green-100 text-green-800',
      'rejected': 'bg-red-100 text-red-800',
      'on-hold': 'bg-gray-100 text-gray-800',
      'implemented': 'bg-purple-100 text-purple-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  }

  // Get priority badge color
  getPriorityBadgeColor(priority) {
    const colors = {
      'low': 'bg-green-100 text-green-800',
      'medium': 'bg-yellow-100 text-yellow-800',
      'high': 'bg-orange-100 text-orange-800',
      'critical': 'bg-red-100 text-red-800'
    };
    return colors[priority] || 'bg-gray-100 text-gray-800';
  }
}

// Create and export a singleton instance
export const ideaService = new IdeaService();
export default ideaService;
