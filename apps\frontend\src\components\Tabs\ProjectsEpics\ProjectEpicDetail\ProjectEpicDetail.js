import React from 'react';
import './ProjectEpicDetail.css';

const ProjectEpicDetail = ({ project, businessCases, programs, onEdit, onBack }) => {
  if (!project) return null;

  const getStatusColor = (status) => {
    const colors = {
      planning: '#f59e0b',
      active: '#10b981',
      'on-hold': '#6b7280',
      completed: '#3b82f6',
      cancelled: '#dc2626'
    };
    return colors[status] || '#6b7280';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      low: '#10b981',
      medium: '#f59e0b',
      high: '#f97316',
      critical: '#dc2626'
    };
    return colors[priority] || '#6b7280';
  };

  const getMilestoneStatusColor = (status) => {
    const colors = {
      pending: '#6b7280',
      'in-progress': '#f59e0b',
      completed: '#10b981',
      delayed: '#dc2626'
    };
    return colors[status] || '#6b7280';
  };

  const getLinkedBusinessCases = () => {
    if (!project.linkedBusinessCases || project.linkedBusinessCases.length === 0) {
      return [];
    }
    
    return project.linkedBusinessCases
      .map(bcId => businessCases.find(bc => bc.id === bcId))
      .filter(bc => bc); // Remove any undefined entries
  };

  const getAssociatedProgram = () => {
    if (!project.associatedProgram) return null;
    return programs.find(p => p.id === project.associatedProgram);
  };

  const linkedBCs = getLinkedBusinessCases();
  const associatedProgram = getAssociatedProgram();

  return (
    <div className="project-epic-detail">
      {/* Header */}
      <div className="detail-header">
        <div className="header-content">
          <div className="project-type">
            <i className={project.type === 'epic' ? 'fas fa-layer-group' : 'fas fa-project-diagram'}></i>
            {project.type === 'epic' ? 'Epic' : 'Project'}
          </div>
          <h2>{project.name}</h2>
          {project.description && (
            <p className="project-description">{project.description}</p>
          )}
        </div>
        <div className="header-actions">
          <button onClick={onEdit} className="btn btn-primary">
            <i className="fas fa-edit"></i>
            Edit
          </button>
          <button onClick={onBack} className="btn btn-secondary">
            <i className="fas fa-arrow-left"></i>
            Back to List
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="detail-content">
        {/* Basic Information */}
        <div className="detail-section">
          <h3>Basic Information</h3>
          <div className="info-grid">
            <div className="info-item">
              <label>Status:</label>
              <span 
                className="status-badge"
                style={{ backgroundColor: getStatusColor(project.status) }}
              >
                {project.status}
              </span>
            </div>
            
            <div className="info-item">
              <label>Priority:</label>
              <span 
                className="priority-badge"
                style={{ backgroundColor: getPriorityColor(project.priority) }}
              >
                {project.priority}
              </span>
            </div>

            {project.owner && (
              <div className="info-item">
                <label>Owner/Manager:</label>
                <span>{project.owner}</span>
              </div>
            )}

            {project.startDate && (
              <div className="info-item">
                <label>Start Date:</label>
                <span>{new Date(project.startDate).toLocaleDateString()}</span>
              </div>
            )}

            {project.endDate && (
              <div className="info-item">
                <label>End Date:</label>
                <span>{new Date(project.endDate).toLocaleDateString()}</span>
              </div>
            )}

            <div className="info-item">
              <label>Created:</label>
              <span>{new Date(project.createdAt || Date.now()).toLocaleDateString()}</span>
            </div>
          </div>
        </div>

        {/* Program Association */}
        {associatedProgram && (
          <div className="detail-section">
            <h3>Associated Program</h3>
            <div className="program-card">
              <div className="program-icon">
                <i className="fas fa-layer-group"></i>
              </div>
              <div className="program-info">
                <div className="program-name">{associatedProgram.name}</div>
                <div className="program-description">{associatedProgram.description}</div>
                <div className="program-details">
                  <span className="program-owner">Owner: {associatedProgram.owner}</span>
                  <span className="program-status">Status: {associatedProgram.status}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Linked Business Cases */}
        <div className="detail-section">
          <h3>Linked Business Cases ({linkedBCs.length})</h3>
          {linkedBCs.length > 0 ? (
            <div className="business-cases-grid">
              {linkedBCs.map(bc => (
                <div key={bc.id} className="business-case-card">
                  <div className="bc-header">
                    <div className="bc-icon">
                      <i className="fas fa-briefcase"></i>
                    </div>
                    <div className="bc-info">
                      <div className="bc-name">{bc.name}</div>
                      <div className="bc-unit">{bc.businessUnit}</div>
                    </div>
                    <div className="bc-status">
                      <span className={`status-indicator status-${bc.status}`}>
                        {bc.status}
                      </span>
                    </div>
                  </div>
                  {bc.description && (
                    <div className="bc-description">{bc.description}</div>
                  )}
                  {bc.calculatedMetrics && (
                    <div className="bc-metrics">
                      <div className="metric">
                        <span className="metric-label">IRR:</span>
                        <span className="metric-value">{bc.calculatedMetrics.irr}%</span>
                      </div>
                      <div className="metric">
                        <span className="metric-label">NPV:</span>
                        <span className="metric-value">${bc.calculatedMetrics.npv?.toLocaleString() || 'N/A'}</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="empty-section">
              <i className="fas fa-briefcase"></i>
              <p>No business cases linked to this {project.type}</p>
            </div>
          )}
        </div>

        {/* Milestones */}
        <div className="detail-section">
          <h3>Milestones ({project.milestones?.length || 0})</h3>
          {project.milestones && project.milestones.length > 0 ? (
            <div className="milestones-timeline">
              {project.milestones.map(milestone => (
                <div key={milestone.id} className="milestone-item">
                  <div className="milestone-indicator">
                    <div 
                      className="milestone-dot"
                      style={{ backgroundColor: getMilestoneStatusColor(milestone.status) }}
                    ></div>
                    <div className="milestone-line"></div>
                  </div>
                  <div className="milestone-content">
                    <div className="milestone-header">
                      <h4>{milestone.name}</h4>
                      <span 
                        className="milestone-status"
                        style={{ backgroundColor: getMilestoneStatusColor(milestone.status) }}
                      >
                        {milestone.status}
                      </span>
                    </div>
                    {milestone.description && (
                      <p className="milestone-description">{milestone.description}</p>
                    )}
                    {milestone.dueDate && (
                      <div className="milestone-date">
                        <i className="fas fa-calendar"></i>
                        Due: {new Date(milestone.dueDate).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="empty-section">
              <i className="fas fa-flag"></i>
              <p>No milestones defined for this {project.type}</p>
            </div>
          )}
        </div>

        {/* Project Timeline */}
        {project.startDate && project.endDate && (
          <div className="detail-section">
            <h3>Project Timeline</h3>
            <div className="timeline-visual">
              <div className="timeline-bar">
                <div className="timeline-progress" style={{ width: '45%' }}></div>
              </div>
              <div className="timeline-dates">
                <span className="start-date">
                  Start: {new Date(project.startDate).toLocaleDateString()}
                </span>
                <span className="end-date">
                  End: {new Date(project.endDate).toLocaleDateString()}
                </span>
              </div>
              <div className="timeline-duration">
                Duration: {Math.ceil((new Date(project.endDate) - new Date(project.startDate)) / (1000 * 60 * 60 * 24))} days
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectEpicDetail;
