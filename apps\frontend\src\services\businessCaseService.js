import { apiGet, apiPost, apiPut, apiDelete, downloadFile } from './api';

class BusinessCaseService {
  // Get all business cases
  async getAll() {
    return await apiGet('/api/business-cases');
  }

  // Get business case by ID
  async getById(id) {
    return await apiGet(`/api/business-cases/${id}`);
  }

  // Create new business case
  async create(businessCaseData) {
    return await apiPost('/api/business-cases', businessCaseData);
  }

  // Update business case
  async update(id, businessCaseData) {
    return await apiPut(`/api/business-cases/${id}`, businessCaseData);
  }

  // Delete business case
  async delete(id) {
    return await apiDelete(`/api/business-cases/${id}`);
  }

  // Get business case statistics
  async getStats() {
    return await apiGet('/api/business-cases/stats');
  }

  // Export business case to Excel
  async exportToExcel(id, format = 'excel') {
    return await downloadFile(`/business-cases/${id}/export?format=${format}`, `business-case-${id}.xlsx`);
  }

  // Export all business cases
  async exportAll(format = 'excel') {
    return await downloadFile(`/business-cases/export-all?format=${format}`, `all-business-cases.xlsx`);
  }

  // Bulk export selected business cases
  async bulkExport(ids, format = 'excel') {
    const idsParam = ids.join(',');
    return await downloadFile(`/business-cases/bulk-export?ids=${idsParam}&format=${format}`, `business-cases-bulk.xlsx`);
  }

  // Bulk delete business cases
  async bulkDelete(ids) {
    return await apiDelete('/business-cases/bulk-delete', { data: { ids } });
  }

  // Export business case
  async exportBusinessCase(id) {
    return await downloadFile(`/api/business-cases/${id}/export`, `business-case-${id}.xlsx`);
  }

  // Download template
  async downloadTemplate() {
    return await downloadFile('/api/template/business-cases', 'business-case-template.xlsx');
  }

  // Search business cases
  async search(query, filters = {}) {
    const params = new URLSearchParams({
      q: query,
      ...filters
    });
    return await apiGet(`/api/business-cases/search?${params}`);
  }

  // Get business cases by business unit
  async getByBusinessUnit(businessUnit) {
    return await apiGet(`/api/business-cases/by-business-unit/${businessUnit}`);
  }

  // Get business cases by status
  async getByStatus(status) {
    return await apiGet(`/api/business-cases/by-status/${status}`);
  }

  // Get business cases by date range
  async getByDateRange(startDate, endDate) {
    const params = new URLSearchParams({
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    });
    return await apiGet(`/api/business-cases/by-date-range?${params}`);
  }

  // Calculate financial metrics
  async calculateMetrics(financialData) {
    return await apiPost('/api/business-cases/calculate-metrics', financialData);
  }

  // Master Business Cases
  async getMasterBusinessCases() {
    return await apiGet('/api/master-bc');
  }

  async getMasterBusinessCaseById(id) {
    return await apiGet(`/api/master-bc/${id}`);
  }

  async createMasterBusinessCase(masterBCData) {
    return await apiPost('/api/master-bc', masterBCData);
  }

  async updateMasterBusinessCase(id, masterBCData) {
    return await apiPut(`/api/master-bc/${id}`, masterBCData);
  }

  async deleteMasterBusinessCase(id) {
    return await apiDelete(`/api/master-bc/${id}`);
  }

  // Link business cases to master business case
  async linkBusinessCases(masterBCId, businessCaseIds) {
    return await apiPut(`/api/master-bc/${masterBCId}/link`, { businessCaseIds });
  }

  // Unlink business cases from master business case
  async unlinkBusinessCases(masterBCId, businessCaseIds) {
    return await apiPut(`/api/master-bc/${masterBCId}/unlink`, { businessCaseIds });
  }

  // Get aggregated metrics for master business case
  async getMasterBCAggregatedMetrics(masterBCId) {
    return await apiGet(`/api/master-bc/${masterBCId}/metrics`);
  }

  // Export master business case
  async exportMasterBusinessCase(id, format = 'excel') {
    return await downloadFile(`/master-bc/${id}/export?format=${format}`, `master-business-case-${id}.xlsx`);
  }

  // Validate business case data
  validateBusinessCase(businessCaseData) {
    const errors = [];

    // Required fields validation
    if (!businessCaseData.name || businessCaseData.name.trim() === '') {
      errors.push('Business case name is required');
    }

    if (!businessCaseData.timeframe || !businessCaseData.timeframe.startYear || !businessCaseData.timeframe.endYear) {
      errors.push('Timeframe (start and end year) is required');
    }

    if (businessCaseData.timeframe && businessCaseData.timeframe.startYear >= businessCaseData.timeframe.endYear) {
      errors.push('End year must be after start year');
    }

    // Financial data validation
    if (!businessCaseData.financialData) {
      errors.push('Financial data is required');
    } else {
      if (!businessCaseData.financialData.capex || businessCaseData.financialData.capex.length === 0) {
        errors.push('At least one CAPEX item is required');
      }

      if (!businessCaseData.financialData.opex || businessCaseData.financialData.opex.length === 0) {
        errors.push('At least one OPEX item is required');
      }

      // Validate financial items
      ['capex', 'opex', 'revenue'].forEach(type => {
        if (businessCaseData.financialData[type]) {
          businessCaseData.financialData[type].forEach((item, index) => {
            if (!item.year || item.year < 2020 || item.year > 2050) {
              errors.push(`${type.toUpperCase()} item ${index + 1}: Invalid year`);
            }
            if (!item.amount || item.amount <= 0) {
              errors.push(`${type.toUpperCase()} item ${index + 1}: Amount must be greater than 0`);
            }
          });
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Format currency for display
  formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  // Calculate total CAPEX
  calculateTotalCapex(capexItems) {
    return capexItems.reduce((total, item) => total + (item.amount || 0), 0);
  }

  // Calculate total OPEX
  calculateTotalOpex(opexItems) {
    return opexItems.reduce((total, item) => total + (item.amount || 0), 0);
  }

  // Calculate total revenue
  calculateTotalRevenue(revenueItems) {
    return revenueItems.reduce((total, item) => total + (item.amount || 0), 0);
  }

  // Get status color class
  getStatusColor(status) {
    switch (status) {
      case 'active': return 'status-active';
      case 'draft': return 'status-draft';
      case 'completed': return 'status-completed';
      case 'archived': return 'status-archived';
      default: return 'status-draft';
    }
  }
}

// Create and export a singleton instance
export const businessCaseService = new BusinessCaseService();
export default businessCaseService;
