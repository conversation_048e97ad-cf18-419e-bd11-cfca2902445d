.portfolio-overview {
  padding: 24px;
  background: #f8fafc;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: transform 0.2s;
}

.metric-card:hover {
  transform: translateY(-2px);
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.metric-card.primary .metric-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.metric-card.success .metric-icon {
  background: linear-gradient(135deg, #10b981, #047857);
}

.metric-card.info .metric-icon {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.metric-card.warning .metric-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

/* Portfolio Health */
.portfolio-health {
  margin-bottom: 32px;
}

.health-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.health-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.health-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.health-header h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.health-header i {
  font-size: 1.25rem;
  color: #64748b;
}

.health-stats {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-value.profitable { color: #10b981; }
.stat-value.total { color: #3b82f6; }
.stat-value.percentage { color: #8b5cf6; }
.stat-value.high-risk { color: #ef4444; }
.stat-value.medium-risk { color: #f59e0b; }
.stat-value.low-risk { color: #10b981; }
.stat-value.linked { color: #10b981; }
.stat-value.unlinked { color: #f59e0b; }

.stat-label {
  font-size: 0.8rem;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Performers Section */
.performers-section {
  margin-bottom: 32px;
}

.performers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.performers-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.performers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.performers-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.view-all-btn {
  padding: 6px 12px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

.view-all-btn:hover {
  background: #2563eb;
}

.performers-list {
  padding: 16px 0;
}

.performer-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 24px;
  transition: background 0.2s;
}

.performer-item:hover {
  background: #f8fafc;
}

.performer-item.attention {
  border-left: 4px solid #ef4444;
}

.performer-rank {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.performer-rank.warning {
  background: #ef4444;
}

.performer-info {
  flex: 1;
}

.performer-name {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.performer-category {
  font-size: 0.8rem;
  color: #64748b;
}

.performer-metrics {
  text-align: right;
}

.metric-npv {
  font-weight: 600;
  color: #10b981;
  margin-bottom: 4px;
}

.metric-npv.negative {
  color: #ef4444;
}

.metric-irr {
  font-size: 0.8rem;
  color: #64748b;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #64748b;
}

.empty-state i {
  font-size: 2rem;
  margin-bottom: 12px;
  color: #cbd5e1;
}

.empty-state p {
  margin: 0;
  font-size: 0.9rem;
}

/* Quick Actions */
.quick-actions {
  margin-bottom: 24px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 24px;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  color: #64748b;
}

.action-btn:hover {
  border-color: #3b82f6;
  background: #f8fafc;
  color: #3b82f6;
  transform: translateY(-2px);
}

.action-btn i {
  font-size: 1.5rem;
}

.action-btn span {
  font-weight: 500;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .portfolio-overview {
    padding: 16px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .health-grid {
    grid-template-columns: 1fr;
  }
  
  .performers-grid {
    grid-template-columns: 1fr;
  }
  
  .health-stats {
    flex-direction: column;
    gap: 12px;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
}
