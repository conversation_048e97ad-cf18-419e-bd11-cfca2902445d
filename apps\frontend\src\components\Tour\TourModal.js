import React from 'react';
import PropTypes from 'prop-types';
import Modal from '../Modals/Modal';

const TourModal = ({ isOpen, onClose }) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Guided Tour" size="md">
      <div className="text-center py-8">
        <i className="fas fa-route text-6xl text-blue-500 mb-6"></i>
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Guided Tour</h3>
        <p className="text-gray-600 mb-6">
          The guided tour feature is currently disabled as per user preferences for a cleaner interface experience.
        </p>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-blue-800">
            <i className="fas fa-info-circle mr-2"></i>
            Tour functionality can be re-enabled if needed in the future
          </p>
        </div>
      </div>
    </Modal>
  );
};

TourModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default TourModal;
