import React, { useState, useEffect } from 'react';
import { useData } from '../../context/DataContext';

const DataValidationReport = () => {
  const {
    businessCases,
    masterBusinessCases,
    projects,
    epics,
    programs,
    ideas,
    dashboardData,
    loading,
    errors
  } = useData();

  const [validationResults, setValidationResults] = useState({});
  const [apiStatus, setApiStatus] = useState({});

  useEffect(() => {
    performValidation();
    checkApiEndpoints();
  }, [businessCases, masterBusinessCases, projects, epics, programs, ideas, dashboardData]);

  const performValidation = () => {
    const results = {
      businessCases: validateBusinessCases(),
      masterBusinessCases: validateMasterBusinessCases(),
      projects: validateProjects(),
      epics: validateEpics(),
      programs: validatePrograms(),
      ideas: validateIdeas(),
      dashboardData: validateDashboardData(),
      relationships: validateRelationships()
    };

    setValidationResults(results);
  };

  const validateBusinessCases = () => {
    if (!businessCases || businessCases.length === 0) {
      return { status: 'error', message: 'No business cases found', count: 0 };
    }

    const issues = [];
    businessCases.forEach((bc, index) => {
      if (!bc.id) issues.push(`Business Case ${index + 1}: Missing ID`);
      if (!bc.name) issues.push(`Business Case ${index + 1}: Missing name`);
      if (!bc.businessUnit) issues.push(`Business Case ${index + 1}: Missing business unit`);
    });

    return {
      status: issues.length > 0 ? 'warning' : 'success',
      message: issues.length > 0 ? `${issues.length} validation issues` : 'All business cases valid',
      count: businessCases.length,
      issues
    };
  };

  const validateMasterBusinessCases = () => {
    if (!masterBusinessCases || masterBusinessCases.length === 0) {
      return { status: 'error', message: 'No master business cases found', count: 0 };
    }

    const issues = [];
    masterBusinessCases.forEach((mbc, index) => {
      if (!mbc.id) issues.push(`Master BC ${index + 1}: Missing ID`);
      if (!mbc.name) issues.push(`Master BC ${index + 1}: Missing name`);
      if (!mbc.linkedBusinessCases || mbc.linkedBusinessCases.length === 0) {
        issues.push(`Master BC ${index + 1}: No linked business cases`);
      }
    });

    return {
      status: issues.length > 0 ? 'warning' : 'success',
      message: issues.length > 0 ? `${issues.length} validation issues` : 'All master business cases valid',
      count: masterBusinessCases.length,
      issues
    };
  };

  const validateProjects = () => {
    if (!projects || projects.length === 0) {
      return { status: 'error', message: 'No projects found', count: 0 };
    }

    const issues = [];
    projects.forEach((project, index) => {
      if (!project.id) issues.push(`Project ${index + 1}: Missing ID`);
      if (!project.name) issues.push(`Project ${index + 1}: Missing name`);
      if (!project.businessUnit) issues.push(`Project ${index + 1}: Missing business unit`);
    });

    return {
      status: issues.length > 0 ? 'warning' : 'success',
      message: issues.length > 0 ? `${issues.length} validation issues` : 'All projects valid',
      count: projects.length,
      issues
    };
  };

  const validateEpics = () => {
    if (!epics || epics.length === 0) {
      return { status: 'error', message: 'No epics found', count: 0 };
    }

    const issues = [];
    epics.forEach((epic, index) => {
      if (!epic.id) issues.push(`Epic ${index + 1}: Missing ID`);
      if (!epic.name) issues.push(`Epic ${index + 1}: Missing name`);
      if (!epic.businessUnit) issues.push(`Epic ${index + 1}: Missing business unit`);
    });

    return {
      status: issues.length > 0 ? 'warning' : 'success',
      message: issues.length > 0 ? `${issues.length} validation issues` : 'All epics valid',
      count: epics.length,
      issues
    };
  };

  const validatePrograms = () => {
    if (!programs || programs.length === 0) {
      return { status: 'error', message: 'No programs found', count: 0 };
    }

    const issues = [];
    programs.forEach((program, index) => {
      if (!program.id) issues.push(`Program ${index + 1}: Missing ID`);
      if (!program.name) issues.push(`Program ${index + 1}: Missing name`);
      if (!program.businessUnit) issues.push(`Program ${index + 1}: Missing business unit`);
    });

    return {
      status: issues.length > 0 ? 'warning' : 'success',
      message: issues.length > 0 ? `${issues.length} validation issues` : 'All programs valid',
      count: programs.length,
      issues
    };
  };

  const validateIdeas = () => {
    if (!ideas || ideas.length === 0) {
      return { status: 'error', message: 'No ideas found', count: 0 };
    }

    const issues = [];
    ideas.forEach((idea, index) => {
      if (!idea.id) issues.push(`Idea ${index + 1}: Missing ID`);
      if (!idea.title) issues.push(`Idea ${index + 1}: Missing title`);
      if (!idea.businessUnit) issues.push(`Idea ${index + 1}: Missing business unit`);
    });

    return {
      status: issues.length > 0 ? 'warning' : 'success',
      message: issues.length > 0 ? `${issues.length} validation issues` : 'All ideas valid',
      count: ideas.length,
      issues
    };
  };

  const validateDashboardData = () => {
    if (!dashboardData) {
      return { status: 'error', message: 'No dashboard data found' };
    }

    const issues = [];
    if (!dashboardData.kpis || Object.keys(dashboardData.kpis).length === 0) {
      issues.push('Missing KPI data');
    }
    if (!dashboardData.charts) {
      issues.push('Missing charts data');
    }
    if (!dashboardData.recentActivity || dashboardData.recentActivity.length === 0) {
      issues.push('Missing recent activity data');
    }

    return {
      status: issues.length > 0 ? 'warning' : 'success',
      message: issues.length > 0 ? `${issues.length} validation issues` : 'Dashboard data valid',
      issues
    };
  };

  const validateRelationships = () => {
    const issues = [];
    
    // Check if master business cases have linked business cases
    if (masterBusinessCases && businessCases) {
      masterBusinessCases.forEach(mbc => {
        if (mbc.linkedBusinessCases) {
          mbc.linkedBusinessCases.forEach(bcId => {
            const linkedBC = businessCases.find(bc => bc.id === bcId);
            if (!linkedBC) {
              issues.push(`Master BC "${mbc.name}" links to non-existent Business Case: ${bcId}`);
            }
          });
        }
      });
    }

    // Check if programs have linked master business cases
    if (programs && masterBusinessCases) {
      programs.forEach(program => {
        if (program.linkedMasterBusinessCase) {
          const linkedMBC = masterBusinessCases.find(mbc => mbc.id === program.linkedMasterBusinessCase);
          if (!linkedMBC) {
            issues.push(`Program "${program.name}" links to non-existent Master BC: ${program.linkedMasterBusinessCase}`);
          }
        }
      });
    }

    return {
      status: issues.length > 0 ? 'warning' : 'success',
      message: issues.length > 0 ? `${issues.length} relationship issues` : 'All relationships valid',
      issues
    };
  };

  const checkApiEndpoints = async () => {
    const endpoints = [
      { name: 'Health', url: '/health' },
      { name: 'Dashboard', url: '/api/dashboard' },
      { name: 'Business Cases', url: '/api/business-cases' },
      { name: 'Master Business Cases', url: '/api/master-bc' },
      { name: 'Projects', url: '/api/projects' },
      { name: 'Programs', url: '/api/programs' },
      { name: 'Ideas', url: '/api/ideas' }
    ];

    const status = {};
    
    for (const endpoint of endpoints) {
      try {
        const response = await fetch(`http://localhost:5000${endpoint.url}`);
        const data = await response.json();
        status[endpoint.name] = {
          status: response.ok ? 'success' : 'error',
          message: response.ok ? 'Working' : `HTTP ${response.status}`,
          data: data
        };
      } catch (error) {
        status[endpoint.name] = {
          status: 'error',
          message: error.message,
          data: null
        };
      }
    }

    setApiStatus(status);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      default: return '❓';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            📊 Comprehensive Data Validation Report
          </h1>
          <p className="text-gray-600">
            Detailed analysis of all data sources and their integrity
          </p>
        </div>

        {/* Data Validation Results */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {Object.entries(validationResults).map(([key, result]) => (
            <div key={key} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 capitalize">
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </h3>
                <span className="text-2xl">{getStatusIcon(result.status)}</span>
              </div>
              
              <div className={`text-sm ${getStatusColor(result.status)} mb-2`}>
                {result.message}
              </div>
              
              {result.count !== undefined && (
                <div className="text-sm text-gray-600 mb-2">
                  Count: {result.count}
                </div>
              )}
              
              {result.issues && result.issues.length > 0 && (
                <div className="mt-3">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Issues:</h4>
                  <ul className="text-xs text-gray-600 space-y-1">
                    {result.issues.slice(0, 5).map((issue, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-red-500 mr-2">•</span>
                        {issue}
                      </li>
                    ))}
                    {result.issues.length > 5 && (
                      <li className="text-gray-500 italic">
                        ... and {result.issues.length - 5} more issues
                      </li>
                    )}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* API Status */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            🌐 API Endpoint Status
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(apiStatus).map(([name, status]) => (
              <div key={name} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-gray-900">{name}</h3>
                  <span className="text-xl">{getStatusIcon(status.status)}</span>
                </div>
                <div className={`text-sm ${getStatusColor(status.status)}`}>
                  {status.message}
                </div>
                {status.data && status.status === 'success' && (
                  <div className="text-xs text-gray-500 mt-2">
                    Response: {JSON.stringify(status.data).substring(0, 50)}...
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataValidationReport;
