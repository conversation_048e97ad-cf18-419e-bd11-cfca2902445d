#!/bin/bash

# MasterBC - Project Setup Script
# 
# Author: <PERSON><PERSON><PERSON>
# Repository: https://github.com/mahegyaneshpandey/spm
# Date: January 27, 2025

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Node.js version
check_node_version() {
    if command_exists node; then
        NODE_VERSION=$(node --version | cut -d'v' -f2)
        REQUIRED_VERSION="16.0.0"
        
        if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" = "$REQUIRED_VERSION" ]; then
            print_success "Node.js version $NODE_VERSION is compatible"
            return 0
        else
            print_error "Node.js version $NODE_VERSION is not compatible. Required: $REQUIRED_VERSION or higher"
            return 1
        fi
    else
        print_error "Node.js is not installed"
        return 1
    fi
}

# Function to check npm version
check_npm_version() {
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        print_success "npm version $NPM_VERSION found"
        return 0
    else
        print_error "npm is not installed"
        return 1
    fi
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing project dependencies..."
    
    # Install root dependencies
    print_status "Installing root workspace dependencies..."
    npm install
    
    # Install frontend dependencies
    print_status "Installing frontend dependencies..."
    cd apps/frontend
    npm install
    cd ../..
    
    # Install backend dependencies
    print_status "Installing backend dependencies..."
    cd apps/backend
    npm install
    cd ../..
    
    print_success "All dependencies installed successfully"
}

# Function to setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # Frontend environment
    if [ ! -f "apps/frontend/.env" ]; then
        print_status "Creating frontend .env file..."
        cat > apps/frontend/.env << EOF
# MasterBC Frontend Environment
# Author: Gyanesh K Pandey

REACT_APP_API_URL=http://localhost:5000
REACT_APP_APP_NAME=MasterBC
REACT_APP_VERSION=1.0.0
REACT_APP_AUTHOR=Gyanesh K Pandey
REACT_APP_REPOSITORY=https://github.com/mahegyaneshpandey/spm

# Development settings
REACT_APP_DEBUG=true
REACT_APP_LOG_LEVEL=debug

# API settings
REACT_APP_API_TIMEOUT=30000
REACT_APP_API_RETRY_ATTEMPTS=3
EOF
        print_success "Frontend .env file created"
    else
        print_warning "Frontend .env file already exists"
    fi
    
    # Backend environment
    if [ ! -f "apps/backend/.env" ]; then
        print_status "Creating backend .env file..."
        cat > apps/backend/.env << EOF
# MasterBC Backend Environment
# Author: Gyanesh K Pandey

PORT=5000
NODE_ENV=development

# Database settings (for future MongoDB integration)
DB_HOST=localhost
DB_PORT=27017
DB_NAME=masterbc
DB_USER=
DB_PASSWORD=

# JWT settings
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h

# File storage
DATA_DIR=./data
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760

# Logging
LOG_LEVEL=debug
LOG_FILE=./logs/app.log

# CORS settings
CORS_ORIGIN=http://localhost:3000
EOF
        print_success "Backend .env file created"
    else
        print_warning "Backend .env file already exists"
    fi
}

# Function to create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    # Backend directories
    mkdir -p apps/backend/logs
    mkdir -p apps/backend/uploads
    mkdir -p apps/backend/temp
    
    # Frontend directories
    mkdir -p apps/frontend/public/assets
    mkdir -p apps/frontend/src/assets
    
    print_success "Directories created successfully"
}

# Function to setup Git hooks
setup_git_hooks() {
    print_status "Setting up Git hooks..."
    
    if [ -d ".git" ]; then
        # Pre-commit hook
        cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# MasterBC Pre-commit Hook
# Author: Gyanesh K Pandey

echo "Running pre-commit checks..."

# Run linting
npm run lint
if [ $? -ne 0 ]; then
    echo "Linting failed. Please fix the issues before committing."
    exit 1
fi

# Run tests
npm test -- --watchAll=false
if [ $? -ne 0 ]; then
    echo "Tests failed. Please fix the issues before committing."
    exit 1
fi

echo "Pre-commit checks passed!"
EOF
        chmod +x .git/hooks/pre-commit
        print_success "Git hooks setup successfully"
    else
        print_warning "Not a Git repository. Skipping Git hooks setup."
    fi
}

# Function to verify installation
verify_installation() {
    print_status "Verifying installation..."
    
    # Check if all package.json files exist
    if [ -f "package.json" ] && [ -f "apps/frontend/package.json" ] && [ -f "apps/backend/package.json" ]; then
        print_success "All package.json files found"
    else
        print_error "Missing package.json files"
        return 1
    fi
    
    # Check if node_modules exist
    if [ -d "node_modules" ] && [ -d "apps/frontend/node_modules" ] && [ -d "apps/backend/node_modules" ]; then
        print_success "All dependencies installed"
    else
        print_error "Missing node_modules directories"
        return 1
    fi
    
    # Check if environment files exist
    if [ -f "apps/frontend/.env" ] && [ -f "apps/backend/.env" ]; then
        print_success "Environment files configured"
    else
        print_error "Missing environment files"
        return 1
    fi
    
    print_success "Installation verification completed"
}

# Function to display next steps
show_next_steps() {
    echo ""
    echo "🎉 MasterBC setup completed successfully!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Start the development servers:"
    echo "   npm run dev"
    echo ""
    echo "2. Or start them individually:"
    echo "   Frontend: cd apps/frontend && npm start"
    echo "   Backend:  cd apps/backend && npm start"
    echo ""
    echo "3. Open your browser and navigate to:"
    echo "   Frontend: http://localhost:3000"
    echo "   Backend:  http://localhost:5000"
    echo ""
    echo "4. Login with demo credentials:"
    echo "   admin/admin, test/test, or gyanesh/gyanesh123"
    echo ""
    echo "📚 Documentation:"
    echo "   - Project Structure: docs/architecture/PROJECT_STRUCTURE.md"
    echo "   - API Documentation: docs/api/README.md"
    echo "   - User Guide: docs/user-guide/README.md"
    echo ""
    echo "👨‍💻 Author: Gyanesh K Pandey"
    echo "🔗 Repository: https://github.com/mahegyaneshpandey/spm"
    echo ""
}

# Main setup function
main() {
    echo "🚀 MasterBC Project Setup"
    echo "Author: Gyanesh K Pandey"
    echo "Repository: https://github.com/mahegyaneshpandey/spm"
    echo ""
    
    # Check prerequisites
    print_status "Checking prerequisites..."
    if ! check_node_version || ! check_npm_version; then
        print_error "Prerequisites not met. Please install Node.js 16+ and npm."
        exit 1
    fi
    
    # Run setup steps
    install_dependencies
    setup_environment
    create_directories
    setup_git_hooks
    verify_installation
    
    # Show completion message
    show_next_steps
}

# Run main function
main "$@"
