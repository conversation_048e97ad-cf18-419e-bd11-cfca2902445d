// Business Case Management Server with MongoDB simulation
const http = require('http');
const url = require('url');
const fs = require('fs');
const path = require('path');

const PORT = process.env.PORT || 5000;
const DATA_DIR = path.join(__dirname, 'data');

// Ensure data directory exists
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// File-based storage (simulating MongoDB collections)
const COLLECTIONS = {
  users: path.join(DATA_DIR, 'users.json'),
  businessCases: path.join(DATA_DIR, 'businessCases.json'),
  parameters: path.join(DATA_DIR, 'parameters.json'),
  programs: path.join(DATA_DIR, 'programs.json'),
  projects: path.join(DATA_DIR, 'projects.json'),
  ideas: path.join(DATA_DIR, 'ideas.json'),
  masterBusinessCases: path.join(DATA_DIR, 'masterBusinessCases.json')
};

// Initialize data storage
function initializeStorage() {
  console.log('🔄 Initializing Business Case Management storage...');
  
  // Initialize users collection (reuse existing)
  if (!fs.existsSync(COLLECTIONS.users)) {
    const defaultUsers = [
      {
        id: '1',
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'admin',
        password: 'password123',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Financial Analyst',
        email: '<EMAIL>',
        role: 'financial_analyst',
        password: 'password123',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '4',
        name: 'Gyanesh',
        email: '<EMAIL>',
        role: 'admin',
        password: 'gyanesh123',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
    fs.writeFileSync(COLLECTIONS.users, JSON.stringify(defaultUsers, null, 2));
    console.log('✅ Users collection initialized');
  }

  // Initialize business cases collection
  if (!fs.existsSync(COLLECTIONS.businessCases)) {
    const defaultBusinessCases = [
      {
        id: '1',
        name: 'Digital Transformation Initiative',
        description: 'Company-wide digital transformation to improve efficiency and customer experience',
        tags: ['digital', 'transformation', 'efficiency'],
        businessUnit: 'IT',
        timeframe: {
          startYear: 2024,
          endYear: 2027
        },
        financialData: {
          capex: [
            { year: 2024, amount: 500000, description: 'Software licenses and infrastructure' },
            { year: 2025, amount: 300000, description: 'Additional hardware and training' }
          ],
          opex: [
            { year: 2024, amount: 200000, description: 'Consulting and implementation' },
            { year: 2025, amount: 150000, description: 'Ongoing support and maintenance' },
            { year: 2026, amount: 150000, description: 'Ongoing support and maintenance' },
            { year: 2027, amount: 150000, description: 'Ongoing support and maintenance' }
          ],
          revenue: [
            { year: 2025, amount: 400000, description: 'Efficiency savings' },
            { year: 2026, amount: 600000, description: 'Efficiency savings and new revenue' },
            { year: 2027, amount: 800000, description: 'Full benefits realization' }
          ],
          totalCapex: 800000,
          totalOpex: 650000
        },
        calculatedMetrics: {
          irr: 18.5,
          npv: 245000,
          paybackPeriod: 2.8,
          grossMargin: 24.1,
          commercialMargin: 21.1
        },
        status: 'active',
        createdBy: '<EMAIL>',
        lastModifiedBy: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Manufacturing Equipment Upgrade',
        description: 'Upgrade production line equipment to increase capacity and reduce costs',
        tags: ['manufacturing', 'equipment', 'capacity'],
        businessUnit: 'Operations',
        timeframe: {
          startYear: 2024,
          endYear: 2026
        },
        financialData: {
          capex: [
            { year: 2024, amount: 1200000, description: 'New production equipment' }
          ],
          opex: [
            { year: 2024, amount: 100000, description: 'Installation and training' },
            { year: 2025, amount: 80000, description: 'Maintenance and support' },
            { year: 2026, amount: 80000, description: 'Maintenance and support' }
          ],
          revenue: [
            { year: 2025, amount: 600000, description: 'Increased production capacity' },
            { year: 2026, amount: 800000, description: 'Full capacity utilization' }
          ],
          totalCapex: 1200000,
          totalOpex: 260000
        },
        calculatedMetrics: {
          irr: 15.2,
          npv: 180000,
          paybackPeriod: 2.1,
          grossMargin: 4.1,
          commercialMargin: -0.9
        },
        status: 'draft',
        createdBy: '<EMAIL>',
        lastModifiedBy: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
    fs.writeFileSync(COLLECTIONS.businessCases, JSON.stringify(defaultBusinessCases, null, 2));
    console.log('✅ Business cases collection initialized');
  }

  // Initialize programs collection
  if (!fs.existsSync(COLLECTIONS.programs)) {
    const defaultPrograms = [
      {
        id: '1',
        name: 'Digital Innovation Program',
        description: 'Strategic program to drive digital transformation across all business units',
        owner: 'John Smith',
        linkedMasterBC: null,
        linkedProjects: [],
        status: 'active',
        startDate: '2024-01-01',
        endDate: '2026-12-31',
        budget: 5000000,
        priority: 'high',
        createdBy: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Manufacturing Excellence Program',
        description: 'Program to optimize manufacturing processes and reduce operational costs',
        owner: 'Sarah Johnson',
        linkedMasterBC: null,
        linkedProjects: [],
        status: 'active',
        startDate: '2024-03-01',
        endDate: '2025-12-31',
        budget: 3000000,
        priority: 'medium',
        createdBy: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '3',
        name: 'Customer Experience Enhancement',
        description: 'Comprehensive program to improve customer satisfaction and retention',
        owner: 'Mike Chen',
        linkedMasterBC: null,
        linkedProjects: [],
        status: 'planning',
        startDate: '2024-06-01',
        endDate: '2025-06-30',
        budget: 2500000,
        priority: 'high',
        createdBy: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
    fs.writeFileSync(COLLECTIONS.programs, JSON.stringify(defaultPrograms, null, 2));
    console.log('✅ Programs collection initialized');
  }

  // Initialize projects collection
  if (!fs.existsSync(COLLECTIONS.projects)) {
    const defaultProjects = [
      {
        id: '1',
        name: 'CRM System Implementation',
        description: 'Implementation of new customer relationship management system',
        type: 'project',
        startDate: '2024-02-01',
        endDate: '2024-08-31',
        owner: 'Alice Cooper',
        associatedProgram: '3',
        linkedBusinessCases: ['1'],
        milestones: [
          { id: '1', title: 'Requirements Gathering', dueDate: '2024-03-15', status: 'completed' },
          { id: '2', title: 'System Design', dueDate: '2024-04-30', status: 'in-progress' },
          { id: '3', title: 'Development Phase', dueDate: '2024-07-15', status: 'pending' }
        ],
        status: 'active',
        createdBy: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Manufacturing Automation Epic',
        description: 'Epic to automate key manufacturing processes using IoT and AI',
        type: 'epic',
        startDate: '2024-01-15',
        endDate: '2024-12-31',
        owner: 'Bob Martinez',
        associatedProgram: '2',
        linkedBusinessCases: ['2'],
        milestones: [
          { id: '4', title: 'IoT Sensor Deployment', dueDate: '2024-04-01', status: 'completed' },
          { id: '5', title: 'AI Model Training', dueDate: '2024-06-30', status: 'in-progress' },
          { id: '6', title: 'Full Integration', dueDate: '2024-11-30', status: 'pending' }
        ],
        status: 'active',
        createdBy: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '3',
        name: 'Digital Platform Development',
        description: 'Development of unified digital platform for all business units',
        type: 'project',
        startDate: '2024-03-01',
        endDate: '2025-02-28',
        owner: 'Carol Davis',
        associatedProgram: '1',
        linkedBusinessCases: ['1'],
        milestones: [
          { id: '7', title: 'Architecture Design', dueDate: '2024-04-15', status: 'pending' },
          { id: '8', title: 'MVP Development', dueDate: '2024-08-31', status: 'pending' },
          { id: '9', title: 'Full Platform Launch', dueDate: '2025-01-31', status: 'pending' }
        ],
        status: 'planning',
        createdBy: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
    fs.writeFileSync(COLLECTIONS.projects, JSON.stringify(defaultProjects, null, 2));
    console.log('✅ Projects collection initialized');
  }

  // Initialize ideas collection
  if (!fs.existsSync(COLLECTIONS.ideas)) {
    const defaultIdeas = [
      {
        id: '1',
        title: 'AI-Powered Customer Support',
        description: 'Implement AI chatbots to handle tier-1 customer support queries',
        category: 'Customer Service',
        submittedBy: '<EMAIL>',
        status: 'under-review',
        priority: 'high',
        estimatedValue: 500000,
        linkedBusinessCaseId: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        title: 'Blockchain Supply Chain Tracking',
        description: 'Use blockchain technology to create transparent supply chain tracking',
        category: 'Supply Chain',
        submittedBy: '<EMAIL>',
        status: 'approved',
        priority: 'medium',
        estimatedValue: 750000,
        linkedBusinessCaseId: '1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '3',
        title: 'Green Energy Initiative',
        description: 'Transition manufacturing facilities to renewable energy sources',
        category: 'Sustainability',
        submittedBy: '<EMAIL>',
        status: 'submitted',
        priority: 'high',
        estimatedValue: 2000000,
        linkedBusinessCaseId: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '4',
        title: 'Employee Wellness Platform',
        description: 'Digital platform for employee health and wellness programs',
        category: 'Human Resources',
        submittedBy: '<EMAIL>',
        status: 'rejected',
        priority: 'low',
        estimatedValue: 150000,
        linkedBusinessCaseId: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
    fs.writeFileSync(COLLECTIONS.ideas, JSON.stringify(defaultIdeas, null, 2));
    console.log('✅ Ideas collection initialized');
  }

  // Initialize master business cases collection
  if (!fs.existsSync(COLLECTIONS.masterBusinessCases)) {
    const defaultMasterBCs = [
      {
        id: '1',
        name: 'Digital Transformation Master BC',
        description: 'Comprehensive digital transformation initiative across all business units',
        linkedBCs: ['1'],
        aggregatedMetrics: {
          totalCapex: 800000,
          totalOpex: 650000,
          totalInvestment: 1450000,
          totalNPV: 245000,
          avgIRR: 18.5,
          avgPaybackPeriod: 2.8,
          avgGrossMargin: 24.1,
          avgCommercialMargin: 21.1,
          linkedCount: 1
        },
        status: 'active',
        createdBy: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Operational Excellence Master BC',
        description: 'Master business case for operational efficiency and cost reduction initiatives',
        linkedBCs: ['2'],
        aggregatedMetrics: {
          totalCapex: 1200000,
          totalOpex: 260000,
          totalInvestment: 1460000,
          totalNPV: 180000,
          avgIRR: 15.2,
          avgPaybackPeriod: 2.1,
          avgGrossMargin: 4.1,
          avgCommercialMargin: -0.9,
          linkedCount: 1
        },
        status: 'active',
        createdBy: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
    fs.writeFileSync(COLLECTIONS.masterBusinessCases, JSON.stringify(defaultMasterBCs, null, 2));
    console.log('✅ Master Business Cases collection initialized');
  }

  console.log('✅ Business Case Management storage initialized successfully');
}

// Helper functions for data operations
function readCollection(collectionName) {
  try {
    const filePath = COLLECTIONS[collectionName];
    if (fs.existsSync(filePath)) {
      const data = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error(`Error reading ${collectionName}:`, error);
    return [];
  }
}

function writeCollection(collectionName, data) {
  try {
    const filePath = COLLECTIONS[collectionName];
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    console.error(`Error writing ${collectionName}:`, error);
    return false;
  }
}

function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Financial calculations
function calculateBusinessCaseMetrics(businessCase) {
  const { timeframe, financialData } = businessCase;
  
  // Simple IRR calculation (approximation)
  const totalInvestment = financialData.totalCapex + financialData.totalOpex;
  const totalRevenue = financialData.revenue ? 
    financialData.revenue.reduce((sum, item) => sum + item.amount, 0) : 0;
  
  const netBenefit = totalRevenue - totalInvestment;
  const years = timeframe.endYear - timeframe.startYear + 1;
  
  // Simplified calculations
  const irr = totalRevenue > 0 ? ((netBenefit / totalInvestment) / years) * 100 : 0;
  const npv = netBenefit * 0.8; // Simplified NPV with discount factor
  const paybackPeriod = totalRevenue > 0 ? totalInvestment / (totalRevenue / years) : 0;
  const grossMargin = totalRevenue > 0 ? ((totalRevenue - totalInvestment) / totalRevenue) * 100 : 0;
  const commercialMargin = grossMargin - 3; // Apply 3% risk adjustment
  
  return {
    irr: Math.round(irr * 10) / 10,
    npv: Math.round(npv),
    paybackPeriod: Math.round(paybackPeriod * 10) / 10,
    grossMargin: Math.round(grossMargin * 10) / 10,
    commercialMargin: Math.round(commercialMargin * 10) / 10
  };
}

// Helper function to parse JSON body
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(error);
      }
    });
  });
}

// Helper function to send JSON response
function sendJSON(res, statusCode, data) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  res.end(JSON.stringify(data));
}

// Create HTTP server
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathName = parsedUrl.pathname;
  const method = req.method;
  const query = parsedUrl.query;

  console.log(`${method} ${pathName}`);

  // Handle CORS preflight
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end();
    return;
  }

  try {
    // Health check
    if (pathName === '/health' && method === 'GET') {
      sendJSON(res, 200, {
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        database: 'Business Case Management Storage',
        features: ['Business Case CRUD', 'Financial Calculations', 'Excel Export Ready']
      });
      return;
    }

    // Authentication endpoints (reuse existing)
    if (pathName === '/api/auth/login' && method === 'POST') {
      const body = await parseBody(req);
      const { email, password } = body;

      console.log('🔐 Login attempt:', { email });

      const users = readCollection('users');
      const user = users.find(u => u.email === email && u.password === password);
      
      if (user) {
        sendJSON(res, 200, {
          success: true,
          data: {
            token: 'business-case-token-' + user.id,
            user: {
              id: user.id,
              name: user.name,
              email: user.email,
              role: user.role,
              createdAt: user.createdAt,
              updatedAt: user.updatedAt
            }
          }
        });
        console.log(`✅ Login successful for ${user.name} (${user.role})`);
      } else {
        sendJSON(res, 400, {
          success: false,
          error: 'Invalid credentials'
        });
        console.log('❌ Login failed: Invalid credentials');
      }
      return;
    }

    // Business Cases endpoints
    if (pathName === '/api/business-cases' && method === 'GET') {
      const businessCases = readCollection('businessCases');

      // Apply filters
      let filteredCases = businessCases;

      if (query.search) {
        const searchTerm = query.search.toLowerCase();
        filteredCases = filteredCases.filter(bc =>
          bc.name.toLowerCase().includes(searchTerm) ||
          (bc.description && bc.description.toLowerCase().includes(searchTerm)) ||
          (bc.tags && bc.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
        );
      }

      if (query.status) {
        filteredCases = filteredCases.filter(bc => bc.status === query.status);
      }

      if (query.businessUnit) {
        filteredCases = filteredCases.filter(bc => bc.businessUnit === query.businessUnit);
      }

      // Transform data for frontend compatibility
      const transformedCases = filteredCases.map(bc => {
        // Calculate totals from yearly data
        let totalCapex = 0;
        let totalOpex = 0;
        let totalRevenue = 0;

        if (bc.capex && typeof bc.capex === 'object') {
          totalCapex = Object.values(bc.capex).reduce((sum, val) => sum + (val || 0), 0);
        }

        if (bc.opex && typeof bc.opex === 'object') {
          totalOpex = Object.values(bc.opex).reduce((sum, val) => sum + (val || 0), 0);
        }

        if (bc.revenue && typeof bc.revenue === 'object') {
          totalRevenue = Object.values(bc.revenue).reduce((sum, val) => sum + (val || 0), 0);
        }

        // Convert yearly data to array format for frontend
        const capexItems = bc.capex ? Object.entries(bc.capex).map(([year, amount]) => ({
          year: parseInt(year),
          amount: amount || 0,
          description: `CAPEX ${year}`
        })) : [];

        const opexItems = bc.opex ? Object.entries(bc.opex).map(([year, amount]) => ({
          year: parseInt(year),
          amount: amount || 0,
          description: `OPEX ${year}`
        })) : [];

        const revenueItems = bc.revenue ? Object.entries(bc.revenue).map(([year, amount]) => ({
          year: parseInt(year),
          amount: amount || 0,
          description: `Revenue ${year}`
        })) : [];

        return {
          ...bc,
          totalCapex,
          totalOpex,
          totalRevenue,
          capexItems,
          opexItems,
          revenueItems,
          startYear: bc.timeframe?.startYear,
          endYear: bc.timeframe?.endYear,
          // Include financial metrics
          irr: bc.financialMetrics?.irr || 0,
          npv: bc.financialMetrics?.npv || 0,
          paybackPeriod: bc.financialMetrics?.paybackPeriod || 0,
          grossMargin: bc.financialMetrics?.grossMargin || 0,
          commercialMargin: bc.financialMetrics?.commercialMargin || 0
        };
      });

      sendJSON(res, 200, {
        success: true,
        data: {
          businessCases: transformedCases,
          pagination: {
            page: 1,
            limit: 50,
            total: transformedCases.length,
            pages: 1
          }
        }
      });
      return;
    }

    if (pathName.startsWith('/api/business-cases/') && method === 'GET') {
      const id = pathName.split('/')[3];
      
      if (id === 'stats') {
        // Business case statistics
        const businessCases = readCollection('businessCases');
        
        const stats = {
          total: businessCases.length,
          byStatus: {},
          totalInvestment: 0,
          totalRevenue: 0,
          avgIRR: 0
        };
        
        businessCases.forEach(bc => {
          stats.byStatus[bc.status] = (stats.byStatus[bc.status] || 0) + 1;
          stats.totalInvestment += bc.financialData.totalCapex + bc.financialData.totalOpex;
          if (bc.financialData.revenue) {
            stats.totalRevenue += bc.financialData.revenue.reduce((sum, r) => sum + r.amount, 0);
          }
          if (bc.calculatedMetrics && bc.calculatedMetrics.irr) {
            stats.avgIRR += bc.calculatedMetrics.irr;
          }
        });
        
        stats.avgIRR = businessCases.length > 0 ? stats.avgIRR / businessCases.length : 0;
        
        sendJSON(res, 200, {
          success: true,
          data: stats
        });
        return;
      }

      // Check for available-projects endpoint
      const pathParts = pathName.split('/');
      console.log('🔍 Path parts:', pathParts, 'Looking for available-projects at index 4:', pathParts[4]);
      if (pathParts[4] === 'available-projects') {
        console.log('✅ Available-projects endpoint matched!');
        const businessCases = readCollection('businessCases');
        console.log('📊 Total business cases found:', businessCases.length);
        const businessCase = businessCases.find(bc => bc.id === id);
        console.log('🔍 Looking for business case with ID:', id);
        console.log('📋 Business case found:', businessCase ? 'YES' : 'NO');

        if (!businessCase) {
          console.log('❌ Business case not found, sending 404');
          sendJSON(res, 404, {
            success: false,
            error: 'Business case not found'
          });
          return;
        }

        // Get all projects
        const projects = readCollection('projects');
        console.log('📊 Total projects found:', projects.length);

        // Get linked projects for this business case
        const linkedProjectIds = businessCase.linkedProjects || [];
        console.log('🔗 Linked project IDs:', linkedProjectIds);
        const linkedProjects = projects.filter(p => linkedProjectIds.includes(p.id));
        console.log('🔗 Linked projects count:', linkedProjects.length);

        // Get available projects (not linked to this business case)
        const availableProjects = projects.filter(p => !linkedProjectIds.includes(p.id));
        console.log('📋 Available projects count:', availableProjects.length);

        console.log('📤 Sending response...');
        sendJSON(res, 200, {
          success: true,
          data: {
            businessCase,
            linkedProjects,
            availableProjects
          }
        });
        console.log('✅ Response sent successfully');
        return;
      }

      // Get single business case
      const businessCases = readCollection('businessCases');
      const businessCase = businessCases.find(bc => bc.id === id);

      if (businessCase) {
        // Transform data for frontend compatibility
        let totalCapex = 0;
        let totalOpex = 0;
        let totalRevenue = 0;

        if (businessCase.capex && typeof businessCase.capex === 'object') {
          totalCapex = Object.values(businessCase.capex).reduce((sum, val) => sum + (val || 0), 0);
        }

        if (businessCase.opex && typeof businessCase.opex === 'object') {
          totalOpex = Object.values(businessCase.opex).reduce((sum, val) => sum + (val || 0), 0);
        }

        if (businessCase.revenue && typeof businessCase.revenue === 'object') {
          totalRevenue = Object.values(businessCase.revenue).reduce((sum, val) => sum + (val || 0), 0);
        }

        // Convert yearly data to array format for frontend
        const capexItems = businessCase.capex ? Object.entries(businessCase.capex).map(([year, amount]) => ({
          year: parseInt(year),
          amount: amount || 0,
          description: `CAPEX ${year}`
        })) : [];

        const opexItems = businessCase.opex ? Object.entries(businessCase.opex).map(([year, amount]) => ({
          year: parseInt(year),
          amount: amount || 0,
          description: `OPEX ${year}`
        })) : [];

        const revenueItems = businessCase.revenue ? Object.entries(businessCase.revenue).map(([year, amount]) => ({
          year: parseInt(year),
          amount: amount || 0,
          description: `Revenue ${year}`
        })) : [];

        const transformedBusinessCase = {
          ...businessCase,
          totalCapex,
          totalOpex,
          totalRevenue,
          capexItems,
          opexItems,
          revenueItems,
          startYear: businessCase.timeframe?.startYear,
          endYear: businessCase.timeframe?.endYear,
          // Include financial metrics
          irr: businessCase.financialMetrics?.irr || 0,
          npv: businessCase.financialMetrics?.npv || 0,
          paybackPeriod: businessCase.financialMetrics?.paybackPeriod || 0,
          grossMargin: businessCase.financialMetrics?.grossMargin || 0,
          commercialMargin: businessCase.financialMetrics?.commercialMargin || 0
        };

        sendJSON(res, 200, {
          success: true,
          data: transformedBusinessCase
        });
      } else {
        sendJSON(res, 404, {
          success: false,
          error: 'Business case not found'
        });
      }
      return;
    }

    if (pathName === '/api/business-cases' && method === 'POST') {
      const body = await parseBody(req);
      const businessCases = readCollection('businessCases');
      
      // Calculate financial metrics
      const calculatedMetrics = calculateBusinessCaseMetrics(body);
      
      const newBusinessCase = {
        id: generateId(),
        ...body,
        calculatedMetrics,
        status: body.status || 'draft',
        createdBy: '<EMAIL>', // Default user
        lastModifiedBy: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      businessCases.push(newBusinessCase);
      const success = writeCollection('businessCases', businessCases);
      
      if (success) {
        sendJSON(res, 201, {
          success: true,
          data: newBusinessCase
        });
        console.log(`✅ New business case created: ${newBusinessCase.name}`);
      } else {
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to create business case'
        });
      }
      return;
    }

    // Business Case UPDATE endpoint
    if (pathName.startsWith('/api/business-cases/') && method === 'PUT') {
      const id = pathName.split('/')[3];

      // Skip if this is a special endpoint like /link-projects
      if (pathName.endsWith('/link-projects')) {
        // Let it fall through to the linking endpoint below
      } else {
        const body = await parseBody(req);
        const businessCases = readCollection('businessCases');
        const bcIndex = businessCases.findIndex(bc => bc.id === id);

        if (bcIndex !== -1) {
          // Recalculate financial metrics if financial data is updated
          let calculatedMetrics = businessCases[bcIndex].calculatedMetrics;
          if (body.financialData) {
            calculatedMetrics = calculateBusinessCaseMetrics(body);
          }

          businessCases[bcIndex] = {
            ...businessCases[bcIndex],
            ...body,
            id: id, // Keep original ID
            calculatedMetrics: calculatedMetrics,
            updatedAt: new Date().toISOString()
          };

          const success = writeCollection('businessCases', businessCases);

          if (success) {
            sendJSON(res, 200, {
              success: true,
              data: businessCases[bcIndex]
            });
            console.log(`✅ Business case updated: ${businessCases[bcIndex].name}`);
          } else {
            sendJSON(res, 500, {
              success: false,
              error: 'Failed to update business case'
            });
          }
        } else {
          sendJSON(res, 404, {
            success: false,
            error: 'Business case not found'
          });
        }
        return;
      }
    }

    // Business Case DELETE endpoint
    if (pathName.startsWith('/api/business-cases/') && method === 'DELETE') {
      const id = pathName.split('/')[3];
      const businessCases = readCollection('businessCases');
      const bcIndex = businessCases.findIndex(bc => bc.id === id);

      if (bcIndex !== -1) {
        const deletedBC = businessCases.splice(bcIndex, 1)[0];
        const success = writeCollection('businessCases', businessCases);

        if (success) {
          sendJSON(res, 200, {
            success: true,
            data: {
              message: 'Business case deleted successfully',
              deletedBusinessCase: deletedBC
            }
          });
          console.log(`✅ Business case deleted: ${deletedBC.name}`);
        } else {
          sendJSON(res, 500, {
            success: false,
            error: 'Failed to delete business case'
          });
        }
      } else {
        sendJSON(res, 404, {
          success: false,
          error: 'Business case not found'
        });
      }
      return;
    }

    // Business Case Project Linking endpoint
    if (pathName.startsWith('/api/business-cases/') && pathName.endsWith('/link-projects') && method === 'POST') {
      const bcId = pathName.split('/')[3];
      const body = await parseBody(req);
      const { projectIds, action } = body;

      const businessCases = readCollection('businessCases');
      const bcIndex = businessCases.findIndex(bc => bc.id === bcId);

      if (bcIndex === -1) {
        sendJSON(res, 404, {
          success: false,
          error: 'Business case not found'
        });
        return;
      }

      const businessCase = businessCases[bcIndex];
      let linkedProjects = businessCase.linkedProjects || [];

      if (action === 'link') {
        // Add project IDs to linked projects (avoid duplicates)
        projectIds.forEach(projectId => {
          if (!linkedProjects.includes(projectId)) {
            linkedProjects.push(projectId);
          }
        });
      } else if (action === 'unlink') {
        // Remove project IDs from linked projects
        linkedProjects = linkedProjects.filter(projectId => !projectIds.includes(projectId));
      }

      businessCase.linkedProjects = linkedProjects;
      businessCase.updatedAt = new Date().toISOString();
      businessCases[bcIndex] = businessCase;

      const success = writeCollection('businessCases', businessCases);

      if (success) {
        sendJSON(res, 200, {
          success: true,
          data: {
            projectsUpdated: projectIds.length,
            linkedProjects: linkedProjects,
            message: `Successfully ${action}ed ${projectIds.length} project(s)`
          }
        });
        console.log(`✅ Business case ${businessCase.name}: ${action}ed ${projectIds.length} project(s)`);
      } else {
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to update business case'
        });
      }
      return;
    }

    // Programs endpoints
    if (pathName === '/api/programs' && method === 'GET') {
      const programs = readCollection('programs');
      const masterBusinessCases = readCollection('masterBusinessCases');
      const projects = readCollection('projects');
      const businessCases = readCollection('businessCases');

      // Transform data for frontend compatibility with connected relationships
      const transformedPrograms = programs.map(program => {
        // Get connected Master Business Case
        const connectedMasterBC = masterBusinessCases.find(mbc =>
          mbc.id === program.linkedMasterBusinessCaseId
        );

        // Get connected Projects and Epics
        const connectedProjects = projects.filter(proj =>
          (program.linkedProjectIds && program.linkedProjectIds.includes(proj.id)) ||
          (program.linkedEpicIds && program.linkedEpicIds.includes(proj.id))
        );

        // Get connected Business Cases through Master BC and Projects
        let connectedBusinessCases = [];

        // Business Cases from Master BC
        if (connectedMasterBC && connectedMasterBC.linkedBusinessCases) {
          const mbcBusinessCases = businessCases.filter(bc =>
            connectedMasterBC.linkedBusinessCases.includes(bc.id)
          );
          connectedBusinessCases = [...connectedBusinessCases, ...mbcBusinessCases];
        }

        // Business Cases from Projects/Epics
        connectedProjects.forEach(proj => {
          if (proj.linkedBusinessCaseIds) {
            const projBusinessCases = businessCases.filter(bc =>
              proj.linkedBusinessCaseIds.includes(bc.id)
            );
            connectedBusinessCases = [...connectedBusinessCases, ...projBusinessCases];
          }
        });

        // Remove duplicates
        connectedBusinessCases = connectedBusinessCases.filter((bc, index, self) =>
          index === self.findIndex(b => b.id === bc.id)
        );

        // Calculate aggregated financial metrics from connected business cases
        let totalInvestment = 0;
        let totalNPV = 0;
        let totalIRR = 0;
        let avgIRR = 0;

        connectedBusinessCases.forEach(bc => {
          // Sum yearly CAPEX and OPEX
          if (bc.capex && typeof bc.capex === 'object') {
            totalInvestment += Object.values(bc.capex).reduce((sum, val) => sum + (val || 0), 0);
          }
          if (bc.opex && typeof bc.opex === 'object') {
            totalInvestment += Object.values(bc.opex).reduce((sum, val) => sum + (val || 0), 0);
          }

          // Sum financial metrics
          totalNPV += bc.financialMetrics?.npv || 0;
          totalIRR += bc.financialMetrics?.irr || 0;
        });

        avgIRR = connectedBusinessCases.length > 0 ? totalIRR / connectedBusinessCases.length : 0;

        return {
          ...program,
          // Connected relationships
          connectedMasterBC: connectedMasterBC ? {
            id: connectedMasterBC.id,
            name: connectedMasterBC.name,
            businessUnit: connectedMasterBC.businessUnit,
            status: connectedMasterBC.status
          } : null,
          connectedProjects: connectedProjects.map(proj => ({
            id: proj.id,
            name: proj.name,
            type: proj.type,
            owner: proj.owner,
            status: proj.status,
            linkedBusinessCases: proj.linkedBusinessCaseIds || []
          })),
          connectedBusinessCases: connectedBusinessCases.map(bc => ({
            id: bc.id,
            name: bc.name,
            businessUnit: bc.businessUnit,
            status: bc.status
          })),
          // Aggregated financial metrics
          aggregatedMetrics: {
            totalInvestment: totalInvestment,
            totalNPV: totalNPV,
            avgIRR: avgIRR,
            connectedProjectsCount: connectedProjects.length,
            connectedBusinessCasesCount: connectedBusinessCases.length
          }
        };
      });

      sendJSON(res, 200, {
        success: true,
        data: {
          programs: transformedPrograms,
          pagination: {
            page: 1,
            limit: 50,
            total: transformedPrograms.length,
            pages: 1
          }
        }
      });
      return;
    }

    if (pathName === '/api/programs' && method === 'POST') {
      const body = await parseBody(req);
      const programs = readCollection('programs');

      const newProgram = {
        id: generateId(),
        name: body.name,
        description: body.description || '',
        owner: body.owner || '',
        linkedMasterBC: body.linkedMasterBC || null,
        linkedProjects: body.linkedProjects || [],
        status: body.status || 'active',
        createdBy: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      programs.push(newProgram);
      const success = writeCollection('programs', programs);

      if (success) {
        sendJSON(res, 201, {
          success: true,
          data: newProgram
        });
        console.log(`✅ New program created: ${newProgram.name}`);
      } else {
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to create program'
        });
      }
      return;
    }

    if (pathName.startsWith('/api/programs/') && method === 'GET') {
      const id = pathName.split('/')[3];
      const programs = readCollection('programs');
      const masterBusinessCases = readCollection('masterBusinessCases');
      const projects = readCollection('projects');
      const businessCases = readCollection('businessCases');
      const program = programs.find(p => p.id === id);

      if (program) {
        // Get connected Master Business Case
        const connectedMasterBC = masterBusinessCases.find(mbc =>
          mbc.id === program.linkedMasterBusinessCaseId
        );

        // Get connected Projects and Epics
        const connectedProjects = projects.filter(proj =>
          (program.linkedProjectIds && program.linkedProjectIds.includes(proj.id)) ||
          (program.linkedEpicIds && program.linkedEpicIds.includes(proj.id))
        );

        // Get connected Business Cases through Master BC and Projects
        let connectedBusinessCases = [];

        // Business Cases from Master BC
        if (connectedMasterBC && connectedMasterBC.linkedBusinessCases) {
          const mbcBusinessCases = businessCases.filter(bc =>
            connectedMasterBC.linkedBusinessCases.includes(bc.id)
          );
          connectedBusinessCases = [...connectedBusinessCases, ...mbcBusinessCases];
        }

        // Business Cases from Projects/Epics
        connectedProjects.forEach(proj => {
          if (proj.linkedBusinessCaseIds) {
            const projBusinessCases = businessCases.filter(bc =>
              proj.linkedBusinessCaseIds.includes(bc.id)
            );
            connectedBusinessCases = [...connectedBusinessCases, ...projBusinessCases];
          }
        });

        // Remove duplicates
        connectedBusinessCases = connectedBusinessCases.filter((bc, index, self) =>
          index === self.findIndex(b => b.id === bc.id)
        );

        // Calculate aggregated financial metrics from connected business cases
        let totalInvestment = 0;
        let totalNPV = 0;
        let totalIRR = 0;
        let avgIRR = 0;

        connectedBusinessCases.forEach(bc => {
          // Sum yearly CAPEX and OPEX
          if (bc.capex && typeof bc.capex === 'object') {
            totalInvestment += Object.values(bc.capex).reduce((sum, val) => sum + (val || 0), 0);
          }
          if (bc.opex && typeof bc.opex === 'object') {
            totalInvestment += Object.values(bc.opex).reduce((sum, val) => sum + (val || 0), 0);
          }

          // Sum financial metrics
          totalNPV += bc.financialMetrics?.npv || 0;
          totalIRR += bc.financialMetrics?.irr || 0;
        });

        avgIRR = connectedBusinessCases.length > 0 ? totalIRR / connectedBusinessCases.length : 0;

        const transformedProgram = {
          ...program,
          // Connected relationships
          connectedMasterBC: connectedMasterBC ? {
            id: connectedMasterBC.id,
            name: connectedMasterBC.name,
            businessUnit: connectedMasterBC.businessUnit,
            status: connectedMasterBC.status
          } : null,
          connectedProjects: connectedProjects.map(proj => ({
            id: proj.id,
            name: proj.name,
            type: proj.type,
            owner: proj.owner,
            status: proj.status,
            linkedBusinessCases: proj.linkedBusinessCaseIds || []
          })),
          connectedBusinessCases: connectedBusinessCases.map(bc => ({
            id: bc.id,
            name: bc.name,
            businessUnit: bc.businessUnit,
            status: bc.status
          })),
          // Aggregated financial metrics
          aggregatedMetrics: {
            totalInvestment: totalInvestment,
            totalNPV: totalNPV,
            avgIRR: avgIRR,
            connectedProjectsCount: connectedProjects.length,
            connectedBusinessCasesCount: connectedBusinessCases.length
          }
        };

        sendJSON(res, 200, {
          success: true,
          data: transformedProgram
        });
      } else {
        sendJSON(res, 404, {
          success: false,
          error: 'Program not found'
        });
      }
      return;
    }

    // Program linking to Master BC endpoint (must come before general PUT)
    if (pathName.startsWith('/api/programs/') && pathName.endsWith('/link-master-bc') && method === 'PUT') {
      const programId = pathName.split('/')[3];
      const body = await parseBody(req);
      const { masterBusinessCaseId, action } = body;

      const programs = readCollection('programs');
      const programIndex = programs.findIndex(p => p.id === programId);

      if (programIndex === -1) {
        sendJSON(res, 404, {
          success: false,
          error: 'Program not found'
        });
        return;
      }

      const program = programs[programIndex];

      if (action === 'link') {
        // Check if Master BC exists
        const masterBCs = readCollection('masterBusinessCases');
        const masterBC = masterBCs.find(mbc => mbc.id === masterBusinessCaseId);

        if (!masterBC) {
          sendJSON(res, 404, {
            success: false,
            error: 'Master Business Case not found'
          });
          return;
        }

        // Check if another program is already linked to this Master BC (one-to-one constraint)
        const existingProgram = programs.find(p => p.linkedMasterBC === masterBusinessCaseId && p.id !== programId);
        if (existingProgram) {
          sendJSON(res, 400, {
            success: false,
            error: `Master BC is already linked to program "${existingProgram.name}". Please unlink it first.`
          });
          return;
        }

        program.linkedMasterBC = masterBusinessCaseId;
      } else if (action === 'unlink') {
        program.linkedMasterBC = null;
      }

      program.updatedAt = new Date().toISOString();
      programs[programIndex] = program;

      const success = writeCollection('programs', programs);

      if (success) {
        sendJSON(res, 200, {
          success: true,
          data: {
            program: program,
            message: `Successfully ${action}ed program ${action === 'link' ? 'to' : 'from'} Master BC`
          }
        });
        console.log(`✅ Program ${program.name}: ${action}ed ${action === 'link' ? 'to' : 'from'} Master BC`);
      } else {
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to update program'
        });
      }
      return;
    }

    if (pathName.startsWith('/api/programs/') && method === 'PUT') {
      const id = pathName.split('/')[3];
      const body = await parseBody(req);
      const programs = readCollection('programs');
      const programIndex = programs.findIndex(p => p.id === id);

      if (programIndex !== -1) {
        programs[programIndex] = {
          ...programs[programIndex],
          ...body,
          id: id, // Keep original ID
          updatedAt: new Date().toISOString()
        };

        const success = writeCollection('programs', programs);

        if (success) {
          sendJSON(res, 200, {
            success: true,
            data: programs[programIndex]
          });
          console.log(`✅ Program updated: ${programs[programIndex].name}`);
        } else {
          sendJSON(res, 500, {
            success: false,
            error: 'Failed to update program'
          });
        }
      } else {
        sendJSON(res, 404, {
          success: false,
          error: 'Program not found'
        });
      }
      return;
    }

    if (pathName.startsWith('/api/programs/') && method === 'DELETE') {
      const id = pathName.split('/')[3];
      const programs = readCollection('programs');
      const programIndex = programs.findIndex(p => p.id === id);

      if (programIndex !== -1) {
        const deletedProgram = programs.splice(programIndex, 1)[0];
        const success = writeCollection('programs', programs);

        if (success) {
          sendJSON(res, 200, {
            success: true,
            data: { message: 'Program deleted successfully' }
          });
          console.log(`✅ Program deleted: ${deletedProgram.name}`);
        } else {
          sendJSON(res, 500, {
            success: false,
            error: 'Failed to delete program'
          });
        }
      } else {
        sendJSON(res, 404, {
          success: false,
          error: 'Program not found'
        });
      }
      return;
    }

    // Projects endpoints
    if (pathName === '/api/projects' && method === 'GET') {
      const projects = readCollection('projects');
      console.log(`📊 Projects loaded: ${projects.length} items`);

      // Parse pagination parameters
      const page = parseInt(query.page) || 1;
      const limit = parseInt(query.limit) || 100; // Increased default limit
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      // Get paginated projects
      const paginatedProjects = projects.slice(startIndex, endIndex);
      console.log(`📤 Sending ${paginatedProjects.length} projects (page ${page})`);

      sendJSON(res, 200, {
        success: true,
        data: {
          projects: paginatedProjects,
          pagination: {
            page: page,
            limit: limit,
            total: projects.length,
            pages: Math.ceil(projects.length / limit),
            hasNext: endIndex < projects.length,
            hasPrev: page > 1
          }
        }
      });
      console.log(`✅ Projects response sent successfully`);
      return;
    }

    if (pathName === '/api/projects' && method === 'POST') {
      const body = await parseBody(req);
      const projects = readCollection('projects');

      const newProject = {
        id: generateId(),
        name: body.name,
        description: body.description || '',
        type: body.type || 'project', // 'project' or 'epic'
        startDate: body.startDate || '',
        endDate: body.endDate || '',
        owner: body.owner || '',
        associatedProgram: body.associatedProgram || null,
        linkedBusinessCases: body.linkedBusinessCases || [],
        milestones: body.milestones || [],
        status: body.status || 'active',
        createdBy: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      projects.push(newProject);
      const success = writeCollection('projects', projects);

      if (success) {
        sendJSON(res, 201, {
          success: true,
          data: newProject
        });
        console.log(`✅ New ${newProject.type} created: ${newProject.name}`);
      } else {
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to create project'
        });
      }
      return;
    }

    if (pathName.startsWith('/api/projects/') && method === 'GET') {
      const id = pathName.split('/')[3];
      const projects = readCollection('projects');
      const project = projects.find(p => p.id === id);

      if (project) {
        sendJSON(res, 200, {
          success: true,
          data: project
        });
      } else {
        sendJSON(res, 404, {
          success: false,
          error: 'Project not found'
        });
      }
      return;
    }

    if (pathName.startsWith('/api/projects/') && method === 'PUT') {
      const id = pathName.split('/')[3];
      const body = await parseBody(req);
      const projects = readCollection('projects');
      const projectIndex = projects.findIndex(p => p.id === id);

      if (projectIndex !== -1) {
        projects[projectIndex] = {
          ...projects[projectIndex],
          ...body,
          id: id,
          updatedAt: new Date().toISOString()
        };

        const success = writeCollection('projects', projects);

        if (success) {
          sendJSON(res, 200, {
            success: true,
            data: projects[projectIndex]
          });
        } else {
          sendJSON(res, 500, {
            success: false,
            error: 'Failed to update project'
          });
        }
      } else {
        sendJSON(res, 404, {
          success: false,
          error: 'Project not found'
        });
      }
      return;
    }

    if (pathName.startsWith('/api/projects/') && method === 'DELETE') {
      const id = pathName.split('/')[3];
      const projects = readCollection('projects');
      const projectIndex = projects.findIndex(p => p.id === id);

      if (projectIndex !== -1) {
        const deletedProject = projects.splice(projectIndex, 1)[0];
        const success = writeCollection('projects', projects);

        if (success) {
          sendJSON(res, 200, {
            success: true,
            message: 'Project deleted successfully',
            data: deletedProject
          });
        } else {
          sendJSON(res, 500, {
            success: false,
            error: 'Failed to delete project'
          });
        }
      } else {
        sendJSON(res, 404, {
          success: false,
          error: 'Project not found'
        });
      }
      return;
    }

    // Ideas endpoints
    if (pathName === '/api/ideas' && method === 'GET') {
      const ideas = readCollection('ideas');
      sendJSON(res, 200, {
        success: true,
        data: {
          ideas: ideas,
          pagination: {
            page: 1,
            limit: 50,
            total: ideas.length,
            pages: 1
          }
        }
      });
      return;
    }

    if (pathName === '/api/ideas' && method === 'POST') {
      const body = await parseBody(req);
      const ideas = readCollection('ideas');

      const newIdea = {
        id: generateId(),
        title: body.title,
        description: body.description || '',
        category: body.category || '',
        submittedBy: body.submittedBy || '<EMAIL>',
        status: body.status || 'submitted',
        priority: body.priority || 'medium',
        estimatedValue: body.estimatedValue || 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      ideas.push(newIdea);
      const success = writeCollection('ideas', ideas);

      if (success) {
        sendJSON(res, 201, {
          success: true,
          data: newIdea
        });
        console.log(`✅ New idea created: ${newIdea.title}`);
      } else {
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to create idea'
        });
      }
      return;
    }

    if (pathName.startsWith('/api/ideas/') && method === 'GET') {
      const id = pathName.split('/')[3];
      const ideas = readCollection('ideas');
      const idea = ideas.find(i => i.id === id);

      if (idea) {
        sendJSON(res, 200, {
          success: true,
          data: { idea: idea }
        });
      } else {
        sendJSON(res, 404, {
          success: false,
          error: 'Idea not found'
        });
      }
      return;
    }

    if (pathName.startsWith('/api/ideas/') && method === 'PUT') {
      const id = pathName.split('/')[3];
      const body = await parseBody(req);
      const ideas = readCollection('ideas');
      const ideaIndex = ideas.findIndex(i => i.id === id);

      if (ideaIndex !== -1) {
        ideas[ideaIndex] = {
          ...ideas[ideaIndex],
          ...body,
          id: id,
          updatedAt: new Date().toISOString()
        };

        const success = writeCollection('ideas', ideas);

        if (success) {
          sendJSON(res, 200, {
            success: true,
            data: { idea: ideas[ideaIndex] }
          });
        } else {
          sendJSON(res, 500, {
            success: false,
            error: 'Failed to update idea'
          });
        }
      } else {
        sendJSON(res, 404, {
          success: false,
          error: 'Idea not found'
        });
      }
      return;
    }

    if (pathName.startsWith('/api/ideas/') && method === 'DELETE') {
      const id = pathName.split('/')[3];
      const ideas = readCollection('ideas');
      const ideaIndex = ideas.findIndex(i => i.id === id);

      if (ideaIndex !== -1) {
        const deletedIdea = ideas.splice(ideaIndex, 1)[0];
        const success = writeCollection('ideas', ideas);

        if (success) {
          sendJSON(res, 200, {
            success: true,
            message: 'Idea deleted successfully',
            data: deletedIdea
          });
        } else {
          sendJSON(res, 500, {
            success: false,
            error: 'Failed to delete idea'
          });
        }
      } else {
        sendJSON(res, 404, {
          success: false,
          error: 'Idea not found'
        });
      }
      return;
    }

    // Master Business Cases endpoints
    // Get single Master BC
    if (pathName.startsWith('/api/master-bc/') && method === 'GET' && !pathName.endsWith('/link') && !pathName.endsWith('/export')) {
      const id = pathName.split('/')[3];
      const masterBusinessCases = readCollection('masterBusinessCases');
      const businessCases = readCollection('businessCases');
      const masterBC = masterBusinessCases.find(mbc => mbc.id === id);

      if (!masterBC) {
        sendJSON(res, 404, {
          success: false,
          error: 'Master Business Case not found'
        });
        return;
      }

      // Get linked business cases
      const linkedBCs = businessCases.filter(bc =>
        masterBC.linkedBusinessCases && masterBC.linkedBusinessCases.includes(bc.id)
      );

      // Calculate real-time aggregated metrics from linked business cases
      let totalCapex = 0;
      let totalOpex = 0;
      let totalRevenue = 0;
      let totalNPV = 0;
      let totalIRR = 0;
      let totalPaybackPeriod = 0;
      let totalGrossMargin = 0;
      let totalCommercialMargin = 0;

      linkedBCs.forEach(bc => {
        // Sum yearly CAPEX
        if (bc.capex && typeof bc.capex === 'object') {
          totalCapex += Object.values(bc.capex).reduce((sum, val) => sum + (val || 0), 0);
        }

        // Sum yearly OPEX
        if (bc.opex && typeof bc.opex === 'object') {
          totalOpex += Object.values(bc.opex).reduce((sum, val) => sum + (val || 0), 0);
        }

        // Sum yearly Revenue
        if (bc.revenue && typeof bc.revenue === 'object') {
          totalRevenue += Object.values(bc.revenue).reduce((sum, val) => sum + (val || 0), 0);
        }

        // Sum financial metrics
        totalNPV += bc.financialMetrics?.npv || 0;
        totalIRR += bc.financialMetrics?.irr || 0;
        totalPaybackPeriod += bc.financialMetrics?.paybackPeriod || 0;
        totalGrossMargin += bc.financialMetrics?.grossMargin || 0;
        totalCommercialMargin += bc.financialMetrics?.commercialMargin || 0;
      });

      const linkedCount = linkedBCs.length;
      const totalInvestment = totalCapex + totalOpex;

      // Calculate averages
      const avgIRR = linkedCount > 0 ? totalIRR / linkedCount : 0;
      const avgPaybackPeriod = linkedCount > 0 ? totalPaybackPeriod / linkedCount : 0;
      const avgGrossMargin = linkedCount > 0 ? totalGrossMargin / linkedCount : 0;
      const avgCommercialMargin = linkedCount > 0 ? totalCommercialMargin / linkedCount : 0;

      const transformedMasterBC = {
        ...masterBC,
        // Fix field name mismatch
        linkedBCs: masterBC.linkedBusinessCases || [],
        // Real-time calculated aggregated metrics
        aggregatedMetrics: {
          totalCapex: totalCapex,
          totalOpex: totalOpex,
          totalRevenue: totalRevenue,
          totalInvestment: totalInvestment,
          totalNPV: totalNPV,
          avgIRR: avgIRR,
          avgPaybackPeriod: avgPaybackPeriod,
          avgGrossMargin: avgGrossMargin,
          avgCommercialMargin: avgCommercialMargin,
          linkedCount: linkedCount
        }
      };

      sendJSON(res, 200, {
        success: true,
        data: transformedMasterBC
      });
      console.log(`✅ Master BC retrieved: ${masterBC.name}`);
      return;
    }

    // List all Master BCs
    if (pathName === '/api/master-bc' && method === 'GET') {
      const masterBCs = readCollection('masterBusinessCases');
      const businessCases = readCollection('businessCases');
      const programs = readCollection('programs');
      const projects = readCollection('projects');

      // Transform data for frontend compatibility with real-time aggregation
      const transformedMasterBCs = masterBCs.map(mbc => {
        // Get linked business cases
        const linkedBCs = businessCases.filter(bc =>
          mbc.linkedBusinessCases && mbc.linkedBusinessCases.includes(bc.id)
        );

        // Get connected program
        const connectedProgram = programs.find(prog => prog.linkedMasterBusinessCaseId === mbc.id);

        // Get connected projects/epics through the program
        const connectedProjects = connectedProgram ?
          projects.filter(proj =>
            (connectedProgram.linkedProjectIds && connectedProgram.linkedProjectIds.includes(proj.id)) ||
            (connectedProgram.linkedEpicIds && connectedProgram.linkedEpicIds.includes(proj.id))
          ) : [];

        // Calculate real-time aggregated metrics from linked business cases
        let totalCapex = 0;
        let totalOpex = 0;
        let totalRevenue = 0;
        let totalNPV = 0;
        let totalIRR = 0;
        let totalPaybackPeriod = 0;
        let totalGrossMargin = 0;
        let totalCommercialMargin = 0;

        linkedBCs.forEach(bc => {
          // Sum yearly CAPEX
          if (bc.capex && typeof bc.capex === 'object') {
            totalCapex += Object.values(bc.capex).reduce((sum, val) => sum + (val || 0), 0);
          }

          // Sum yearly OPEX
          if (bc.opex && typeof bc.opex === 'object') {
            totalOpex += Object.values(bc.opex).reduce((sum, val) => sum + (val || 0), 0);
          }

          // Sum yearly Revenue
          if (bc.revenue && typeof bc.revenue === 'object') {
            totalRevenue += Object.values(bc.revenue).reduce((sum, val) => sum + (val || 0), 0);
          }

          // Sum financial metrics
          totalNPV += bc.financialMetrics?.npv || 0;
          totalIRR += bc.financialMetrics?.irr || 0;
          totalPaybackPeriod += bc.financialMetrics?.paybackPeriod || 0;
          totalGrossMargin += bc.financialMetrics?.grossMargin || 0;
          totalCommercialMargin += bc.financialMetrics?.commercialMargin || 0;
        });

        const linkedCount = linkedBCs.length;
        const totalInvestment = totalCapex + totalOpex;

        // Calculate averages
        const avgIRR = linkedCount > 0 ? totalIRR / linkedCount : 0;
        const avgPaybackPeriod = linkedCount > 0 ? totalPaybackPeriod / linkedCount : 0;
        const avgGrossMargin = linkedCount > 0 ? totalGrossMargin / linkedCount : 0;
        const avgCommercialMargin = linkedCount > 0 ? totalCommercialMargin / linkedCount : 0;

        return {
          ...mbc,
          // Fix field name mismatch
          linkedBCs: mbc.linkedBusinessCases || [],
          // Connected relationships
          connectedProgram: connectedProgram ? {
            id: connectedProgram.id,
            name: connectedProgram.name,
            owner: connectedProgram.owner,
            status: connectedProgram.status
          } : null,
          connectedProjects: connectedProjects.map(proj => ({
            id: proj.id,
            name: proj.name,
            type: proj.type,
            owner: proj.owner,
            status: proj.status,
            linkedBusinessCases: proj.linkedBusinessCaseIds || []
          })),
          // Business case names for easy reference
          linkedBusinessCaseNames: linkedBCs.map(bc => ({
            id: bc.id,
            name: bc.name,
            businessUnit: bc.businessUnit,
            status: bc.status
          })),
          // Real-time calculated aggregated metrics
          aggregatedMetrics: {
            totalCapex: totalCapex,
            totalOpex: totalOpex,
            totalRevenue: totalRevenue,
            totalInvestment: totalInvestment,
            totalNPV: totalNPV,
            avgIRR: avgIRR,
            avgPaybackPeriod: avgPaybackPeriod,
            avgGrossMargin: avgGrossMargin,
            avgCommercialMargin: avgCommercialMargin,
            linkedCount: linkedCount
          }
        };
      });

      sendJSON(res, 200, {
        success: true,
        data: {
          masterBusinessCases: transformedMasterBCs,
          pagination: {
            page: 1,
            limit: 50,
            total: transformedMasterBCs.length,
            pages: 1
          }
        }
      });
      return;
    }

    if (pathName === '/api/master-bc' && method === 'POST') {
      const body = await parseBody(req);
      const masterBCs = readCollection('masterBusinessCases');

      const newMasterBC = {
        id: generateId(),
        name: body.name,
        description: body.description || '',
        category: body.category || '',
        businessUnits: body.businessUnits || [],
        priority: body.priority || 'Medium',
        linkedBCs: body.linkedBCs || [],
        aggregatedMetrics: body.aggregatedMetrics || {},
        status: body.status || 'draft',
        createdBy: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      masterBCs.push(newMasterBC);
      const success = writeCollection('masterBusinessCases', masterBCs);

      if (success) {
        sendJSON(res, 201, {
          success: true,
          data: newMasterBC
        });
        console.log(`✅ New Master Business Case created: ${newMasterBC.name}`);
      } else {
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to create Master Business Case'
        });
      }
      return;
    }

    // Master BC linking endpoint
    if (pathName.startsWith('/api/master-bc/') && pathName.endsWith('/link') && method === 'PUT') {
      const id = pathName.split('/')[3];
      const body = await parseBody(req);
      const { businessCaseIds, action } = body;

      const masterBCs = readCollection('masterBusinessCases');
      const masterBCIndex = masterBCs.findIndex(mbc => mbc.id === id);

      if (masterBCIndex === -1) {
        sendJSON(res, 404, {
          success: false,
          error: 'Master Business Case not found'
        });
        return;
      }

      const masterBC = masterBCs[masterBCIndex];

      // Initialize linkedBCs if it doesn't exist
      if (!masterBC.linkedBCs) {
        masterBC.linkedBCs = [];
      }

      if (action === 'add' || action === 'link') {
        // Add business cases (avoid duplicates)
        businessCaseIds.forEach(bcId => {
          if (!masterBC.linkedBCs.includes(bcId)) {
            masterBC.linkedBCs.push(bcId);
          }
        });
      } else if (action === 'remove' || action === 'unlink') {
        // Remove business cases
        masterBC.linkedBCs = masterBC.linkedBCs.filter(bcId => !businessCaseIds.includes(bcId));
      } else if (action === 'replace') {
        // Replace all linked business cases
        masterBC.linkedBCs = [...businessCaseIds];
      }

      // Recalculate aggregated metrics
      const businessCases = readCollection('businessCases');
      const linkedBusinessCases = businessCases.filter(bc => masterBC.linkedBCs.includes(bc.id));

      if (linkedBusinessCases.length > 0) {
        const totalCapex = linkedBusinessCases.reduce((sum, bc) => sum + (bc.financialData?.totalCapex || 0), 0);
        const totalOpex = linkedBusinessCases.reduce((sum, bc) => sum + (bc.financialData?.totalOpex || 0), 0);
        const totalNPV = linkedBusinessCases.reduce((sum, bc) => sum + (bc.calculatedMetrics?.npv || 0), 0);

        const validIRRs = linkedBusinessCases
          .map(bc => bc.calculatedMetrics?.irr || 0)
          .filter(irr => irr > 0);
        const avgIRR = validIRRs.length > 0 ? validIRRs.reduce((sum, irr) => sum + irr, 0) / validIRRs.length : 0;

        const validPaybacks = linkedBusinessCases
          .map(bc => bc.calculatedMetrics?.paybackPeriod || 0)
          .filter(pb => pb > 0);
        const avgPayback = validPaybacks.length > 0 ? validPaybacks.reduce((sum, pb) => sum + pb, 0) / validPaybacks.length : 0;

        const validGrossMargins = linkedBusinessCases
          .map(bc => bc.calculatedMetrics?.grossMargin || 0)
          .filter(gm => gm !== 0);
        const avgGrossMargin = validGrossMargins.length > 0 ? validGrossMargins.reduce((sum, gm) => sum + gm, 0) / validGrossMargins.length : 0;

        const validCommercialMargins = linkedBusinessCases
          .map(bc => bc.calculatedMetrics?.commercialMargin || 0)
          .filter(cm => cm !== 0);
        const avgCommercialMargin = validCommercialMargins.length > 0 ? validCommercialMargins.reduce((sum, cm) => sum + cm, 0) / validCommercialMargins.length : 0;

        masterBC.aggregatedMetrics = {
          totalCapex: Math.round(totalCapex),
          totalOpex: Math.round(totalOpex),
          totalInvestment: Math.round(totalCapex + totalOpex),
          totalNPV: Math.round(totalNPV),
          avgIRR: Math.round(avgIRR * 10) / 10,
          avgPaybackPeriod: Math.round(avgPayback * 10) / 10,
          avgGrossMargin: Math.round(avgGrossMargin * 10) / 10,
          avgCommercialMargin: Math.round(avgCommercialMargin * 10) / 10,
          linkedCount: linkedBusinessCases.length
        };
      } else {
        masterBC.aggregatedMetrics = {
          totalCapex: 0,
          totalOpex: 0,
          totalInvestment: 0,
          totalNPV: 0,
          avgIRR: 0,
          avgPaybackPeriod: 0,
          avgGrossMargin: 0,
          avgCommercialMargin: 0,
          linkedCount: 0
        };
      }

      masterBC.updatedAt = new Date().toISOString();
      masterBCs[masterBCIndex] = masterBC;

      const success = writeCollection('masterBusinessCases', masterBCs);

      if (success) {
        sendJSON(res, 200, {
          success: true,
          data: {
            masterBC: masterBC,
            message: `Successfully ${action}ed ${businessCaseIds.length} business case(s)`
          }
        });
        console.log(`✅ Master BC ${masterBC.name}: ${action}ed ${businessCaseIds.length} business case(s)`);
      } else {
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to update Master Business Case'
        });
      }
      return;
    }

    // Master BC UPDATE endpoint
    if (pathName.startsWith('/api/master-bc/') && method === 'PUT' && !pathName.endsWith('/link')) {
      const id = pathName.split('/')[3];
      const body = await parseBody(req);
      const masterBCs = readCollection('masterBusinessCases');
      const masterBCIndex = masterBCs.findIndex(mbc => mbc.id === id);

      if (masterBCIndex !== -1) {
        masterBCs[masterBCIndex] = {
          ...masterBCs[masterBCIndex],
          ...body,
          id: id, // Keep original ID
          updatedAt: new Date().toISOString()
        };

        const success = writeCollection('masterBusinessCases', masterBCs);

        if (success) {
          sendJSON(res, 200, {
            success: true,
            data: masterBCs[masterBCIndex]
          });
          console.log(`✅ Master BC updated: ${masterBCs[masterBCIndex].name}`);
        } else {
          sendJSON(res, 500, {
            success: false,
            error: 'Failed to update Master Business Case'
          });
        }
      } else {
        sendJSON(res, 404, {
          success: false,
          error: 'Master Business Case not found'
        });
      }
      return;
    }

    // Master BC DELETE endpoint
    if (pathName.startsWith('/api/master-bc/') && method === 'DELETE') {
      const id = pathName.split('/')[3];
      const masterBCs = readCollection('masterBusinessCases');
      const masterBCIndex = masterBCs.findIndex(mbc => mbc.id === id);

      if (masterBCIndex !== -1) {
        const deletedMasterBC = masterBCs.splice(masterBCIndex, 1)[0];

        // Also unlink any programs that were linked to this Master BC
        const programs = readCollection('programs');
        const updatedPrograms = programs.map(program => {
          if (program.linkedMasterBC === id) {
            return {
              ...program,
              linkedMasterBC: null,
              updatedAt: new Date().toISOString()
            };
          }
          return program;
        });

        const masterBCSuccess = writeCollection('masterBusinessCases', masterBCs);
        const programsSuccess = writeCollection('programs', updatedPrograms);

        if (masterBCSuccess && programsSuccess) {
          sendJSON(res, 200, {
            success: true,
            data: {
              message: 'Master Business Case deleted successfully',
              deletedMasterBC: deletedMasterBC,
              unlinkedPrograms: updatedPrograms.filter(p => programs.find(orig => orig.id === p.id && orig.linkedMasterBC === id)).length
            }
          });
          console.log(`✅ Master BC deleted: ${deletedMasterBC.name}`);
        } else {
          sendJSON(res, 500, {
            success: false,
            error: 'Failed to delete Master Business Case'
          });
        }
      } else {
        sendJSON(res, 404, {
          success: false,
          error: 'Master Business Case not found'
        });
      }
      return;
    }

    // Relationships endpoint - NEW
    if (pathName === '/api/relationships' && method === 'GET') {
      try {
        const ideas = readCollection('ideas');
        const businessCases = readCollection('businessCases');
        const masterBCs = readCollection('masterBusinessCases');
        const programs = readCollection('programs');
        const projects = readCollection('projects');

        // Build comprehensive relationship data
        const relationships = {
          nodes: [],
          edges: [],
          summary: {
            totalEntities: 0,
            totalConnections: 0,
            entityCounts: {
              ideas: ideas.length,
              businessCases: businessCases.length,
              masterBusinessCases: masterBCs.length,
              programs: programs.length,
              projects: projects.filter(p => p.type === 'project').length,
              epics: projects.filter(p => p.type === 'epic').length
            }
          }
        };

        // Add Ideas as nodes
        ideas.forEach(idea => {
          relationships.nodes.push({
            id: idea.id,
            label: idea.title,
            type: 'idea',
            data: {
              category: idea.category,
              status: idea.status,
              priority: idea.priority,
              estimatedValue: idea.estimatedValue,
              submittedBy: idea.submittedBy
            }
          });
        });

        // Add Business Cases as nodes and create edges to Ideas
        businessCases.forEach(bc => {
          relationships.nodes.push({
            id: bc.id,
            label: bc.name,
            type: 'businessCase',
            data: {
              businessUnit: bc.businessUnit,
              status: bc.status,
              totalCapex: Object.values(bc.capex || {}).reduce((sum, val) => sum + val, 0),
              totalOpex: Object.values(bc.opex || {}).reduce((sum, val) => sum + val, 0),
              totalRevenue: Object.values(bc.revenue || {}).reduce((sum, val) => sum + val, 0),
              irr: bc.financialMetrics?.irr,
              npv: bc.financialMetrics?.npv
            }
          });

          // Create edge from Idea to Business Case
          if (bc.linkedIdeaId) {
            relationships.edges.push({
              source: bc.linkedIdeaId,
              target: bc.id,
              type: 'leads_to',
              label: 'leads to'
            });
          }
        });

        // Add Master Business Cases as nodes and create edges to Business Cases
        masterBCs.forEach(mbc => {
          relationships.nodes.push({
            id: mbc.id,
            label: mbc.name,
            type: 'masterBusinessCase',
            data: {
              businessUnit: mbc.businessUnit,
              status: mbc.status,
              totalCapex: mbc.aggregatedMetrics?.totalCapex,
              totalOpex: mbc.aggregatedMetrics?.totalOpex,
              totalRevenue: mbc.aggregatedMetrics?.totalRevenue,
              avgIrr: mbc.aggregatedMetrics?.avgIrr,
              totalNpv: mbc.aggregatedMetrics?.totalNpv,
              linkedBusinessCasesCount: mbc.linkedBusinessCases?.length || 0
            }
          });

          // Create edges from Business Cases to Master Business Case
          if (mbc.linkedBusinessCases) {
            mbc.linkedBusinessCases.forEach(bcId => {
              relationships.edges.push({
                source: bcId,
                target: mbc.id,
                type: 'connects_to',
                label: 'connects to'
              });
            });
          }
        });

        // Add Programs as nodes and create edges to Master Business Cases
        programs.forEach(program => {
          relationships.nodes.push({
            id: program.id,
            label: program.name,
            type: 'program',
            data: {
              businessUnit: program.businessUnit,
              status: program.status,
              budget: program.budget,
              owner: program.owner,
              startDate: program.startDate,
              endDate: program.endDate,
              linkedProjectsCount: program.linkedProjectIds?.length || 0,
              linkedEpicsCount: program.linkedEpicIds?.length || 0
            }
          });

          // Create edge from Master Business Case to Program
          if (program.linkedMasterBusinessCaseId) {
            relationships.edges.push({
              source: program.linkedMasterBusinessCaseId,
              target: program.id,
              type: 'connects_to',
              label: 'connects to'
            });
          }
        });

        // Add Projects and Epics as nodes and create bidirectional edges to Programs
        projects.forEach(project => {
          relationships.nodes.push({
            id: project.id,
            label: project.name,
            type: project.type, // 'project' or 'epic'
            data: {
              businessUnit: project.businessUnit,
              status: project.status,
              budget: project.budget,
              owner: project.owner,
              startDate: project.startDate,
              endDate: project.endDate,
              canUpdateProgram: project.canUpdateProgram,
              linkedBusinessCasesCount: project.linkedBusinessCaseIds?.length || 0,
              milestonesCount: project.milestones?.length || 0
            }
          });

          // Create bidirectional edge between Program and Project/Epic
          if (project.linkedProgramId) {
            // Program includes Project/Epic
            relationships.edges.push({
              source: project.linkedProgramId,
              target: project.id,
              type: 'includes',
              label: 'includes'
            });

            // Project/Epic can update Program (bidirectional)
            if (project.canUpdateProgram) {
              relationships.edges.push({
                source: project.id,
                target: project.linkedProgramId,
                type: 'updates',
                label: 'updates'
              });
            }
          }

          // Create edges from Projects/Epics to Business Cases
          if (project.linkedBusinessCaseIds) {
            project.linkedBusinessCaseIds.forEach(bcId => {
              relationships.edges.push({
                source: project.id,
                target: bcId,
                type: 'implements',
                label: 'implements'
              });
            });
          }
        });

        // Calculate summary statistics
        relationships.summary.totalEntities = relationships.nodes.length;
        relationships.summary.totalConnections = relationships.edges.length;

        console.log(`📊 Relationships endpoint: ${relationships.summary.totalEntities} entities, ${relationships.summary.totalConnections} connections`);

        sendJSON(res, 200, {
          success: true,
          data: relationships
        });
        return;

      } catch (error) {
        console.error('Error in relationships endpoint:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to load relationship data'
        });
        return;
      }
    }

    // Dashboard endpoint
    if (pathName === '/api/dashboard' && method === 'GET') {
      try {
        const businessCases = readCollection('businessCases');
        const projects = readCollection('projects');
        const programs = readCollection('programs');
        const masterBCs = readCollection('masterBusinessCases');
        const ideas = readCollection('ideas');

        // Calculate KPIs
        const totalBusinessCases = businessCases.length;
        const totalProjects = projects.length;
        const totalPrograms = programs.length;
        const totalMasterBCs = masterBCs.length;
        const totalIdeas = ideas.length;

        // Calculate financial metrics
        let totalInvestment = 0;
        let totalNPV = 0;
        let totalIRR = 0;
        let validIRRCount = 0;
        let activeProjects = 0;
        let completedProjects = 0;

        businessCases.forEach(bc => {
          // Calculate total CAPEX and OPEX from yearly breakdown
          let bcCapex = 0;
          let bcOpex = 0;

          if (bc.capex && typeof bc.capex === 'object') {
            bcCapex = Object.values(bc.capex).reduce((sum, val) => sum + (val || 0), 0);
          }

          if (bc.opex && typeof bc.opex === 'object') {
            bcOpex = Object.values(bc.opex).reduce((sum, val) => sum + (val || 0), 0);
          }

          totalInvestment += bcCapex + bcOpex;

          // Use financialMetrics instead of calculatedMetrics
          if (bc.financialMetrics) {
            if (bc.financialMetrics.npv) {
              totalNPV += bc.financialMetrics.npv;
            }
            if (bc.financialMetrics.irr) {
              totalIRR += bc.financialMetrics.irr;
              validIRRCount++;
            }
          }
        });

        projects.forEach(project => {
          if (project.status === 'active' || project.status === 'in_progress') {
            activeProjects++;
          } else if (project.status === 'completed') {
            completedProjects++;
          }
        });

        const avgIRR = validIRRCount > 0 ? totalIRR / validIRRCount : 0;

        // Calculate business unit analysis
        const businessUnitAnalysis = {};
        businessCases.forEach(bc => {
          const bu = bc.businessUnit || 'Uncategorized';
          if (!businessUnitAnalysis[bu]) {
            businessUnitAnalysis[bu] = {
              name: bu,
              totalInvestment: 0,
              totalNPV: 0,
              avgIRR: 0,
              irrSum: 0,
              irrCount: 0,
              businessCaseCount: 0
            };
          }

          // Calculate investment for this BC
          let bcCapex = 0;
          let bcOpex = 0;
          if (bc.capex) bcCapex = Object.values(bc.capex).reduce((sum, val) => sum + (val || 0), 0);
          if (bc.opex) bcOpex = Object.values(bc.opex).reduce((sum, val) => sum + (val || 0), 0);

          businessUnitAnalysis[bu].totalInvestment += bcCapex + bcOpex;
          businessUnitAnalysis[bu].businessCaseCount++;

          if (bc.financialMetrics) {
            if (bc.financialMetrics.npv) {
              businessUnitAnalysis[bu].totalNPV += bc.financialMetrics.npv;
            }
            if (bc.financialMetrics.irr) {
              businessUnitAnalysis[bu].irrSum += bc.financialMetrics.irr;
              businessUnitAnalysis[bu].irrCount++;
            }
          }
        });

        // Calculate average IRR for each business unit
        Object.values(businessUnitAnalysis).forEach(bu => {
          bu.avgIRR = bu.irrCount > 0 ? bu.irrSum / bu.irrCount : 0;
        });

        const dashboardData = {
          kpis: {
            totalBusinessCases: {
              value: totalBusinessCases,
              change: '+12%',
              trend: 'up'
            },
            totalInvestment: {
              value: totalInvestment,
              change: '+8%',
              trend: 'up'
            },
            averageIRR: {
              value: avgIRR,
              change: '+2.3%',
              trend: 'up'
            },
            totalNPV: {
              value: totalNPV,
              change: '+15%',
              trend: 'up'
            },
            activeProjects: {
              value: activeProjects,
              change: '+5',
              trend: 'up'
            },
            completedProjects: {
              value: completedProjects,
              change: '+3',
              trend: 'up'
            }
          },
          charts: {
            investmentByBusinessUnit: Object.values(businessUnitAnalysis),
            roiTrends: [],
            projectStatusDistribution: [],
            financialMetricsComparison: []
          },
          businessUnits: {
            summary: {
              totalPortfolioValue: totalInvestment,
              activeBusinessUnits: Object.keys(businessUnitAnalysis).length,
              totalNPV: totalNPV,
              avgIRR: avgIRR
            },
            details: Object.values(businessUnitAnalysis)
          },
          recentActivity: [
            {
              id: 1,
              type: 'business_case_created',
              title: 'New Business Case Created',
              description: 'Digital Transformation Initiative business case was created',
              user: 'John Doe',
              timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
            },
            {
              id: 2,
              type: 'project_updated',
              title: 'Project Updated',
              description: 'Cloud Migration project milestones were updated',
              user: 'Jane Smith',
              timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
            },
            {
              id: 3,
              type: 'master_bc_approved',
              title: 'Master Business Case Approved',
              description: 'IT Infrastructure Master BC received final approval',
              user: 'Mike Johnson',
              timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
            },
            {
              id: 4,
              type: 'export_completed',
              title: 'Export Completed',
              description: 'Q4 Business Cases exported to Excel successfully',
              user: 'Sarah Wilson',
              timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString()
            },
            {
              id: 5,
              type: 'program_created',
              title: 'New Program Created',
              description: 'Digital Innovation Program was created with 5 linked projects',
              user: 'David Brown',
              timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()
            }
          ],
          insights: [
            'Portfolio performance is trending upward with 15% NPV growth',
            'Digital transformation initiatives show highest ROI potential',
            'Consider consolidating similar business cases for better resource allocation'
          ]
        };

        sendJSON(res, 200, {
          success: true,
          data: dashboardData
        });
        console.log('✅ Dashboard data retrieved successfully');
      } catch (error) {
        console.error('Error retrieving dashboard data:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to retrieve dashboard data'
        });
      }
      return;
    }

    // System integrity validation endpoint
    if (pathName === '/api/system/integrity/validate' && method === 'GET') {
      try {
        const businessCases = readCollection('businessCases');
        const projects = readCollection('projects');
        const programs = readCollection('programs');
        const masterBCs = readCollection('masterBusinessCases');
        const ideas = readCollection('ideas');

        const validation = {
          success: true,
          timestamp: new Date().toISOString(),
          summary: {
            totalBusinessCases: businessCases.length,
            totalProjects: projects.length,
            totalPrograms: programs.length,
            totalMasterBCs: masterBCs.length,
            totalIdeas: ideas.length
          },
          integrity: {
            orphanedBusinessCases: 0,
            orphanedProjects: 0,
            brokenLinks: 0,
            duplicateIds: 0
          },
          status: 'healthy'
        };

        sendJSON(res, 200, validation);
        console.log('✅ System integrity validation completed');
      } catch (error) {
        console.error('Error during system integrity validation:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'System integrity validation failed'
        });
      }
      return;
    }

    // 404 for unknown routes
    sendJSON(res, 404, {
      success: false,
      error: 'Route not found'
    });

  } catch (error) {
    console.error('Server error:', error);
    sendJSON(res, 500, {
      success: false,
      error: 'Internal server error'
    });
  }
});

// Start server
function startServer() {
  initializeStorage();
  
  server.listen(PORT, () => {
    console.log(`🚀 Business Case Management Server running on port ${PORT}`);
    console.log(`📊 Dashboard: http://localhost:${PORT}/health`);
    console.log(`🗄️  Database: File-based storage (MongoDB simulation)`);
    console.log(`📁 Data directory: ${DATA_DIR}`);
    console.log(`🔐 Demo credentials:`);
    console.log(`   Admin: <EMAIL> / password123`);
    console.log(`   Analyst: <EMAIL> / password123`);
    console.log(`   Gyanesh (Admin): <EMAIL> / gyanesh123`);
    console.log(`\n📡 Business Case Management API Endpoints:`);
    console.log(`   🔐 Authentication:`);
    console.log(`      POST /api/auth/login - User authentication`);
    console.log(`   📊 Dashboard:`);
    console.log(`      GET  /api/dashboard - Get dashboard data and KPIs`);
    console.log(`      GET  /api/relationships - Get comprehensive relationship data for visualization`);
    console.log(`   📊 Business Cases:`);
    console.log(`      GET    /api/business-cases - List all business cases`);
    console.log(`      GET    /api/business-cases/:id - Get single business case`);
    console.log(`      POST   /api/business-cases - Create new business case`);
    console.log(`      PUT    /api/business-cases/:id - Update business case`);
    console.log(`      DELETE /api/business-cases/:id - Delete business case`);
    console.log(`      GET    /api/business-cases/stats - Get statistics`);
    console.log(`   🎯 Programs:`);
    console.log(`      GET  /api/programs - List all programs`);
    console.log(`      POST /api/programs - Create new program`);
    console.log(`      GET  /api/programs/:id - Get single program`);
    console.log(`      PUT  /api/programs/:id - Update program`);
    console.log(`      DELETE /api/programs/:id - Delete program`);
    console.log(`      PUT  /api/programs/:id/link-master-bc - Link/unlink program to Master BC`);
    console.log(`   📋 Projects & Epics:`);
    console.log(`      GET  /api/projects - List all projects/epics`);
    console.log(`      POST /api/projects - Create new project/epic`);
    console.log(`   💡 Ideas:`);
    console.log(`      GET  /api/ideas - List all ideas`);
    console.log(`      POST /api/ideas - Create new idea`);
    console.log(`   🏢 Master Business Cases:`);
    console.log(`      GET    /api/master-bc - List all master business cases`);
    console.log(`      GET    /api/master-bc/:id - Get single master business case`);
    console.log(`      POST   /api/master-bc - Create new master business case`);
    console.log(`      PUT    /api/master-bc/:id - Update master business case`);
    console.log(`      DELETE /api/master-bc/:id - Delete master business case`);
    console.log(`      PUT    /api/master-bc/:id/link - Link/unlink business cases`);
  });
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('\nSIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

// Start the server
startServer();
