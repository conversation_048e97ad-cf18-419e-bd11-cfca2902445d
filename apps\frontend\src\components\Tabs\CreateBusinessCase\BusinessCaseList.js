import React, { useState } from 'react';

const BusinessCaseList = ({ businessCases, onEdit, onDelete, onView, onExport }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [businessUnitFilter, setBusinessUnitFilter] = useState('');
  const [sortBy, setSortBy] = useState('updatedAt');
  const [sortOrder, setSortOrder] = useState('desc');

  // Filter and sort business cases
  const filteredBusinessCases = businessCases
    .filter(bc => {
      const matchesSearch = bc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           bc.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = !statusFilter || bc.status === statusFilter;
      const matchesBusinessUnit = !businessUnitFilter || bc.businessUnit === businessUnitFilter;
      
      return matchesSearch && matchesStatus && matchesBusinessUnit;
    })
    .sort((a, b) => {
      const aValue = a[sortBy];
      const bValue = b[sortBy];
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

  const getStatusBadge = (status) => {
    const statusConfig = {
      draft: { color: 'bg-gray-100 text-gray-800', icon: 'fas fa-edit' },
      'under-review': { color: 'bg-yellow-100 text-yellow-800', icon: 'fas fa-clock' },
      approved: { color: 'bg-green-100 text-green-800', icon: 'fas fa-check-circle' },
      rejected: { color: 'bg-red-100 text-red-800', icon: 'fas fa-times-circle' },
      'on-hold': { color: 'bg-orange-100 text-orange-800', icon: 'fas fa-pause-circle' }
    };

    const config = statusConfig[status] || statusConfig.draft;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <i className={`${config.icon} mr-1`}></i>
        {status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </span>
    );
  };

  const calculateROI = (bc) => {
    const totalInvestment = (bc.totalCapex || 0) + (bc.totalOpex || 0);
    const totalRevenue = bc.totalRevenue || 0;
    
    if (totalInvestment === 0) return 'N/A';
    
    const roi = ((totalRevenue - totalInvestment) / totalInvestment) * 100;
    return `${roi.toFixed(1)}%`;
  };

  const formatCurrency = (amount) => {
    if (!amount) return '$0';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const businessUnits = [...new Set(businessCases.map(bc => bc.businessUnit).filter(Boolean))];
  const statuses = [...new Set(businessCases.map(bc => bc.status).filter(Boolean))];

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Search business cases..."
              />
              <i className="fas fa-search absolute left-3 top-3 text-gray-400"></i>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Statuses</option>
              {statuses.map(status => (
                <option key={status} value={status}>
                  {status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Business Unit</label>
            <select
              value={businessUnitFilter}
              onChange={(e) => setBusinessUnitFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Units</option>
              {businessUnits.map(unit => (
                <option key={unit} value={unit}>{unit}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600">
              Showing {filteredBusinessCases.length} of {businessCases.length} business cases
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-600">Sort by:</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded text-sm"
            >
              <option value="updatedAt">Last Updated</option>
              <option value="name">Name</option>
              <option value="status">Status</option>
              <option value="businessUnit">Business Unit</option>
              <option value="totalRevenue">Revenue</option>
            </select>
            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="px-2 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50"
            >
              <i className={`fas fa-sort-${sortOrder === 'asc' ? 'up' : 'down'}`}></i>
            </button>
          </div>
        </div>
      </div>

      {/* Business Cases Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredBusinessCases.map(businessCase => (
          <div key={businessCase.id} className="bg-white rounded-lg border border-gray-200 hover:shadow-lg transition-shadow duration-200">
            <div className="p-6">
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1 line-clamp-2">
                    {businessCase.name}
                  </h3>
                  <p className="text-sm text-gray-600">{businessCase.businessUnit}</p>
                </div>
                <div className="ml-3">
                  {getStatusBadge(businessCase.status)}
                </div>
              </div>

              {/* Description */}
              <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                {businessCase.description}
              </p>

              {/* Financial Summary */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center bg-blue-50 rounded-lg p-3">
                  <div className="text-lg font-semibold text-blue-600">
                    {formatCurrency(businessCase.totalRevenue)}
                  </div>
                  <div className="text-xs text-blue-600">Revenue</div>
                </div>
                <div className="text-center bg-green-50 rounded-lg p-3">
                  <div className="text-lg font-semibold text-green-600">
                    {calculateROI(businessCase)}
                  </div>
                  <div className="text-xs text-green-600">ROI</div>
                </div>
              </div>

              {/* Timeline */}
              <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                <span>
                  <i className="fas fa-calendar mr-1"></i>
                  {businessCase.startYear} - {businessCase.endYear}
                </span>
                <span>
                  <i className="fas fa-clock mr-1"></i>
                  {formatDate(businessCase.updatedAt)}
                </span>
              </div>

              {/* Tags */}
              {businessCase.tags && businessCase.tags.length > 0 && (
                <div className="mb-4">
                  <div className="flex flex-wrap gap-1">
                    {businessCase.tags.slice(0, 3).map((tag, index) => (
                      <span key={index} className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                        {tag}
                      </span>
                    ))}
                    {businessCase.tags.length > 3 && (
                      <span className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                        +{businessCase.tags.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* Promoted from Idea indicator */}
              {businessCase.promotedFromIdea && (
                <div className="mb-4">
                  <span className="inline-flex items-center text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
                    <i className="fas fa-lightbulb mr-1"></i>
                    Promoted from Idea
                  </span>
                </div>
              )}

              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="flex space-x-2">
                  <button
                    onClick={() => onView(businessCase)}
                    className="px-3 py-1 text-blue-600 hover:bg-blue-50 rounded text-sm"
                    title="View Details"
                  >
                    <i className="fas fa-eye"></i>
                  </button>
                  <button
                    onClick={() => onEdit(businessCase)}
                    className="px-3 py-1 text-gray-600 hover:bg-gray-50 rounded text-sm"
                    title="Edit"
                  >
                    <i className="fas fa-edit"></i>
                  </button>
                  <button
                    onClick={() => onExport(businessCase)}
                    className="px-3 py-1 text-green-600 hover:bg-green-50 rounded text-sm"
                    title="Export"
                  >
                    <i className="fas fa-download"></i>
                  </button>
                </div>
                <button
                  onClick={() => onDelete(businessCase)}
                  className="px-3 py-1 text-red-600 hover:bg-red-50 rounded text-sm"
                  title="Delete"
                >
                  <i className="fas fa-trash"></i>
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredBusinessCases.length === 0 && (
        <div className="text-center py-12">
          <i className="fas fa-search text-4xl text-gray-400 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No business cases found</h3>
          <p className="text-gray-600">
            {searchTerm || statusFilter || businessUnitFilter
              ? 'Try adjusting your search criteria or filters'
              : 'No business cases have been created yet'
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default BusinessCaseList;
