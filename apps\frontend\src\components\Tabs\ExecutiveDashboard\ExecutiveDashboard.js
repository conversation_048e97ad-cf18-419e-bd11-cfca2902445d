import React, { useState, useEffect } from 'react';
import { useData } from '../../../context/DataContext';
import OverviewSection from './OverviewSection';
import PortfolioAnalysis from './PortfolioAnalysis';
import FinancialPerformance from './FinancialPerformance';
import RiskCompliance from './RiskCompliance';
import './ExecutiveDashboard.css';

const ExecutiveDashboard = () => {
  const {
    dashboardData,
    masterBusinessCases,
    programs,
    businessCases,
    projects,
    loading,
    loadDashboardData,
    fetchMasterBusinessCases,
    fetchPrograms,
    fetchBusinessCases,
    fetchProjects
  } = useData();

  const [activeView, setActiveView] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    // Load all data when component mounts
    const loadAllData = async () => {
      try {
        await Promise.all([
          loadDashboardData(),
          fetchMasterBusinessCases(),
          fetchPrograms(),
          fetchBusinessCases(),
          fetchProjects()
        ]);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      }
    };

    loadAllData();
  }, [loadDashboardData, fetchMasterBusinessCases, fetchPrograms, fetchBusinessCases, fetchProjects]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        loadDashboardData(),
        fetchMasterBusinessCases(),
        fetchPrograms(),
        fetchBusinessCases(),
        fetchProjects()
      ]);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const views = [
    { 
      id: 'overview', 
      name: 'Overview', 
      icon: 'fas fa-tachometer-alt',
      description: 'Key metrics and recent activity'
    },
    { 
      id: 'portfolio', 
      name: 'Portfolio Analysis', 
      icon: 'fas fa-chart-pie',
      description: 'Master Business Cases and Programs analysis'
    },
    { 
      id: 'financial', 
      name: 'Financial Performance', 
      icon: 'fas fa-chart-line',
      description: 'Financial metrics and trends'
    },
    { 
      id: 'risk', 
      name: 'Risk & Compliance', 
      icon: 'fas fa-shield-alt',
      description: 'Risk assessment and compliance status'
    }
  ];

  if (loading.dashboard && !dashboardData.kpis) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <p className="text-gray-600">Loading executive dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="executive-dashboard">
      {/* Header */}
      <div className="dashboard-header">
        <div className="header-content">
          <h2 className="dashboard-title">Executive Dashboard</h2>
          <p className="dashboard-subtitle">
            Strategic insights and portfolio performance overview
          </p>
        </div>
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="refresh-btn"
        >
          <i className={`fas fa-sync-alt ${refreshing ? 'fa-spin' : ''}`}></i>
          Refresh
        </button>
      </div>

      {/* View Navigation */}
      <div className="view-navigation">
        {views.map(view => (
          <button
            key={view.id}
            onClick={() => setActiveView(view.id)}
            className={`view-btn ${activeView === view.id ? 'active' : ''}`}
            title={view.description}
          >
            <i className={view.icon}></i>
            <span className="view-name">{view.name}</span>
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="dashboard-content">
        {activeView === 'overview' && (
          <OverviewSection 
            dashboardData={dashboardData}
            onViewChange={setActiveView}
          />
        )}
        
        {activeView === 'portfolio' && (
          <PortfolioAnalysis
            masterBusinessCases={masterBusinessCases}
            programs={programs}
            businessCases={businessCases}
            projects={projects}
            onViewChange={setActiveView}
          />
        )}
        
        {activeView === 'financial' && (
          <FinancialPerformance 
            dashboardData={dashboardData}
            masterBusinessCases={masterBusinessCases}
            businessCases={businessCases}
          />
        )}
        
        {activeView === 'risk' && (
          <RiskCompliance 
            masterBusinessCases={masterBusinessCases}
            programs={programs}
            dashboardData={dashboardData}
          />
        )}
      </div>
    </div>
  );
};

export default ExecutiveDashboard;
