@echo off
title Master Business Case Management - Dependency Installer
color 0C

echo ========================================
echo  Master Business Case Management
echo  Dependency Installer
echo ========================================
echo.

echo [INFO] This will install all required dependencies for the application.
echo [INFO] This process may take 3-5 minutes depending on your internet speed.
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo.
    echo Please install Node.js first:
    echo 1. Go to: https://nodejs.org/
    echo 2. Download the LTS version
    echo 3. Install and restart your computer
    echo 4. Run this installer again
    echo.
    echo [INFO] Press any key to exit...
    pause >nul
    exit /b 1
)

echo [SUCCESS] Node.js is installed!
echo [INFO] Node.js version:
node --version
echo [INFO] npm version:
npm --version
echo.

echo [INFO] Checking project structure...

REM Check if we're in the right directory
if not exist "apps\frontend\package.json" (
    echo [ERROR] Frontend directory not found
    echo [INFO] Make sure you're running this from the spm project root directory
    echo [INFO] The directory should contain 'apps\frontend' and 'apps\backend' folders
    echo.
    echo [INFO] Press any key to exit...
    pause >nul
    exit /b 1
)

if not exist "apps\backend\package.json" (
    echo [WARNING] Backend directory not found
    echo [INFO] Only frontend dependencies will be installed
    echo.
)

echo [SUCCESS] Project structure is valid!
echo.

echo [INFO] Installing Frontend Dependencies...
echo [INFO] Location: apps\frontend
cd /d "%~dp0apps\frontend"

if exist "node_modules" (
    echo [INFO] Removing existing node_modules...
    rmdir /s /q node_modules
)

echo [INFO] Running npm install for frontend...
npm install

if errorlevel 1 (
    echo [ERROR] Failed to install frontend dependencies
    echo [INFO] Please check your internet connection and try again
    echo [INFO] Press any key to exit...
    pause >nul
    exit /b 1
)

echo [SUCCESS] Frontend dependencies installed successfully!
echo.

REM Install backend dependencies if backend exists
if exist "%~dp0apps\backend\package.json" (
    echo [INFO] Installing Backend Dependencies...
    echo [INFO] Location: apps\backend
    cd /d "%~dp0apps\backend"
    
    if exist "node_modules" (
        echo [INFO] Removing existing backend node_modules...
        rmdir /s /q node_modules
    )
    
    echo [INFO] Running npm install for backend...
    npm install
    
    if errorlevel 1 (
        echo [ERROR] Failed to install backend dependencies
        echo [INFO] Frontend is still functional without backend
        echo [INFO] Press any key to continue...
        pause >nul
    ) else (
        echo [SUCCESS] Backend dependencies installed successfully!
    )
    echo.
)

cd /d "%~dp0"

echo ========================================
echo  INSTALLATION COMPLETE!
echo ========================================
echo.
echo [SUCCESS] All dependencies have been installed successfully!
echo.
echo Next steps:
echo 1. Double-click 'START_APPLICATION.bat' to launch the application
echo 2. Or double-click 'start-frontend.bat' for frontend only
echo.
echo The application will be available at:
echo - Frontend: http://localhost:3000
echo - Backend:  http://localhost:5000 (if installed)
echo.
echo [INFO] Press any key to exit...
pause >nul
