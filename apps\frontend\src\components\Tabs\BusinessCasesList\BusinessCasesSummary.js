import React from 'react';

const BusinessCasesSummary = ({ businessCases, filteredCount }) => {
  // Calculate summary statistics
  const totalBusinessCases = businessCases.length;
  const filteredBusinessCases = filteredCount;
  
  const totalInvestment = businessCases.reduce((sum, bc) => 
    sum + (bc.totalCapex || 0) + (bc.totalOpex || 0), 0
  );

  const totalRevenue = businessCases.reduce((sum, bc) => 
    sum + (bc.totalRevenue || 0), 0
  );

  const averageROI = businessCases.length > 0 
    ? businessCases.reduce((sum, bc) => {
        const investment = (bc.totalCapex || 0) + (bc.totalOpex || 0);
        const revenue = bc.totalRevenue || 0;
        const roi = investment > 0 ? ((revenue - investment) / investment) * 100 : 0;
        return sum + roi;
      }, 0) / businessCases.length
    : 0;

  const statusCounts = businessCases.reduce((acc, bc) => {
    acc[bc.status] = (acc[bc.status] || 0) + 1;
    return acc;
  }, {});

  const approvedCount = statusCounts.approved || 0;
  const approvalRate = totalBusinessCases > 0 ? (approvedCount / totalBusinessCases) * 100 : 0;

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
      {/* Total Business Cases */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-briefcase text-blue-600"></i>
            </div>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-600">Total Cases</p>
            <p className="text-xl font-bold text-gray-900">{totalBusinessCases}</p>
            {filteredBusinessCases !== totalBusinessCases && (
              <p className="text-xs text-gray-500">({filteredBusinessCases} filtered)</p>
            )}
          </div>
        </div>
      </div>

      {/* Total Investment */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-dollar-sign text-green-600"></i>
            </div>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-600">Investment</p>
            <p className="text-xl font-bold text-gray-900">{formatCurrency(totalInvestment)}</p>
          </div>
        </div>
      </div>

      {/* Total Revenue */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-chart-line text-purple-600"></i>
            </div>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-600">Revenue</p>
            <p className="text-xl font-bold text-gray-900">{formatCurrency(totalRevenue)}</p>
          </div>
        </div>
      </div>

      {/* Average ROI */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-percentage text-orange-600"></i>
            </div>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-600">Avg ROI</p>
            <p className={`text-xl font-bold ${
              averageROI > 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {formatPercentage(averageROI)}
            </p>
          </div>
        </div>
      </div>

      {/* Approval Rate */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-check-circle text-emerald-600"></i>
            </div>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-600">Approval Rate</p>
            <p className="text-xl font-bold text-emerald-600">{formatPercentage(approvalRate)}</p>
            <p className="text-xs text-gray-500">{approvedCount} approved</p>
          </div>
        </div>
      </div>

      {/* Status Breakdown */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center mb-2">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-tasks text-indigo-600"></i>
            </div>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-600">Status</p>
            <p className="text-xl font-bold text-gray-900">{Object.keys(statusCounts).length}</p>
          </div>
        </div>
        <div className="space-y-1">
          {Object.entries(statusCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([status, count]) => {
              const statusConfig = {
                draft: { color: 'text-gray-600', bg: 'bg-gray-100' },
                'under-review': { color: 'text-yellow-600', bg: 'bg-yellow-100' },
                approved: { color: 'text-green-600', bg: 'bg-green-100' },
                rejected: { color: 'text-red-600', bg: 'bg-red-100' },
                'on-hold': { color: 'text-orange-600', bg: 'bg-orange-100' }
              };
              
              const config = statusConfig[status] || { color: 'text-gray-600', bg: 'bg-gray-100' };
              
              return (
                <div key={status} className="flex items-center justify-between text-xs">
                  <span className={`capitalize ${config.color}`}>
                    {status.replace('-', ' ')}
                  </span>
                  <span className={`px-2 py-1 rounded-full ${config.bg} ${config.color} font-medium`}>
                    {count}
                  </span>
                </div>
              );
            })}
        </div>
      </div>
    </div>
  );
};

export default BusinessCasesSummary;
