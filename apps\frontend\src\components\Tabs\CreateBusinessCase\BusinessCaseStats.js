import React from 'react';

const BusinessCaseStats = ({ businessCases }) => {
  // Calculate statistics
  const totalBusinessCases = businessCases.length;
  
  const statusCounts = businessCases.reduce((acc, bc) => {
    acc[bc.status] = (acc[bc.status] || 0) + 1;
    return acc;
  }, {});

  const businessUnitCounts = businessCases.reduce((acc, bc) => {
    if (bc.businessUnit) {
      acc[bc.businessUnit] = (acc[bc.businessUnit] || 0) + 1;
    }
    return acc;
  }, {});

  const totalInvestment = businessCases.reduce((sum, bc) => 
    sum + (bc.totalCapex || 0) + (bc.totalOpex || 0), 0
  );

  const totalRevenue = businessCases.reduce((sum, bc) => 
    sum + (bc.totalRevenue || 0), 0
  );

  const averageROI = businessCases.length > 0 
    ? businessCases.reduce((sum, bc) => {
        const investment = (bc.totalCapex || 0) + (bc.totalOpex || 0);
        const revenue = bc.totalRevenue || 0;
        const roi = investment > 0 ? ((revenue - investment) / investment) * 100 : 0;
        return sum + roi;
      }, 0) / businessCases.length
    : 0;

  const promotedFromIdeas = businessCases.filter(bc => bc.promotedFromIdea).length;

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`;
  };

  const getStatusColor = (status) => {
    const colors = {
      draft: 'text-gray-600',
      'under-review': 'text-yellow-600',
      approved: 'text-green-600',
      rejected: 'text-red-600',
      'on-hold': 'text-orange-600'
    };
    return colors[status] || 'text-gray-600';
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Total Business Cases */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-briefcase text-xl text-blue-600"></i>
            </div>
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-600">Total Business Cases</p>
            <p className="text-2xl font-bold text-gray-900">{totalBusinessCases}</p>
          </div>
        </div>
        
        {/* Status Breakdown */}
        <div className="mt-4 space-y-2">
          {Object.entries(statusCounts).map(([status, count]) => (
            <div key={status} className="flex items-center justify-between text-sm">
              <span className={`capitalize ${getStatusColor(status)}`}>
                {status.replace('-', ' ')}
              </span>
              <span className="font-medium text-gray-900">{count}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Financial Overview */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-dollar-sign text-xl text-green-600"></i>
            </div>
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-600">Total Investment</p>
            <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalInvestment)}</p>
          </div>
        </div>
        
        <div className="mt-4 space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Expected Revenue</span>
            <span className="font-medium text-green-600">{formatCurrency(totalRevenue)}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Average ROI</span>
            <span className="font-medium text-blue-600">{formatPercentage(averageROI)}</span>
          </div>
        </div>
      </div>

      {/* Business Unit Distribution */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-building text-xl text-purple-600"></i>
            </div>
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-600">Business Units</p>
            <p className="text-2xl font-bold text-gray-900">{Object.keys(businessUnitCounts).length}</p>
          </div>
        </div>
        
        <div className="mt-4 space-y-2 max-h-24 overflow-y-auto">
          {Object.entries(businessUnitCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 4)
            .map(([unit, count]) => (
              <div key={unit} className="flex items-center justify-between text-sm">
                <span className="text-gray-600 truncate">{unit}</span>
                <span className="font-medium text-gray-900">{count}</span>
              </div>
            ))}
          {Object.keys(businessUnitCounts).length > 4 && (
            <div className="text-xs text-gray-500 text-center">
              +{Object.keys(businessUnitCounts).length - 4} more
            </div>
          )}
        </div>
      </div>

      {/* Idea Promotion Stats */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-lightbulb text-xl text-orange-600"></i>
            </div>
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-600">Promoted from Ideas</p>
            <p className="text-2xl font-bold text-gray-900">{promotedFromIdeas}</p>
          </div>
        </div>
        
        <div className="mt-4 space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Promotion Rate</span>
            <span className="font-medium text-orange-600">
              {totalBusinessCases > 0 ? formatPercentage((promotedFromIdeas / totalBusinessCases) * 100) : '0%'}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Direct Creation</span>
            <span className="font-medium text-gray-600">
              {totalBusinessCases - promotedFromIdeas}
            </span>
          </div>
        </div>
      </div>

      {/* Recent Activity Chart */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 md:col-span-2">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          <i className="fas fa-chart-bar mr-2 text-blue-600"></i>
          Status Distribution
        </h3>
        
        <div className="space-y-3">
          {Object.entries(statusCounts).map(([status, count]) => {
            const percentage = totalBusinessCases > 0 ? (count / totalBusinessCases) * 100 : 0;
            const statusConfig = {
              draft: { color: 'bg-gray-500', label: 'Draft' },
              'under-review': { color: 'bg-yellow-500', label: 'Under Review' },
              approved: { color: 'bg-green-500', label: 'Approved' },
              rejected: { color: 'bg-red-500', label: 'Rejected' },
              'on-hold': { color: 'bg-orange-500', label: 'On Hold' }
            };
            
            const config = statusConfig[status] || { color: 'bg-gray-500', label: status };
            
            return (
              <div key={status} className="flex items-center">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">{config.label}</span>
                    <span className="text-sm text-gray-600">{count} ({percentage.toFixed(1)}%)</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${config.color}`}
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Top Business Units */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 md:col-span-2">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          <i className="fas fa-building mr-2 text-purple-600"></i>
          Top Business Units
        </h3>
        
        <div className="space-y-3">
          {Object.entries(businessUnitCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([unit, count]) => {
              const percentage = totalBusinessCases > 0 ? (count / totalBusinessCases) * 100 : 0;
              
              return (
                <div key={unit} className="flex items-center">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-gray-700">{unit}</span>
                      <span className="text-sm text-gray-600">{count} ({percentage.toFixed(1)}%)</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="h-2 rounded-full bg-purple-500"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              );
            })}
        </div>
      </div>
    </div>
  );
};

export default BusinessCaseStats;
