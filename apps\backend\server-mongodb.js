// Financial Modeling API Server with MongoDB
const http = require('http');
const url = require('url');
const { MongoClient, ObjectId } = require('mongodb');

const PORT = process.env.PORT || 5000;
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/financial-modeling';

let db;
let client;

// Initialize MongoDB connection
async function connectToMongoDB() {
  try {
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    db = client.db('financial-modeling');
    console.log('✅ Connected to MongoDB successfully');
    
    // Initialize default data if collections are empty
    await initializeDefaultData();
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    console.log('📝 Make sure MongoDB is running on localhost:27017');
    console.log('📝 Or install MongoDB: https://www.mongodb.com/try/download/community');
    process.exit(1);
  }
}

// Initialize default data
async function initializeDefaultData() {
  try {
    const usersCollection = db.collection('users');
    const userCount = await usersCollection.countDocuments();
    
    if (userCount === 0) {
      console.log('🔄 Initializing default users...');
      await usersCollection.insertMany([
        {
          _id: new ObjectId(),
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'admin',
          password: 'password123', // In production, this should be hashed
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          _id: new ObjectId(),
          name: 'Financial Analyst',
          email: '<EMAIL>',
          role: 'financial_analyst',
          password: 'password123',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          _id: new ObjectId(),
          name: 'Executive User',
          email: '<EMAIL>',
          role: 'executive',
          password: 'password123',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          _id: new ObjectId(),
          name: 'Gyanesh',
          email: 'gyanesh',
          role: 'admin',
          password: 'gyanesh123',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]);
      console.log('✅ Default users created');
    }

    // Initialize financial data
    const financialDataCollection = db.collection('financialData');
    const dataCount = await financialDataCollection.countDocuments();
    
    if (dataCount === 0) {
      console.log('🔄 Initializing financial data...');
      await financialDataCollection.insertOne({
        _id: new ObjectId(),
        type: 'dashboard_metrics',
        metrics: {
          npv: 2400000,
          irr: 18.7,
          paybackPeriod: 3.2,
          yieldIndex: 1.45,
          grossMargin: 42.3,
          breakEvenSales: 1800000
        },
        models: [
          {
            id: '1',
            name: 'Product Launch Q1 2024',
            lastModified: '2 hours ago',
            npv: '$2.4M',
            irr: '18.7%',
            status: 'Active'
          },
          {
            id: '2',
            name: 'Market Expansion Europe',
            lastModified: '1 day ago',
            npv: '$1.8M',
            irr: '15.2%',
            status: 'Draft'
          },
          {
            id: '3',
            name: 'Cost Optimization Initiative',
            lastModified: '3 days ago',
            npv: '$890K',
            irr: '22.1%',
            status: 'Completed'
          }
        ],
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log('✅ Financial data initialized');
    }

    // Initialize parameters
    const parametersCollection = db.collection('parameters');
    const paramCount = await parametersCollection.countDocuments();
    
    if (paramCount === 0) {
      console.log('🔄 Initializing financial parameters...');
      await parametersCollection.insertOne({
        _id: new ObjectId(),
        discountRate: 10.0,
        taxRate: 25.0,
        financialRate: 5.0,
        currency: 'USD',
        conversionRates: {
          'EUR': 1.08,
          'GBP': 1.25,
          'JPY': 0.0067
        },
        inflationRates: {
          rd: 3.0,
          production: 2.5,
          sales: 2.0
        },
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log('✅ Financial parameters initialized');
    }

  } catch (error) {
    console.error('Error initializing default data:', error);
  }
}

// Helper function to parse JSON body
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(error);
      }
    });
  });
}

// Helper function to send JSON response
function sendJSON(res, statusCode, data) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  res.end(JSON.stringify(data));
}

// Create HTTP server
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${path}`);

  // Handle CORS preflight
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end();
    return;
  }

  try {
    // Health check
    if (path === '/health' && method === 'GET') {
      sendJSON(res, 200, {
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        database: 'MongoDB Connected'
      });
      return;
    }

    // Login endpoint
    if (path === '/api/auth/login' && method === 'POST') {
      const body = await parseBody(req);
      const { email, password } = body;

      console.log('Login attempt:', { email });

      const usersCollection = db.collection('users');
      const user = await usersCollection.findOne({ email, password });
      
      if (user) {
        sendJSON(res, 200, {
          success: true,
          data: {
            token: 'mongodb-jwt-token-' + user._id,
            user: {
              id: user._id,
              name: user.name,
              email: user.email,
              role: user.role,
              createdAt: user.createdAt,
              updatedAt: user.updatedAt
            }
          }
        });
      } else {
        sendJSON(res, 400, {
          success: false,
          error: 'Invalid credentials'
        });
      }
      return;
    }

    // Verify token endpoint
    if (path === '/api/auth/verify' && method === 'GET') {
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const userId = token.replace('mongodb-jwt-token-', '');
        
        try {
          const usersCollection = db.collection('users');
          const user = await usersCollection.findOne({ _id: new ObjectId(userId) });
          
          if (user) {
            sendJSON(res, 200, {
              success: true,
              data: {
                id: user._id,
                name: user.name,
                email: user.email,
                role: user.role,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
              }
            });
          } else {
            sendJSON(res, 401, {
              success: false,
              error: 'Invalid token'
            });
          }
        } catch (error) {
          sendJSON(res, 401, {
            success: false,
            error: 'Invalid token format'
          });
        }
      } else {
        sendJSON(res, 401, {
          success: false,
          error: 'No token provided'
        });
      }
      return;
    }

    // Dashboard data endpoint
    if (path === '/api/dashboard' && method === 'GET') {
      const financialDataCollection = db.collection('financialData');
      const dashboardData = await financialDataCollection.findOne({ type: 'dashboard_metrics' });
      
      sendJSON(res, 200, {
        success: true,
        data: {
          metrics: dashboardData.metrics,
          models: dashboardData.models
        }
      });
      return;
    }

    // Financial parameters endpoint
    if (path === '/api/parameters' && method === 'GET') {
      const parametersCollection = db.collection('parameters');
      const parameters = await parametersCollection.findOne({});
      
      sendJSON(res, 200, {
        success: true,
        data: parameters
      });
      return;
    }

    // Update financial parameters endpoint
    if (path === '/api/parameters' && method === 'PUT') {
      const body = await parseBody(req);
      const parametersCollection = db.collection('parameters');
      
      const result = await parametersCollection.updateOne(
        {},
        { 
          $set: { 
            ...body, 
            updatedAt: new Date() 
          } 
        },
        { upsert: true }
      );
      
      sendJSON(res, 200, {
        success: true,
        data: { updated: result.modifiedCount > 0 || result.upsertedCount > 0 }
      });
      return;
    }

    // Users management endpoint
    if (path === '/api/users' && method === 'GET') {
      const usersCollection = db.collection('users');
      const users = await usersCollection.find({}, { projection: { password: 0 } }).toArray();
      
      sendJSON(res, 200, {
        success: true,
        data: users
      });
      return;
    }

    // Create new user endpoint
    if (path === '/api/users' && method === 'POST') {
      const body = await parseBody(req);
      const usersCollection = db.collection('users');
      
      // Check if user already exists
      const existingUser = await usersCollection.findOne({ email: body.email });
      if (existingUser) {
        sendJSON(res, 400, {
          success: false,
          error: 'User already exists'
        });
        return;
      }
      
      const newUser = {
        _id: new ObjectId(),
        ...body,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await usersCollection.insertOne(newUser);
      
      // Return user without password
      const { password, ...userWithoutPassword } = newUser;
      sendJSON(res, 201, {
        success: true,
        data: userWithoutPassword
      });
      return;
    }

    // Placeholder endpoints
    const placeholderEndpoints = [
      '/api/costs',
      '/api/sales',
      '/api/pricing',
      '/api/sensitivity',
      '/api/financial',
      '/api/export'
    ];

    if (placeholderEndpoints.some(endpoint => path.startsWith(endpoint))) {
      sendJSON(res, 200, {
        success: true,
        data: [],
        message: `${path} endpoint - MongoDB ready, implementation coming soon`
      });
      return;
    }

    // 404 for unknown routes
    sendJSON(res, 404, {
      success: false,
      error: 'Route not found'
    });

  } catch (error) {
    console.error('Server error:', error);
    sendJSON(res, 500, {
      success: false,
      error: 'Internal server error'
    });
  }
});

// Start server
async function startServer() {
  await connectToMongoDB();
  
  server.listen(PORT, () => {
    console.log(`🚀 Financial Modeling API Server running on port ${PORT}`);
    console.log(`📊 Dashboard: http://localhost:${PORT}/health`);
    console.log(`🗄️  Database: MongoDB (${MONGODB_URI})`);
    console.log(`🔐 Demo credentials:`);
    console.log(`   Admin: <EMAIL> / password123`);
    console.log(`   Analyst: <EMAIL> / password123`);
    console.log(`   Executive: <EMAIL> / password123`);
    console.log(`   Gyanesh (Admin): gyanesh / gyanesh123`);
    console.log(`\n📡 API Endpoints:`);
    console.log(`   POST /api/auth/login`);
    console.log(`   GET  /api/auth/verify`);
    console.log(`   GET  /api/dashboard`);
    console.log(`   GET  /api/parameters`);
    console.log(`   PUT  /api/parameters`);
    console.log(`   GET  /api/users`);
    console.log(`   POST /api/users`);
  });
}

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  if (client) {
    await client.close();
  }
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', async () => {
  console.log('\nSIGINT received, shutting down gracefully');
  if (client) {
    await client.close();
  }
  server.close(() => {
    console.log('Process terminated');
  });
});

// Start the server
startServer().catch(console.error);
