# MasterBC Migration Guide

**Author: <PERSON><PERSON><PERSON>**  
**Repository: https://github.com/mahegyaneshpandey/spm**  
**Date: January 27, 2025**

## 🎯 **Migration Overview**

This guide provides step-by-step instructions to migrate from the current MasterBC codebase to the new, organized project structure. The migration preserves all existing functionality while improving maintainability and scalability.

## 📋 **Pre-Migration Checklist**

- [ ] Backup current codebase
- [ ] Ensure all tests are passing
- [ ] Document current environment setup
- [ ] Verify all dependencies are documented
- [ ] Create migration branch in Git

## 🔄 **Migration Steps**

### **Phase 1: Project Structure Setup**

#### **Step 1.1: Create New Directory Structure**
```bash
# Create the new project structure
mkdir -p MasterBC-New/{apps/{frontend,backend},packages,docs,scripts,tests,tools}
mkdir -p MasterBC-New/apps/frontend/src/{components,contexts,services,hooks,utils,types}
mkdir -p MasterBC-New/apps/backend/src/{controllers,models,routes,services,middleware,utils,types}
mkdir -p MasterBC-New/packages/{shared-types,shared-utils,shared-constants}
mkdir -p MasterBC-New/docs/{api,architecture,user-guide,development}
```

#### **Step 1.2: Copy Root Configuration Files**
```bash
# Copy and update root package.json
cp package.json MasterBC-New/package.json
# Update to workspace configuration (see new package.json)

# Copy Git configuration
cp .gitignore MasterBC-New/.gitignore
cp .git MasterBC-New/.git -r  # If starting fresh repo
```

### **Phase 2: Frontend Migration**

#### **Step 2.1: Move React Application**
```bash
# Copy frontend source files
cp -r react-business-case-app/src/* MasterBC-New/apps/frontend/src/
cp -r react-business-case-app/public/* MasterBC-New/apps/frontend/public/
cp react-business-case-app/package.json MasterBC-New/apps/frontend/package.json
```

#### **Step 2.2: Reorganize Frontend Structure**
```bash
# Move components to organized structure
mv MasterBC-New/apps/frontend/src/components/Tabs MasterBC-New/apps/frontend/src/components/tabs
mv MasterBC-New/apps/frontend/src/context MasterBC-New/apps/frontend/src/contexts

# Create new organized directories
mkdir -p MasterBC-New/apps/frontend/src/components/{common,layout}
mkdir -p MasterBC-New/apps/frontend/src/hooks
```

#### **Step 2.3: Update Import Paths**
Update all import statements in React components to reflect new structure:
```javascript
// Old imports
import { useData } from '../context/DataContext';
import { useAuth } from '../context/AuthContext';

// New imports
import { useData } from '../contexts/DataContext';
import { useAuth } from '../contexts/AuthContext';
```

### **Phase 3: Backend Migration**

#### **Step 3.1: Move Backend Files**
```bash
# Copy backend files
cp backend/business-case-server.js MasterBC-New/apps/backend/src/server.js
cp -r backend/data MasterBC-New/apps/backend/data
cp backend/package.json MasterBC-New/apps/backend/package.json
```

#### **Step 3.2: Refactor Backend Structure**
```bash
# Create organized backend structure
mkdir -p MasterBC-New/apps/backend/src/{controllers,routes,services,middleware}

# Split monolithic server file into organized modules
# (This requires manual refactoring - see backend refactoring guide)
```

### **Phase 4: Shared Packages Creation**

#### **Step 4.1: Extract Common Types**
```bash
# Create shared types package
cd MasterBC-New/packages/shared-types
npm init -y
```

Create TypeScript definitions for shared entities:
```typescript
// packages/shared-types/src/BusinessCase.ts
export interface BusinessCase {
  id: string;
  name: string;
  description?: string;
  capex: number;
  opex: number;
  // ... other properties
}
```

#### **Step 4.2: Extract Common Utils**
```bash
# Create shared utilities package
cd MasterBC-New/packages/shared-utils
npm init -y
```

Move common utility functions:
```javascript
// packages/shared-utils/src/calculations.js
export const calculateIRR = (cashFlows) => {
  // IRR calculation logic
};

export const calculateNPV = (cashFlows, discountRate) => {
  // NPV calculation logic
};
```

### **Phase 5: Documentation Migration**

#### **Step 5.1: Create API Documentation**
```bash
# Copy existing API documentation or create new
cp existing-api-docs/* MasterBC-New/docs/api/
```

#### **Step 5.2: Create Architecture Documentation**
```bash
# Create architecture diagrams and documentation
# Use the provided PROJECT_STRUCTURE.md as a template
```

### **Phase 6: Testing Setup**

#### **Step 6.1: Migrate Existing Tests**
```bash
# Copy existing test files
cp -r existing-tests/* MasterBC-New/tests/
```

#### **Step 6.2: Create New Test Structure**
```bash
# Organize tests by type
mkdir -p MasterBC-New/tests/{unit,integration,e2e}
```

### **Phase 7: Build and Deployment**

#### **Step 7.1: Create Build Scripts**
```bash
# Copy provided scripts
cp MasterBC-Restructured/scripts/* MasterBC-New/scripts/
chmod +x MasterBC-New/scripts/*.sh
```

#### **Step 7.2: Setup Development Environment**
```bash
# Run setup script
cd MasterBC-New
./scripts/setup.sh
```

## 🔧 **Configuration Updates**

### **Frontend Configuration**
Update `apps/frontend/package.json`:
```json
{
  "name": "@masterbc/frontend",
  "version": "1.0.0",
  "dependencies": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "lint": "eslint src/"
  }
}
```

### **Backend Configuration**
Update `apps/backend/package.json`:
```json
{
  "name": "@masterbc/backend",
  "version": "1.0.0",
  "dependencies": {
    "express": "^4.18.0",
    "cors": "^2.8.5"
  },
  "scripts": {
    "start": "node src/server.js",
    "dev": "nodemon src/server.js",
    "test": "jest",
    "lint": "eslint src/"
  }
}
```

## ✅ **Post-Migration Verification**

### **Step 1: Verify Installation**
```bash
cd MasterBC-New
npm install
npm run dev
```

### **Step 2: Test All Functionality**
- [ ] Login system works
- [ ] Business cases CRUD operations
- [ ] Projects & Epics functionality
- [ ] Master business cases
- [ ] Programs management
- [ ] Dashboard displays correctly
- [ ] All API endpoints respond

### **Step 3: Run Tests**
```bash
npm test
npm run test:integration
npm run test:e2e
```

### **Step 4: Verify Build Process**
```bash
npm run build
```

## 🚀 **Deployment**

### **Step 1: Update Repository**
```bash
# Commit migrated code
git add .
git commit -m "feat: Migrate to organized project structure

- Restructured into monorepo with apps/packages
- Improved component organization
- Added comprehensive documentation
- Created automated setup scripts
- Enhanced development workflow

Author: Gyanesh K Pandey
Repository: https://github.com/mahegyaneshpandey/spm"

# Push to new branch
git push origin feature/project-restructure
```

### **Step 2: Create Pull Request**
Create a pull request with:
- Detailed description of changes
- Migration checklist
- Testing verification
- Documentation updates

## 🔄 **Rollback Plan**

If issues arise during migration:

1. **Immediate Rollback**: Switch back to original codebase
2. **Partial Rollback**: Revert specific components
3. **Fix Forward**: Address issues in new structure

## 📚 **Additional Resources**

- [Project Structure Documentation](docs/architecture/PROJECT_STRUCTURE.md)
- [API Documentation](docs/api/README.md)
- [Development Setup Guide](docs/development/setup.md)
- [Contributing Guidelines](docs/development/contributing.md)

## 👨‍💻 **Support**

For migration assistance:
- **Author**: Gyanesh K Pandey
- **Repository**: https://github.com/mahegyaneshpandey/spm
- **Issues**: Create GitHub issues for migration problems

---

*This migration preserves all existing functionality while providing a foundation for future scalability and maintainability.*
