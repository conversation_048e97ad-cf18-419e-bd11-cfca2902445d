import React, { useMemo } from 'react';
import BusinessUnitPortfolio from './BusinessUnitPortfolio';

const PortfolioAnalysis = ({ masterBusinessCases, programs, businessCases, projects, onViewChange }) => {
  console.log('🔍 PortfolioAnalysis received data:', { 
    masterBusinessCases: masterBusinessCases?.length, 
    programs: programs?.length, 
    businessCases: businessCases?.length 
  });

  // Calculate portfolio metrics based on real data
  const portfolioMetrics = useMemo(() => {
    if (!masterBusinessCases || !Array.isArray(masterBusinessCases)) {
      return {
        totalMasterBCs: 0,
        totalInvestment: 0,
        totalNPV: 0,
        avgIRR: 0,
        activeCount: 0,
        draftCount: 0,
        categories: {},
        riskLevels: {},
        topPerformers: []
      };
    }

    let totalInvestment = 0;
    let totalNPV = 0;
    let totalIRR = 0;
    let irrCount = 0;
    let activeCount = 0;
    let draftCount = 0;
    const categories = {};
    const riskLevels = {};

    masterBusinessCases.forEach(mbc => {
      // Status counts
      if (mbc.status === 'active') activeCount++;
      if (mbc.status === 'draft') draftCount++;

      // Financial metrics
      if (mbc.aggregatedMetrics) {
        totalInvestment += mbc.aggregatedMetrics.totalInvestment || 0;
        totalNPV += mbc.aggregatedMetrics.totalNPV || 0;
        if (mbc.aggregatedMetrics.avgIRR) {
          totalIRR += mbc.aggregatedMetrics.avgIRR;
          irrCount++;
        }
      }

      // Categories
      const category = mbc.category || 'Uncategorized';
      categories[category] = (categories[category] || 0) + 1;

      // Risk levels
      const riskLevel = mbc.metadata?.riskLevel || 'Unknown';
      riskLevels[riskLevel] = (riskLevels[riskLevel] || 0) + 1;
    });

    // Top performers by NPV
    const topPerformers = masterBusinessCases
      .filter(mbc => mbc.aggregatedMetrics?.totalNPV > 0)
      .sort((a, b) => (b.aggregatedMetrics?.totalNPV || 0) - (a.aggregatedMetrics?.totalNPV || 0))
      .slice(0, 5);

    return {
      totalMasterBCs: masterBusinessCases.length,
      totalInvestment,
      totalNPV,
      avgIRR: irrCount > 0 ? totalIRR / irrCount : 0,
      activeCount,
      draftCount,
      categories,
      riskLevels,
      topPerformers
    };
  }, [masterBusinessCases]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${Number(value).toFixed(1)}%`;
  };

  return (
    <div className="portfolio-analysis">
      {/* Business Unit Portfolio - Most Important Section */}
      <BusinessUnitPortfolio
        masterBusinessCases={masterBusinessCases}
        programs={programs}
        projects={projects}
        businessCases={businessCases}
      />

      {/* Portfolio Summary */}
      <div className="portfolio-summary">
        <h3 className="section-title">Portfolio Summary</h3>
        <div className="summary-grid">
          <div className="summary-card">
            <div className="summary-icon">
              <i className="fas fa-briefcase"></i>
            </div>
            <div className="summary-content">
              <h4>Total Master Business Cases</h4>
              <p className="summary-value">{portfolioMetrics.totalMasterBCs}</p>
              <span className="summary-detail">
                {portfolioMetrics.activeCount} Active, {portfolioMetrics.draftCount} Draft
              </span>
            </div>
          </div>

          <div className="summary-card">
            <div className="summary-icon">
              <i className="fas fa-dollar-sign"></i>
            </div>
            <div className="summary-content">
              <h4>Total Portfolio Investment</h4>
              <p className="summary-value">{formatCurrency(portfolioMetrics.totalInvestment)}</p>
              <span className="summary-detail">Across all Master BCs</span>
            </div>
          </div>

          <div className="summary-card">
            <div className="summary-icon">
              <i className="fas fa-trending-up"></i>
            </div>
            <div className="summary-content">
              <h4>Total Portfolio NPV</h4>
              <p className="summary-value">{formatCurrency(portfolioMetrics.totalNPV)}</p>
              <span className="summary-detail">Expected value creation</span>
            </div>
          </div>

          <div className="summary-card">
            <div className="summary-icon">
              <i className="fas fa-chart-line"></i>
            </div>
            <div className="summary-content">
              <h4>Average Portfolio IRR</h4>
              <p className="summary-value">{formatPercentage(portfolioMetrics.avgIRR)}</p>
              <span className="summary-detail">Weighted average return</span>
            </div>
          </div>
        </div>
      </div>

      {/* Category Distribution */}
      <div className="category-section">
        <h3 className="section-title">Portfolio by Category</h3>
        <div className="category-grid">
          {Object.entries(portfolioMetrics.categories).map(([category, count]) => (
            <div key={category} className="category-card">
              <h4>{category}</h4>
              <p className="category-count">{count} Master BC{count !== 1 ? 's' : ''}</p>
              <div className="category-bar">
                <div 
                  className="category-fill"
                  style={{ 
                    width: `${(count / portfolioMetrics.totalMasterBCs) * 100}%` 
                  }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Risk Distribution */}
      <div className="risk-section">
        <h3 className="section-title">Risk Distribution</h3>
        <div className="risk-grid">
          {Object.entries(portfolioMetrics.riskLevels).map(([risk, count]) => (
            <div key={risk} className={`risk-card risk-${risk.toLowerCase()}`}>
              <div className="risk-header">
                <h4>{risk} Risk</h4>
                <span className="risk-count">{count}</span>
              </div>
              <div className="risk-percentage">
                {((count / portfolioMetrics.totalMasterBCs) * 100).toFixed(1)}% of portfolio
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Top Performers */}
      <div className="performers-section">
        <h3 className="section-title">Top Performing Master Business Cases</h3>
        <div className="performers-list">
          {portfolioMetrics.topPerformers.map((mbc, index) => (
            <div key={mbc.id} className="performer-card">
              <div className="performer-rank">#{index + 1}</div>
              <div className="performer-content">
                <h4>{mbc.name}</h4>
                <p className="performer-description">{mbc.description}</p>
                <div className="performer-metrics">
                  <span className="metric">
                    NPV: {formatCurrency(mbc.aggregatedMetrics?.totalNPV || 0)}
                  </span>
                  <span className="metric">
                    IRR: {formatPercentage(mbc.aggregatedMetrics?.avgIRR || 0)}
                  </span>
                  <span className="metric">
                    Investment: {formatCurrency(mbc.aggregatedMetrics?.totalInvestment || 0)}
                  </span>
                </div>
              </div>
              <div className="performer-status">
                <span className={`status-badge status-${mbc.status}`}>
                  {mbc.status}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <style jsx>{`
        .portfolio-analysis {
          padding: 24px;
        }

        .section-title {
          font-size: 20px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 20px 0;
        }

        .portfolio-summary {
          margin-bottom: 32px;
        }

        .summary-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;
        }

        .summary-card {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 20px;
          background: white;
          border-radius: 12px;
          border: 1px solid #e2e8f0;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .summary-icon {
          width: 48px;
          height: 48px;
          background: #3b82f6;
          color: white;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
        }

        .summary-content h4 {
          font-size: 14px;
          font-weight: 500;
          color: #64748b;
          margin: 0 0 8px 0;
        }

        .summary-value {
          font-size: 24px;
          font-weight: 700;
          color: #1e293b;
          margin: 0 0 4px 0;
        }

        .summary-detail {
          font-size: 12px;
          color: #94a3b8;
        }

        .category-section,
        .risk-section,
        .performers-section {
          margin-bottom: 32px;
        }

        .category-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;
        }

        .category-card {
          padding: 16px;
          background: white;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
        }

        .category-card h4 {
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 8px 0;
        }

        .category-count {
          color: #64748b;
          margin: 0 0 12px 0;
        }

        .category-bar {
          height: 4px;
          background: #e2e8f0;
          border-radius: 2px;
          overflow: hidden;
        }

        .category-fill {
          height: 100%;
          background: #3b82f6;
          transition: width 0.3s ease;
        }

        .risk-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 16px;
        }

        .risk-card {
          padding: 16px;
          background: white;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
        }

        .risk-card.risk-low {
          border-left: 4px solid #10b981;
        }

        .risk-card.risk-medium {
          border-left: 4px solid #f59e0b;
        }

        .risk-card.risk-high {
          border-left: 4px solid #ef4444;
        }

        .risk-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
        }

        .risk-header h4 {
          font-size: 14px;
          font-weight: 600;
          color: #1e293b;
          margin: 0;
        }

        .risk-count {
          font-size: 18px;
          font-weight: 700;
          color: #3b82f6;
        }

        .risk-percentage {
          font-size: 12px;
          color: #64748b;
        }

        .performers-list {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .performer-card {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 20px;
          background: white;
          border-radius: 12px;
          border: 1px solid #e2e8f0;
        }

        .performer-rank {
          width: 40px;
          height: 40px;
          background: #3b82f6;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 700;
          flex-shrink: 0;
        }

        .performer-content {
          flex: 1;
        }

        .performer-content h4 {
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 4px 0;
        }

        .performer-description {
          color: #64748b;
          margin: 0 0 8px 0;
          font-size: 14px;
        }

        .performer-metrics {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;
        }

        .metric {
          font-size: 12px;
          color: #64748b;
          background: #f1f5f9;
          padding: 4px 8px;
          border-radius: 4px;
        }

        .performer-status {
          flex-shrink: 0;
        }

        .status-badge {
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 500;
          text-transform: capitalize;
        }

        .status-badge.status-active {
          background: #dcfce7;
          color: #166534;
        }

        .status-badge.status-draft {
          background: #fef3c7;
          color: #92400e;
        }

        @media (max-width: 768px) {
          .portfolio-analysis {
            padding: 16px;
          }

          .summary-grid {
            grid-template-columns: 1fr;
          }

          .performer-card {
            flex-direction: column;
            text-align: center;
          }

          .performer-metrics {
            justify-content: center;
          }
        }
      `}</style>
    </div>
  );
};

export default PortfolioAnalysis;
