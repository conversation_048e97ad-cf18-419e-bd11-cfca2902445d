/* Data Loading Status Styles */
.data-loading-status {
  padding: 24px;
  background: #f8f9fa;
  min-height: 100vh;
}

.status-header {
  text-align: center;
  margin-bottom: 32px;
}

.status-header h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}

.status-header p {
  margin: 0 0 16px 0;
  color: #6b7280;
  font-size: 16px;
}

.view-toggle {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.toggle-btn:hover {
  background: #e5e7eb;
}

.toggle-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

/* Status Grid */
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.status-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #d1d5db;
  transition: all 0.2s;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.status-card.status-success {
  border-left-color: #10b981;
}

.status-card.status-error {
  border-left-color: #ef4444;
}

.status-card.status-warning {
  border-left-color: #f59e0b;
}

.status-card.status-loading {
  border-left-color: #3b82f6;
}

.status-icon {
  font-size: 24px;
  margin-bottom: 12px;
}

.status-info h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.status-text {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.expected-text {
  margin: 0 0 12px 0;
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.error-details,
.data-preview {
  margin-top: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.error-details strong,
.data-preview strong {
  display: block;
  margin-bottom: 8px;
  font-size: 12px;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.error-details pre,
.data-preview pre {
  margin: 0;
  font-size: 11px;
  color: #6b7280;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 100px;
  overflow-y: auto;
}

/* Status Summary */
.status-summary {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.status-summary h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.stat-value.success {
  color: #10b981;
}

.stat-value.error {
  color: #ef4444;
}

.stat-value.warning {
  color: #f59e0b;
}

.stat-value.loading {
  color: #3b82f6;
}

/* Status Actions */
.status-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 32px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn.refresh {
  background: #3b82f6;
  color: white;
}

.action-btn.refresh:hover {
  background: #2563eb;
}

.action-btn.export {
  background: #10b981;
  color: white;
}

.action-btn.export:hover {
  background: #059669;
}

/* Troubleshooting */
.troubleshooting {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.troubleshooting h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.troubleshooting-tips {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.tip {
  padding: 16px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
}

.tip strong {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #1e40af;
}

.tip p {
  margin: 0;
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .data-loading-status {
    padding: 16px;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .summary-stats {
    grid-template-columns: 1fr;
  }
  
  .status-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

/* Animation for loading states */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.status-card.status-loading .status-icon {
  animation: pulse 2s infinite;
}

/* Scrollbar styling for code blocks */
.error-details pre::-webkit-scrollbar,
.data-preview pre::-webkit-scrollbar {
  width: 4px;
}

.error-details pre::-webkit-scrollbar-track,
.data-preview pre::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.error-details pre::-webkit-scrollbar-thumb,
.data-preview pre::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.error-details pre::-webkit-scrollbar-thumb:hover,
.data-preview pre::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* API Testing */
.api-testing {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.api-testing h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.api-tests {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.test-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  color: #1e40af;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.test-btn:hover {
  background: #dbeafe;
  border-color: #93c5fd;
}
