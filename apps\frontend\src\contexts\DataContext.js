/**
 * MasterBC - Data Context Provider
 * 
 * Centralized data management for the Master Business Case application.
 * Provides state management and API integration for all business entities.
 * 
 * <AUTHOR>
 * @repository https://github.com/mahegyaneshpandey/spm
 * @date January 27, 2025
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { businessCaseService } from '../services/businessCaseService';
import { dashboardService } from '../services/dashboardService';
import { projectService } from '../services/projectService';
import { ideaService } from '../services/ideaService';
import { useAuth } from './AuthContext';
import { LOADING_STATES, ERROR_MESSAGES } from '../utils/constants';

// Create the Data Context
const DataContext = createContext();

/**
 * Custom hook to access the Data Context
 * 
 * @returns {Object} Data context value with state and actions
 * @throws {Error} If used outside of DataProvider
 */
export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

/**
 * Initial state structure for all data entities
 */
const initialState = {
  // Business entities
  businessCases: [],
  masterBusinessCases: [],
  projects: [],
  epics: [],
  programs: [],
  ideas: [],
  
  // Dashboard data
  dashboardData: {
    kpis: {},
    charts: {},
    recentActivity: [],
    insights: []
  },
  
  // Loading states for each entity
  loading: {
    businessCases: false,
    masterBusinessCases: false,
    projects: false,
    programs: false,
    ideas: false,
    dashboard: false
  },
  
  // Error states for each entity
  errors: {}
};

/**
 * Data Provider Component
 * 
 * Manages all application data state and provides CRUD operations
 * for business cases, projects, programs, ideas, and dashboard data.
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 */
export const DataProvider = ({ children }) => {
  const { isAuthenticated, authToken } = useAuth();
  
  // State management for all entities
  const [businessCases, setBusinessCases] = useState(initialState.businessCases);
  const [masterBusinessCases, setMasterBusinessCases] = useState(initialState.masterBusinessCases);
  const [projects, setProjects] = useState(initialState.projects);
  const [epics, setEpics] = useState(initialState.epics);
  const [programs, setPrograms] = useState(initialState.programs);
  const [ideas, setIdeas] = useState(initialState.ideas);
  const [dashboardData, setDashboardData] = useState(initialState.dashboardData);
  const [loading, setLoading] = useState(initialState.loading);
  const [errors, setErrors] = useState(initialState.errors);

  /**
   * Generic error handler for API operations
   * 
   * @param {string} entity - Entity name for error tracking
   * @param {Error} error - Error object
   */
  const handleError = useCallback((entity, error) => {
    console.error(`${entity} operation failed:`, error);
    setErrors(prev => ({ 
      ...prev, 
      [entity]: error.message || ERROR_MESSAGES.SERVER_ERROR 
    }));
  }, []);

  /**
   * Generic loading state manager
   * 
   * @param {string} entity - Entity name
   * @param {boolean} isLoading - Loading state
   */
  const setEntityLoading = useCallback((entity, isLoading) => {
    setLoading(prev => ({ ...prev, [entity]: isLoading }));
  }, []);

  /**
   * Clear errors for a specific entity
   * 
   * @param {string} entity - Entity name
   */
  const clearError = useCallback((entity) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[entity];
      return newErrors;
    });
  }, []);

  // ============================================================================
  // BUSINESS CASES OPERATIONS
  // ============================================================================

  /**
   * Load all business cases from the API
   */
  const loadBusinessCases = useCallback(async () => {
    setEntityLoading('businessCases', true);
    clearError('businessCases');
    
    try {
      const response = await businessCaseService.getAll();
      if (response.success) {
        // Extract the businessCases array from the nested data structure
        setBusinessCases(response.data.businessCases || []);
      } else {
        handleError('businessCases', new Error(response.error));
      }
    } catch (error) {
      handleError('businessCases', error);
    } finally {
      setEntityLoading('businessCases', false);
    }
  }, [handleError, setEntityLoading, clearError]);

  /**
   * Create a new business case
   * 
   * @param {Object} businessCaseData - Business case data
   * @returns {Object} Operation result
   */
  const createBusinessCase = useCallback(async (businessCaseData) => {
    try {
      const response = await businessCaseService.create(businessCaseData);
      if (response.success) {
        setBusinessCases(prev => [...prev, response.data]);
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, []);

  /**
   * Update an existing business case
   * 
   * @param {string} id - Business case ID
   * @param {Object} businessCaseData - Updated business case data
   * @returns {Object} Operation result
   */
  const updateBusinessCase = useCallback(async (id, businessCaseData) => {
    try {
      const response = await businessCaseService.update(id, businessCaseData);
      if (response.success) {
        setBusinessCases(prev => 
          prev.map(bc => bc.id === id ? response.data : bc)
        );
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, []);

  /**
   * Delete a business case
   * 
   * @param {string} id - Business case ID
   * @returns {Object} Operation result
   */
  const deleteBusinessCase = useCallback(async (id) => {
    try {
      const response = await businessCaseService.delete(id);
      if (response.success) {
        setBusinessCases(prev => prev.filter(bc => bc.id !== id));
        return { success: true };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, []);

  // ============================================================================
  // MASTER BUSINESS CASES OPERATIONS
  // ============================================================================

  /**
   * Load all master business cases from the API
   */
  const loadMasterBusinessCases = useCallback(async () => {
    setEntityLoading('masterBusinessCases', true);
    clearError('masterBusinessCases');
    
    try {
      const response = await businessCaseService.getMasterBusinessCases();
      if (response.success) {
        // Extract the masterBusinessCases array from the nested data structure
        setMasterBusinessCases(response.data.masterBusinessCases || []);
      } else {
        handleError('masterBusinessCases', new Error(response.error));
      }
    } catch (error) {
      handleError('masterBusinessCases', error);
    } finally {
      setEntityLoading('masterBusinessCases', false);
    }
  }, [handleError, setEntityLoading, clearError]);

  /**
   * Create a new master business case
   * 
   * @param {Object} masterBCData - Master business case data
   * @returns {Object} Operation result
   */
  const createMasterBusinessCase = useCallback(async (masterBCData) => {
    try {
      const response = await businessCaseService.createMasterBusinessCase(masterBCData);
      if (response.success) {
        setMasterBusinessCases(prev => [...prev, response.data]);
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, []);

  /**
   * Update an existing master business case
   * 
   * @param {string} id - Master business case ID
   * @param {Object} masterBCData - Updated master business case data
   * @returns {Object} Operation result
   */
  const updateMasterBusinessCase = useCallback(async (id, masterBCData) => {
    try {
      const response = await businessCaseService.updateMasterBusinessCase(id, masterBCData);
      if (response.success) {
        setMasterBusinessCases(prev =>
          prev.map(mbc => mbc.id === id ? response.data : mbc)
        );
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, []);

  /**
   * Link business cases to a master business case
   * 
   * @param {string} masterBCId - Master business case ID
   * @param {Array} businessCaseIds - Array of business case IDs to link
   * @returns {Object} Operation result
   */
  const linkBusinessCasesToMasterBC = useCallback(async (masterBCId, businessCaseIds) => {
    try {
      const response = await businessCaseService.linkBusinessCases(masterBCId, businessCaseIds);
      if (response.success) {
        // Refresh master business cases to get updated aggregated metrics
        await loadMasterBusinessCases();
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [loadMasterBusinessCases]);

  // ============================================================================
  // PROJECTS & PROGRAMS OPERATIONS
  // ============================================================================

  /**
   * Load all projects and epics from the API
   */
  const loadProjects = useCallback(async () => {
    setEntityLoading('projects', true);
    clearError('projects');

    try {
      const response = await projectService.getAll();
      if (response.success) {
        setProjects(response.data.projects || []);
        setEpics(response.data.epics || []);
      } else {
        handleError('projects', new Error(response.error));
      }
    } catch (error) {
      handleError('projects', error);
    } finally {
      setEntityLoading('projects', false);
    }
  }, [handleError, setEntityLoading, clearError]);

  /**
   * Load all programs from the API
   */
  const loadPrograms = useCallback(async () => {
    setEntityLoading('programs', true);
    clearError('programs');

    try {
      const response = await projectService.getPrograms();
      if (response.success) {
        // Extract the programs array from the nested data structure
        setPrograms(response.data.programs || []);
      } else {
        handleError('programs', new Error(response.error));
      }
    } catch (error) {
      handleError('programs', error);
    } finally {
      setEntityLoading('programs', false);
    }
  }, [handleError, setEntityLoading, clearError]);

  /**
   * Create a new program
   *
   * @param {Object} programData - Program data
   * @returns {Object} Operation result
   */
  const createProgram = useCallback(async (programData) => {
    try {
      const response = await projectService.createProgram(programData);
      if (response.success) {
        setPrograms(prev => [...prev, response.data]);
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, []);

  /**
   * Update an existing program
   *
   * @param {string} id - Program ID
   * @param {Object} programData - Updated program data
   * @returns {Object} Operation result
   */
  const updateProgram = useCallback(async (id, programData) => {
    try {
      const response = await projectService.updateProgram(id, programData);
      if (response.success) {
        setPrograms(prev =>
          prev.map(program => program.id === id ? response.data : program)
        );
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, []);

  /**
   * Delete a program
   *
   * @param {string} id - Program ID
   * @returns {Object} Operation result
   */
  const deleteProgram = useCallback(async (id) => {
    try {
      const response = await projectService.deleteProgram(id);
      if (response.success) {
        setPrograms(prev => prev.filter(program => program.id !== id));
        return { success: true };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, []);

  /**
   * Link a program to a master business case
   *
   * @param {string} programId - Program ID
   * @param {string} masterBCId - Master business case ID
   * @returns {Object} Operation result
   */
  const linkProgramToMasterBC = useCallback(async (programId, masterBCId) => {
    try {
      const response = await projectService.linkProgramToMasterBC(programId, masterBCId);
      if (response.success) {
        // Refresh programs and master business cases
        await Promise.all([loadPrograms(), loadMasterBusinessCases()]);
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [loadPrograms, loadMasterBusinessCases]);

  // ============================================================================
  // IDEAS OPERATIONS
  // ============================================================================

  /**
   * Load all ideas from the API
   */
  const loadIdeas = useCallback(async () => {
    setEntityLoading('ideas', true);
    clearError('ideas');

    try {
      const response = await ideaService.getAll();
      if (response.success) {
        // Extract the ideas array from the nested data structure
        setIdeas(response.data.ideas || []);
      } else {
        handleError('ideas', new Error(response.error));
      }
    } catch (error) {
      handleError('ideas', error);
    } finally {
      setEntityLoading('ideas', false);
    }
  }, [handleError, setEntityLoading, clearError]);

  /**
   * Create a new idea
   *
   * @param {Object} ideaData - Idea data
   * @returns {Object} Operation result
   */
  const createIdea = useCallback(async (ideaData) => {
    try {
      const response = await ideaService.create(ideaData);
      if (response.success) {
        setIdeas(prev => [...prev, response.data]);
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, []);

  /**
   * Update an existing idea
   *
   * @param {string} id - Idea ID
   * @param {Object} ideaData - Updated idea data
   * @returns {Object} Operation result
   */
  const updateIdea = useCallback(async (id, ideaData) => {
    try {
      const response = await ideaService.update(id, ideaData);
      if (response.success) {
        setIdeas(prev =>
          prev.map(idea => idea.id === id ? response.data : idea)
        );
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, []);

  /**
   * Delete an idea
   *
   * @param {string} id - Idea ID
   * @returns {Object} Operation result
   */
  const deleteIdea = useCallback(async (id) => {
    try {
      const response = await ideaService.delete(id);
      if (response.success) {
        setIdeas(prev => prev.filter(idea => idea.id !== id));
        return { success: true };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, []);

  // ============================================================================
  // DASHBOARD OPERATIONS
  // ============================================================================

  /**
   * Load dashboard data from the API
   */
  const loadDashboardData = useCallback(async () => {
    setEntityLoading('dashboard', true);
    clearError('dashboard');

    try {
      const response = await dashboardService.getDashboardData();
      if (response.success) {
        setDashboardData(response.data);
      } else {
        handleError('dashboard', new Error(response.error));
      }
    } catch (error) {
      handleError('dashboard', error);
    } finally {
      setEntityLoading('dashboard', false);
    }
  }, [handleError, setEntityLoading, clearError]);

  // ============================================================================
  // BULK OPERATIONS
  // ============================================================================

  /**
   * Load all initial data when the user is authenticated
   */
  const loadInitialData = useCallback(async () => {
    try {
      await Promise.all([
        loadBusinessCases(),
        loadMasterBusinessCases(),
        loadProjects(),
        loadPrograms(),
        loadIdeas(),
        loadDashboardData()
      ]);
    } catch (error) {
      console.error('Failed to load initial data:', error);
    }
  }, [
    loadBusinessCases,
    loadMasterBusinessCases,
    loadProjects,
    loadPrograms,
    loadIdeas,
    loadDashboardData
  ]);

  /**
   * Refresh all data
   */
  const refreshData = useCallback(() => {
    return loadInitialData();
  }, [loadInitialData]);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  /**
   * Load data when user becomes authenticated
   */
  useEffect(() => {
    if (isAuthenticated && authToken) {
      loadInitialData();
    }
  }, [isAuthenticated, authToken, loadInitialData]);

  // ============================================================================
  // CONTEXT VALUE
  // ============================================================================

  const contextValue = {
    // Data state
    businessCases,
    masterBusinessCases,
    projects,
    epics,
    programs,
    ideas,
    dashboardData,

    // Loading and error states
    loading,
    errors,

    // Business Cases operations
    loadBusinessCases,
    createBusinessCase,
    updateBusinessCase,
    deleteBusinessCase,

    // Master Business Cases operations
    loadMasterBusinessCases,
    createMasterBusinessCase,
    updateMasterBusinessCase,
    linkBusinessCasesToMasterBC,

    // Projects & Programs operations
    loadProjects,
    loadPrograms,
    createProgram,
    updateProgram,
    deleteProgram,
    linkProgramToMasterBC,

    // Ideas operations
    loadIdeas,
    createIdea,
    updateIdea,
    deleteIdea,

    // Dashboard operations
    loadDashboardData,

    // Convenience functions for backward compatibility
    fetchMasterBusinessCases: loadMasterBusinessCases,
    fetchBusinessCases: loadBusinessCases,
    fetchPrograms: loadPrograms,
    fetchProjects: loadProjects,
    fetchIdeas: loadIdeas,

    // Utility functions
    refreshData,
    clearError
  };

  return (
    <DataContext.Provider value={contextValue}>
      {children}
    </DataContext.Provider>
  );
};
