import React from 'react';
import './MasterBCDetail.css';

const MasterBCDetail = ({
  masterBC,
  businessCases = [],
  programs = [],
  onBack,
  onEdit,
  onLinkBusinessCases,
  onLinkPrograms
}) => {
  if (!masterBC) return null;

  // Get linked business cases details
  const linkedBusinessCases = businessCases.filter(bc =>
    (masterBC.linkedBCs && masterBC.linkedBCs.includes(bc.id)) ||
    (masterBC.linkedBusinessCases && masterBC.linkedBusinessCases.includes(bc.id))
  );

  // Get linked programs
  const linkedPrograms = programs.filter(p => p.linkedMasterBC === masterBC.id);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount || 0);
  };

  return (
    <div className="master-bc-detail">
      <div className="detail-header">
        <div className="detail-title">
          <h3>{masterBC.name}</h3>
          <p>Master Business Case Details</p>
        </div>
        <div className="detail-actions">
          <button onClick={onEdit} className="btn btn-primary">
            <i className="fas fa-edit"></i>
            Edit
          </button>
          <button onClick={onLinkBusinessCases} className="btn btn-outline">
            <i className="fas fa-link"></i>
            Link BCs
          </button>
          <button onClick={onLinkPrograms} className="btn btn-outline">
            <i className="fas fa-sitemap"></i>
            Link Program
          </button>
          <button onClick={onBack} className="btn btn-secondary">
            <i className="fas fa-arrow-left"></i>
            Back
          </button>
        </div>
      </div>

      <div className="detail-content">
        <div className="detail-section">
          <h4>Basic Information</h4>
          <div className="detail-grid">
            <div className="detail-item">
              <label>Name:</label>
              <span>{masterBC.name}</span>
            </div>
            <div className="detail-item">
              <label>ID:</label>
              <span>{masterBC.id}</span>
            </div>
            {masterBC.description && (
              <div className="detail-item full-width">
                <label>Description:</label>
                <span>{masterBC.description}</span>
              </div>
            )}
          </div>
        </div>

        {masterBC.aggregatedMetrics && (
          <div className="detail-section">
            <h4>Aggregated Financial Metrics</h4>
            <div className="metrics-grid">
              <div className="metric-card">
                <div className="metric-value">{formatCurrency(masterBC.aggregatedMetrics.totalCapex)}</div>
                <div className="metric-label">Total CAPEX</div>
              </div>
              <div className="metric-card">
                <div className="metric-value">{formatCurrency(masterBC.aggregatedMetrics.totalOpex)}</div>
                <div className="metric-label">Total OPEX</div>
              </div>
              <div className="metric-card">
                <div className="metric-value">{formatCurrency(masterBC.aggregatedMetrics.totalInvestment)}</div>
                <div className="metric-label">Total Investment</div>
              </div>
              <div className="metric-card">
                <div className="metric-value">{formatCurrency(masterBC.aggregatedMetrics.totalNPV)}</div>
                <div className="metric-label">Total NPV</div>
              </div>
              <div className="metric-card">
                <div className="metric-value">{masterBC.aggregatedMetrics.avgIRR || 0}%</div>
                <div className="metric-label">Average IRR</div>
              </div>
              <div className="metric-card">
                <div className="metric-value">{masterBC.aggregatedMetrics.avgPaybackPeriod || 0} years</div>
                <div className="metric-label">Avg Payback Period</div>
              </div>
              <div className="metric-card">
                <div className="metric-value">{masterBC.aggregatedMetrics.linkedCount || 0}</div>
                <div className="metric-label">Linked Business Cases</div>
              </div>
            </div>
          </div>
        )}

        {/* Linked Business Cases */}
        <div className="detail-section">
          <div className="section-header">
            <h4>Linked Business Cases ({linkedBusinessCases.length})</h4>
            <button onClick={onLinkBusinessCases} className="btn btn-sm btn-outline">
              <i className="fas fa-link"></i>
              Manage Links
            </button>
          </div>

          {linkedBusinessCases.length > 0 ? (
            <div className="linked-items">
              {linkedBusinessCases.map((bc) => (
                <div key={bc.id} className="linked-item">
                  <div className="linked-icon">
                    <i className="fas fa-briefcase"></i>
                  </div>
                  <div className="linked-info">
                    <div className="linked-name">{bc.name}</div>
                    <div className="linked-description">{bc.description}</div>
                    <div className="linked-metrics">
                      NPV: {formatCurrency(bc.npv || bc.calculatedMetrics?.npv || bc.financialMetrics?.npv)} |
                      IRR: {bc.irr || bc.calculatedMetrics?.irr || bc.financialMetrics?.irr || 0}% |
                      Status: {bc.status}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="empty-state">
              <i className="fas fa-briefcase"></i>
              <p>No business cases linked</p>
              <button onClick={onLinkBusinessCases} className="btn btn-primary">
                Link Business Cases
              </button>
            </div>
          )}
        </div>

        {/* Linked Programs */}
        <div className="detail-section">
          <div className="section-header">
            <h4>Linked Program ({linkedPrograms.length})</h4>
            <button onClick={onLinkPrograms} className="btn btn-sm btn-outline">
              <i className="fas fa-sitemap"></i>
              Manage Link
            </button>
          </div>

          {linkedPrograms.length > 0 ? (
            <div className="linked-items">
              {linkedPrograms.map((program) => (
                <div key={program.id} className="linked-item">
                  <div className="linked-icon">
                    <i className="fas fa-sitemap"></i>
                  </div>
                  <div className="linked-info">
                    <div className="linked-name">{program.name}</div>
                    <div className="linked-description">{program.description}</div>
                    <div className="linked-metrics">
                      Owner: {program.owner} |
                      Status: {program.status} |
                      Priority: {program.priority}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="empty-state">
              <i className="fas fa-sitemap"></i>
              <p>No program linked</p>
              <button onClick={onLinkPrograms} className="btn btn-primary">
                Link Program
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MasterBCDetail;
