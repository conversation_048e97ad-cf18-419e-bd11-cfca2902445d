import React, { useState } from 'react';
import { useData } from '../../context/DataContext';
import DataValidationReport from './DataValidationReport';
import DataSummaryReport from './DataSummaryReport';
import RelationshipVerification from './RelationshipVerification';
import './DataLoadingStatus.css';

const DataLoadingStatus = () => {
  const {
    businessCases,
    masterBusinessCases,
    projects,
    epics,
    programs,
    ideas,
    dashboardData,
    loading,
    errors
  } = useData();

  const [activeView, setActiveView] = useState('status');

  const dataStatus = [
    {
      name: 'Business Cases',
      data: businessCases,
      loading: loading.businessCases,
      error: errors.businessCases,
      expected: 'Array of business case objects'
    },
    {
      name: 'Master Business Cases',
      data: masterBusinessCases,
      loading: loading.masterBusinessCases,
      error: errors.masterBusinessCases,
      expected: 'Array of master business case objects'
    },
    {
      name: 'Projects',
      data: projects,
      loading: loading.projects,
      error: errors.projects,
      expected: 'Array of project objects'
    },
    {
      name: 'Epics',
      data: epics,
      loading: loading.projects, // Same loading state as projects
      error: errors.projects, // Same error state as projects
      expected: 'Array of epic objects'
    },
    {
      name: 'Programs',
      data: programs,
      loading: loading.programs,
      error: errors.programs,
      expected: 'Array of program objects'
    },
    {
      name: 'Ideas',
      data: ideas,
      loading: loading.ideas,
      error: errors.ideas,
      expected: 'Array of idea objects'
    },
    {
      name: 'Dashboard Data',
      data: dashboardData,
      loading: loading.dashboard,
      error: errors.dashboard,
      expected: 'Object with KPIs, charts, and insights'
    }
  ];

  const getStatusIcon = (item) => {
    if (item.loading) return '⏳';
    if (item.error) return '❌';
    if (!item.data || (Array.isArray(item.data) && item.data.length === 0)) return '⚠️';
    return '✅';
  };

  const getStatusText = (item) => {
    if (item.loading) return 'Loading...';
    if (item.error) return `Error: ${item.error}`;
    if (!item.data) return 'No data';
    if (Array.isArray(item.data)) {
      return `${item.data.length} items loaded`;
    }
    return 'Data loaded';
  };

  const getStatusColor = (item) => {
    if (item.loading) return 'status-loading';
    if (item.error) return 'status-error';
    if (!item.data || (Array.isArray(item.data) && item.data.length === 0)) return 'status-warning';
    return 'status-success';
  };

  if (activeView === 'validation') {
    return <DataValidationReport />;
  }

  if (activeView === 'summary') {
    return <DataSummaryReport />;
  }

  if (activeView === 'relationships') {
    return <RelationshipVerification />;
  }

  return (
    <div className="data-loading-status">
      <div className="status-header">
        <h3>📊 Data Loading Status</h3>
        <p>Real-time status of all data sources in the application</p>

        <div className="view-toggle">
          <button
            className={`toggle-btn ${activeView === 'status' ? 'active' : ''}`}
            onClick={() => setActiveView('status')}
          >
            📊 Status View
          </button>
          <button
            className={`toggle-btn ${activeView === 'summary' ? 'active' : ''}`}
            onClick={() => setActiveView('summary')}
          >
            📋 Data Summary
          </button>
          <button
            className={`toggle-btn ${activeView === 'relationships' ? 'active' : ''}`}
            onClick={() => setActiveView('relationships')}
          >
            🔗 Relationships
          </button>
          <button
            className={`toggle-btn ${activeView === 'validation' ? 'active' : ''}`}
            onClick={() => setActiveView('validation')}
          >
            🔍 Validation Report
          </button>
        </div>
      </div>

      <div className="status-grid">
        {dataStatus.map((item, index) => (
          <div key={index} className={`status-card ${getStatusColor(item)}`}>
            <div className="status-icon">
              {getStatusIcon(item)}
            </div>
            <div className="status-info">
              <h4>{item.name}</h4>
              <p className="status-text">{getStatusText(item)}</p>
              <p className="expected-text">{item.expected}</p>
              {item.error && (
                <div className="error-details">
                  <strong>Error Details:</strong>
                  <pre>{item.error}</pre>
                </div>
              )}
              {item.data && Array.isArray(item.data) && item.data.length > 0 && (
                <div className="data-preview">
                  <strong>Sample Data:</strong>
                  <pre>{JSON.stringify(item.data[0], null, 2).substring(0, 200)}...</pre>
                </div>
              )}
              {item.data && !Array.isArray(item.data) && (
                <div className="data-preview">
                  <strong>Data Structure:</strong>
                  <pre>{JSON.stringify(Object.keys(item.data), null, 2)}</pre>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="status-summary">
        <h4>📈 Summary</h4>
        <div className="summary-stats">
          <div className="stat">
            <span className="stat-label">Total Data Sources:</span>
            <span className="stat-value">{dataStatus.length}</span>
          </div>
          <div className="stat">
            <span className="stat-label">Successfully Loaded:</span>
            <span className="stat-value success">
              {dataStatus.filter(item => !item.loading && !item.error && item.data).length}
            </span>
          </div>
          <div className="stat">
            <span className="stat-label">Loading:</span>
            <span className="stat-value loading">
              {dataStatus.filter(item => item.loading).length}
            </span>
          </div>
          <div className="stat">
            <span className="stat-label">Errors:</span>
            <span className="stat-value error">
              {dataStatus.filter(item => item.error).length}
            </span>
          </div>
          <div className="stat">
            <span className="stat-label">Empty/Missing:</span>
            <span className="stat-value warning">
              {dataStatus.filter(item => !item.loading && !item.error && (!item.data || (Array.isArray(item.data) && item.data.length === 0))).length}
            </span>
          </div>
        </div>
      </div>

      <div className="status-actions">
        <button 
          className="action-btn refresh"
          onClick={() => window.location.reload()}
        >
          🔄 Refresh Page
        </button>
        <button 
          className="action-btn export"
          onClick={() => {
            const statusReport = {
              timestamp: new Date().toISOString(),
              dataStatus: dataStatus.map(item => ({
                name: item.name,
                hasData: !!item.data,
                dataLength: Array.isArray(item.data) ? item.data.length : 'N/A',
                loading: item.loading,
                error: item.error
              }))
            };
            const blob = new Blob([JSON.stringify(statusReport, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `data-status-report-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
          }}
        >
          📥 Export Report
        </button>
      </div>

      <div className="troubleshooting">
        <h4>🔧 Troubleshooting</h4>
        <div className="troubleshooting-tips">
          <div className="tip">
            <strong>If you see errors:</strong>
            <p>The backend API might not be running. Check if the server is started on port 5000.</p>
          </div>
          <div className="tip">
            <strong>If data is empty:</strong>
            <p>The application should fall back to sample data. If not, there might be a data loading issue.</p>
          </div>
          <div className="tip">
            <strong>If loading persists:</strong>
            <p>There might be a network issue or the API endpoints are not responding.</p>
          </div>
          <div className="tip">
            <strong>Missing "Application" section:</strong>
            <p>If you're looking for an "Application" section in any tab, it might be referring to a specific dashboard component that needs data. Check the tab content and ensure all required data is loaded.</p>
          </div>
        </div>
      </div>

      {/* API Endpoint Testing */}
      <div className="api-testing">
        <h4>🌐 API Endpoint Testing</h4>
        <div className="api-tests">
          <button
            className="test-btn"
            onClick={async () => {
              try {
                const response = await fetch('http://localhost:5000/health');
                const data = await response.json();
                alert(`Health Check: ${JSON.stringify(data)}`);
              } catch (error) {
                alert(`Health Check Failed: ${error.message}`);
              }
            }}
          >
            🏥 Test Health Endpoint
          </button>
          <button
            className="test-btn"
            onClick={async () => {
              try {
                const response = await fetch('http://localhost:5000/api/dashboard');
                const data = await response.json();
                alert(`Dashboard API: ${data.success ? 'Working' : 'Failed'}`);
              } catch (error) {
                alert(`Dashboard API Failed: ${error.message}`);
              }
            }}
          >
            📊 Test Dashboard API
          </button>
          <button
            className="test-btn"
            onClick={async () => {
              try {
                const response = await fetch('http://localhost:5000/api/business-cases');
                const data = await response.json();
                alert(`Business Cases API: ${data.success ? `${data.data.length} items` : 'Failed'}`);
              } catch (error) {
                alert(`Business Cases API Failed: ${error.message}`);
              }
            }}
          >
            💼 Test Business Cases API
          </button>
        </div>
      </div>
    </div>
  );
};

export default DataLoadingStatus;
