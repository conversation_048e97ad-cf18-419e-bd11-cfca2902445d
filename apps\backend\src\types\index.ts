export enum UserRole {
  ADMIN = 'admin',
  FINANCIAL_ANALYST = 'financial_analyst',
  PROJECT_MANAGER = 'project_manager',
  BUSINESS_ANALYST = 'business_analyst',
  VIEWER = 'viewer'
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}
