import React, { useState, useEffect } from 'react';

const IdeaPromotionModal = ({ isOpen, onClose, onPromote, ideas = [] }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('approved');
  const [selectedIdea, setSelectedIdea] = useState(null);
  const [filteredIdeas, setFilteredIdeas] = useState([]);

  useEffect(() => {
    if (isOpen) {
      // Filter ideas that are eligible for promotion
      const eligibleIdeas = ideas.filter(idea => {
        const matchesSearch = idea.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             idea.description.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus = !statusFilter || idea.status === statusFilter;
        const notAlreadyPromoted = !idea.promotedToBusinessCase; // Assuming this field exists
        
        return matchesSearch && matchesStatus && notAlreadyPromoted;
      });
      
      setFilteredIdeas(eligibleIdeas);
    }
  }, [isOpen, ideas, searchTerm, statusFilter]);

  const handlePromote = () => {
    if (selectedIdea) {
      onPromote(selectedIdea);
      setSelectedIdea(null);
      setSearchTerm('');
      onClose();
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      submitted: { color: 'bg-blue-100 text-blue-800', icon: 'fas fa-paper-plane' },
      'under-review': { color: 'bg-yellow-100 text-yellow-800', icon: 'fas fa-clock' },
      approved: { color: 'bg-green-100 text-green-800', icon: 'fas fa-check-circle' },
      rejected: { color: 'bg-red-100 text-red-800', icon: 'fas fa-times-circle' },
      'on-hold': { color: 'bg-orange-100 text-orange-800', icon: 'fas fa-pause-circle' }
    };

    const config = statusConfig[status] || statusConfig.submitted;
    
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <i className={`${config.icon} mr-1`}></i>
        {status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </span>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-green-500 to-blue-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Promote Idea to Business Case</h2>
              <p className="text-green-100 mt-1">
                Select an approved idea to convert into a comprehensive business case
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:bg-white hover:bg-opacity-20 rounded-lg p-2"
            >
              <i className="fas fa-times text-xl"></i>
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="p-6 border-b border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">Search Ideas</label>
              <div className="relative">
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Search by title or description..."
                />
                <i className="fas fa-search absolute left-3 top-3 text-gray-400"></i>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status Filter</label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Statuses</option>
                <option value="approved">Approved</option>
                <option value="under-review">Under Review</option>
                <option value="submitted">Submitted</option>
              </select>
            </div>
          </div>

          <div className="mt-4 flex items-center justify-between">
            <span className="text-sm text-gray-600">
              {filteredIdeas.length} eligible ideas found
            </span>
            {selectedIdea && (
              <span className="text-sm text-green-600 font-medium">
                <i className="fas fa-check-circle mr-1"></i>
                Selected: {selectedIdea.title}
              </span>
            )}
          </div>
        </div>

        {/* Ideas List */}
        <div className="p-6 max-h-96 overflow-y-auto">
          {filteredIdeas.length === 0 ? (
            <div className="text-center py-12">
              <i className="fas fa-lightbulb text-4xl text-gray-400 mb-4"></i>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No eligible ideas found</h3>
              <p className="text-gray-600">
                {searchTerm || statusFilter
                  ? 'Try adjusting your search criteria or filters'
                  : 'No approved ideas are available for promotion'
                }
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredIdeas.map(idea => (
                <div
                  key={idea.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                    selectedIdea?.id === idea.id
                      ? 'border-blue-500 bg-blue-50 shadow-md'
                      : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                  }`}
                  onClick={() => setSelectedIdea(idea)}
                >
                  {/* Idea Header */}
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1 line-clamp-2">
                        {idea.title}
                      </h4>
                      <p className="text-sm text-gray-600">{idea.category}</p>
                    </div>
                    <div className="ml-3 flex flex-col items-end space-y-2">
                      {getStatusBadge(idea.status)}
                      {selectedIdea?.id === idea.id && (
                        <i className="fas fa-check-circle text-blue-500 text-lg"></i>
                      )}
                    </div>
                  </div>

                  {/* Idea Description */}
                  <p className="text-sm text-gray-600 mb-3 line-clamp-3">
                    {idea.description}
                  </p>

                  {/* Idea Details */}
                  <div className="space-y-2">
                    {idea.businessUnit && (
                      <div className="flex items-center text-sm text-gray-600">
                        <i className="fas fa-building mr-2 w-4"></i>
                        <span>{idea.businessUnit}</span>
                      </div>
                    )}
                    
                    {idea.estimatedValue && (
                      <div className="flex items-center text-sm text-gray-600">
                        <i className="fas fa-dollar-sign mr-2 w-4"></i>
                        <span>${idea.estimatedValue.toLocaleString()}</span>
                      </div>
                    )}
                    
                    <div className="flex items-center text-sm text-gray-600">
                      <i className="fas fa-calendar mr-2 w-4"></i>
                      <span>Submitted {formatDate(idea.submissionDate || idea.createdAt)}</span>
                    </div>
                  </div>

                  {/* Tags */}
                  {idea.tags && idea.tags.length > 0 && (
                    <div className="mt-3">
                      <div className="flex flex-wrap gap-1">
                        {idea.tags.slice(0, 3).map((tag, index) => (
                          <span key={index} className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                            {tag}
                          </span>
                        ))}
                        {idea.tags.length > 3 && (
                          <span className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                            +{idea.tags.length - 3}
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Promotion Benefits */}
                  {selectedIdea?.id === idea.id && (
                    <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                      <h5 className="text-sm font-medium text-green-800 mb-2">
                        <i className="fas fa-arrow-up mr-1"></i>
                        Promotion Benefits
                      </h5>
                      <ul className="text-xs text-green-700 space-y-1">
                        <li>• Pre-filled business case form with idea details</li>
                        <li>• Maintains traceability from idea to business case</li>
                        <li>• Accelerated business case development process</li>
                        <li>• Automatic linking and relationship tracking</li>
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 flex items-center justify-between">
          <div className="text-sm text-gray-600">
            {selectedIdea ? (
              <span>
                <i className="fas fa-info-circle mr-1"></i>
                The selected idea will be used to pre-populate the business case form
              </span>
            ) : (
              <span>Select an idea to promote to business case</span>
            )}
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handlePromote}
              disabled={!selectedIdea}
              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <i className="fas fa-arrow-up mr-2"></i>
              Promote to Business Case
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IdeaPromotionModal;
