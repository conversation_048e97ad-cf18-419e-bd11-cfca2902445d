# Port Configuration Guide

## Default Port Assignments

| Service | Port | Environment Variable | Configuration File |
|---------|------|---------------------|-------------------|
| Frontend (React) | 3000 | `PORT` | `apps/frontend/.env` |
| Backend (Node.js) | 5001 | `PORT` | `apps/backend/.env` |
| MongoDB | 27017 | `MONGODB_URI` | `apps/backend/.env` |

## Common Port Issues & Solutions

### Issue: "Port 5000 is already in use"
**Cause:** Windows AirPlay service or other applications using port 5000
**Solution:** Use port 5001 for backend (already configured)

### Issue: Frontend can't connect to backend
**Cause:** Port mismatch between frontend API config and backend server
**Solution:** 
1. Check `apps/frontend/src/services/api.js` line 4
2. Ensure it matches the backend port in `apps/backend/.env`
3. Update `REACT_APP_API_BASE_URL` in `apps/frontend/.env`

### Issue: "EADDRINUSE" error
**Cause:** Port already occupied by another process
**Solution:**
```bash
# Find process using port
netstat -ano | findstr :5001
# Kill process (replace PID)
taskkill /PID <PID> /F
```

## Quick Port Check Commands

```bash
# Check if ports are available
netstat -an | findstr :3000
netstat -an | findstr :5001

# Test API connection
curl http://localhost:5001/health

# Test frontend
curl http://localhost:3000
```

## Environment Variables Setup

### Frontend (.env)
```
REACT_APP_API_BASE_URL=http://localhost:5001
```

### Backend (.env)
```
PORT=5001
```

## Troubleshooting Checklist

- [ ] Backend server is running on port 5001
- [ ] Frontend is configured to connect to port 5001
- [ ] No other services are using these ports
- [ ] Environment variables are loaded correctly
- [ ] CORS is configured for localhost:3000

## Prevention Tips

1. **Always use environment variables** for port configuration
2. **Document port assignments** in README files
3. **Use consistent ports** across development team
4. **Check port availability** before starting services
5. **Use docker-compose** for consistent environments
