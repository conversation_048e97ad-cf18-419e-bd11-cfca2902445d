/**
 * Database Migration Utility
 * Handles data migration between JSON files and MongoDB
 */

const fs = require('fs');
const path = require('path');
const DatabaseAdapter = require('./database-adapter');
const dbConfig = require('./database-config');

class DatabaseMigration {
    constructor() {
        this.jsonDataDir = path.join(__dirname, 'data');
        this.collections = [
            'businessCases',
            'masterBusinessCases', 
            'projects',
            'programs',
            'ideas',
            'users',
            'feedback',
            'milestones',
            'auditLog'
        ];
    }

    /**
     * Migrate data from JSON files to MongoDB
     */
    async migrateJSONToMongoDB(mongoConfig = null) {
        console.log('\n🔄 Starting JSON to MongoDB migration...');
        
        try {
            // Initialize MongoDB adapter
            const config = mongoConfig || dbConfig.getMongoConfig();
            const mongoAdapter = new DatabaseAdapter({
                mode: 'mongodb',
                ...config
            });

            // Wait for MongoDB connection
            await new Promise(resolve => setTimeout(resolve, 2000));

            let totalMigrated = 0;
            const migrationReport = {
                success: [],
                errors: [],
                summary: {}
            };

            // Migrate each collection
            for (const collection of this.collections) {
                try {
                    console.log(`📦 Migrating ${collection}...`);
                    
                    // Read JSON data
                    const jsonFile = path.join(this.jsonDataDir, `${collection}.json`);
                    if (!fs.existsSync(jsonFile)) {
                        console.log(`⚠️  ${collection}.json not found, skipping...`);
                        continue;
                    }

                    const jsonData = JSON.parse(fs.readFileSync(jsonFile, 'utf8'));
                    
                    if (!Array.isArray(jsonData) || jsonData.length === 0) {
                        console.log(`📭 ${collection} is empty, skipping...`);
                        migrationReport.summary[collection] = { count: 0, status: 'empty' };
                        continue;
                    }

                    // Write to MongoDB
                    const success = await mongoAdapter.writeCollection(collection, jsonData, 'migration');
                    
                    if (success) {
                        console.log(`✅ ${collection}: ${jsonData.length} documents migrated`);
                        migrationReport.success.push(collection);
                        migrationReport.summary[collection] = { 
                            count: jsonData.length, 
                            status: 'success' 
                        };
                        totalMigrated += jsonData.length;
                    } else {
                        throw new Error('Write operation failed');
                    }

                } catch (error) {
                    console.error(`❌ Error migrating ${collection}:`, error.message);
                    migrationReport.errors.push({
                        collection,
                        error: error.message
                    });
                    migrationReport.summary[collection] = { 
                        count: 0, 
                        status: 'error',
                        error: error.message 
                    };
                }
            }

            await mongoAdapter.close();

            // Print migration report
            console.log('\n📊 Migration Report');
            console.log('==================');
            console.log(`Total documents migrated: ${totalMigrated}`);
            console.log(`Successful collections: ${migrationReport.success.length}`);
            console.log(`Failed collections: ${migrationReport.errors.length}`);
            
            if (migrationReport.errors.length > 0) {
                console.log('\n❌ Errors:');
                migrationReport.errors.forEach(error => {
                    console.log(`  - ${error.collection}: ${error.error}`);
                });
            }

            return migrationReport;

        } catch (error) {
            console.error('❌ Migration failed:', error.message);
            throw error;
        }
    }

    /**
     * Migrate data from MongoDB to JSON files
     */
    async migrateMongoDBToJSON(mongoConfig = null) {
        console.log('\n🔄 Starting MongoDB to JSON migration...');
        
        try {
            // Initialize MongoDB adapter
            const config = mongoConfig || dbConfig.getMongoConfig();
            const mongoAdapter = new DatabaseAdapter({
                mode: 'mongodb',
                ...config
            });

            // Wait for MongoDB connection
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Ensure JSON data directory exists
            if (!fs.existsSync(this.jsonDataDir)) {
                fs.mkdirSync(this.jsonDataDir, { recursive: true });
            }

            let totalMigrated = 0;
            const migrationReport = {
                success: [],
                errors: [],
                summary: {}
            };

            // Migrate each collection
            for (const collection of this.collections) {
                try {
                    console.log(`📦 Migrating ${collection}...`);
                    
                    // Read from MongoDB
                    const mongoData = await mongoAdapter.readCollection(collection);
                    
                    if (!Array.isArray(mongoData) || mongoData.length === 0) {
                        console.log(`📭 ${collection} is empty, creating empty file...`);
                        fs.writeFileSync(
                            path.join(this.jsonDataDir, `${collection}.json`),
                            JSON.stringify([], null, 2)
                        );
                        migrationReport.summary[collection] = { count: 0, status: 'empty' };
                        continue;
                    }

                    // Write to JSON file
                    const jsonFile = path.join(this.jsonDataDir, `${collection}.json`);
                    fs.writeFileSync(jsonFile, JSON.stringify(mongoData, null, 2));
                    
                    console.log(`✅ ${collection}: ${mongoData.length} documents migrated`);
                    migrationReport.success.push(collection);
                    migrationReport.summary[collection] = { 
                        count: mongoData.length, 
                        status: 'success' 
                    };
                    totalMigrated += mongoData.length;

                } catch (error) {
                    console.error(`❌ Error migrating ${collection}:`, error.message);
                    migrationReport.errors.push({
                        collection,
                        error: error.message
                    });
                    migrationReport.summary[collection] = { 
                        count: 0, 
                        status: 'error',
                        error: error.message 
                    };
                }
            }

            await mongoAdapter.close();

            // Print migration report
            console.log('\n📊 Migration Report');
            console.log('==================');
            console.log(`Total documents migrated: ${totalMigrated}`);
            console.log(`Successful collections: ${migrationReport.success.length}`);
            console.log(`Failed collections: ${migrationReport.errors.length}`);
            
            if (migrationReport.errors.length > 0) {
                console.log('\n❌ Errors:');
                migrationReport.errors.forEach(error => {
                    console.log(`  - ${error.collection}: ${error.error}`);
                });
            }

            return migrationReport;

        } catch (error) {
            console.error('❌ Migration failed:', error.message);
            throw error;
        }
    }

    /**
     * Backup JSON data before migration
     */
    async backupJSONData() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupDir = path.join(__dirname, `backup-${timestamp}`);
        
        try {
            fs.mkdirSync(backupDir, { recursive: true });
            
            for (const collection of this.collections) {
                const sourceFile = path.join(this.jsonDataDir, `${collection}.json`);
                const backupFile = path.join(backupDir, `${collection}.json`);
                
                if (fs.existsSync(sourceFile)) {
                    fs.copyFileSync(sourceFile, backupFile);
                }
            }
            
            console.log(`✅ JSON data backed up to: ${backupDir}`);
            return backupDir;
            
        } catch (error) {
            console.error('❌ Backup failed:', error.message);
            throw error;
        }
    }

    /**
     * Validate data integrity after migration
     */
    async validateMigration(sourceMode, targetMode) {
        console.log('\n🔍 Validating migration...');
        
        try {
            const sourceAdapter = new DatabaseAdapter({ mode: sourceMode });
            const targetAdapter = new DatabaseAdapter({ mode: targetMode });
            
            // Wait for connections
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const validationReport = {
                valid: true,
                collections: {},
                errors: []
            };

            for (const collection of this.collections) {
                try {
                    const sourceData = await sourceAdapter.readCollection(collection);
                    const targetData = await targetAdapter.readCollection(collection);
                    
                    const sourceCount = Array.isArray(sourceData) ? sourceData.length : 0;
                    const targetCount = Array.isArray(targetData) ? targetData.length : 0;
                    
                    const isValid = sourceCount === targetCount;
                    
                    validationReport.collections[collection] = {
                        sourceCount,
                        targetCount,
                        valid: isValid
                    };
                    
                    if (!isValid) {
                        validationReport.valid = false;
                        validationReport.errors.push(
                            `${collection}: count mismatch (source: ${sourceCount}, target: ${targetCount})`
                        );
                    }
                    
                    console.log(`${isValid ? '✅' : '❌'} ${collection}: ${sourceCount} → ${targetCount}`);
                    
                } catch (error) {
                    validationReport.valid = false;
                    validationReport.errors.push(`${collection}: ${error.message}`);
                    console.error(`❌ ${collection}: ${error.message}`);
                }
            }

            await sourceAdapter.close();
            await targetAdapter.close();

            console.log(`\n${validationReport.valid ? '✅' : '❌'} Migration validation ${validationReport.valid ? 'passed' : 'failed'}`);
            
            return validationReport;

        } catch (error) {
            console.error('❌ Validation failed:', error.message);
            throw error;
        }
    }
}

// CLI interface
if (require.main === module) {
    const migration = new DatabaseMigration();
    const command = process.argv[2];
    
    async function runMigration() {
        try {
            switch (command) {
                case 'json-to-mongo':
                    await migration.backupJSONData();
                    await migration.migrateJSONToMongoDB();
                    await migration.validateMigration('json', 'mongodb');
                    break;
                    
                case 'mongo-to-json':
                    await migration.backupJSONData();
                    await migration.migrateMongoDBToJSON();
                    await migration.validateMigration('mongodb', 'json');
                    break;
                    
                case 'backup':
                    await migration.backupJSONData();
                    break;
                    
                case 'validate':
                    const sourceMode = process.argv[3] || 'json';
                    const targetMode = process.argv[4] || 'mongodb';
                    await migration.validateMigration(sourceMode, targetMode);
                    break;
                    
                default:
                    console.log('Usage:');
                    console.log('  node database-migration.js json-to-mongo');
                    console.log('  node database-migration.js mongo-to-json');
                    console.log('  node database-migration.js backup');
                    console.log('  node database-migration.js validate [source] [target]');
            }
        } catch (error) {
            console.error('❌ Migration failed:', error.message);
            process.exit(1);
        }
    }
    
    runMigration();
}

module.exports = DatabaseMigration;
