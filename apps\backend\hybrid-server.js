/**
 * Hybrid Business Case Server
 * Supports both JSON files (development) and MongoDB (production)
 */

const http = require('http');
const url = require('url');
const fs = require('fs');
const path = require('path');

// Import hybrid database system
const DatabaseAdapter = require('./database-adapter');
const dbConfig = require('./database-config');

// Initialize database adapter
let dbAdapter;

async function initializeDatabase() {
    try {
        const config = dbConfig.getFullConfig();
        dbAdapter = new DatabaseAdapter(config);
        
        // Print database configuration
        dbConfig.printSummary();
        
        // Wait for database initialization
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Perform health check
        const health = await dbAdapter.healthCheck();
        console.log(`🏥 Database health: ${health.status} (${health.mode})`);
        
        if (health.status === 'unhealthy') {
            console.warn('⚠️ Database health check failed:', health.error);
        }
        
        return true;
    } catch (error) {
        console.error('❌ Database initialization failed:', error.message);
        return false;
    }
}

// Helper functions using the hybrid database adapter
async function readCollection(collectionName) {
    try {
        return await dbAdapter.readCollection(collectionName);
    } catch (error) {
        console.error(`Error reading collection ${collectionName}:`, error.message);
        return [];
    }
}

async function writeCollection(collectionName, data, userId = 'system') {
    try {
        return await dbAdapter.writeCollection(collectionName, data, userId);
    } catch (error) {
        console.error(`Error writing collection ${collectionName}:`, error.message);
        return false;
    }
}

async function insertDocument(collectionName, document) {
    try {
        return await dbAdapter.insertDocument(collectionName, document);
    } catch (error) {
        console.error(`Error inserting document in ${collectionName}:`, error.message);
        return null;
    }
}

async function updateDocument(collectionName, filter, update) {
    try {
        return await dbAdapter.updateDocument(collectionName, filter, update);
    } catch (error) {
        console.error(`Error updating document in ${collectionName}:`, error.message);
        return false;
    }
}

async function deleteDocument(collectionName, filter) {
    try {
        return await dbAdapter.deleteDocument(collectionName, filter);
    } catch (error) {
        console.error(`Error deleting document in ${collectionName}:`, error.message);
        return false;
    }
}

async function findDocuments(collectionName, query = {}, options = {}) {
    try {
        return await dbAdapter.findDocuments(collectionName, query, options);
    } catch (error) {
        console.error(`Error finding documents in ${collectionName}:`, error.message);
        return [];
    }
}

function generateId() {
    return `id_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Helper function to parse JSON body
async function getRequestBody(req) {
    return new Promise((resolve, reject) => {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        req.on('end', () => {
            try {
                resolve(body);
            } catch (error) {
                reject(error);
            }
        });
    });
}

// Helper function to send JSON response
function sendJSON(res, statusCode, data) {
    res.writeHead(statusCode, {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end(JSON.stringify(data));
}

// Create HTTP server
const server = http.createServer(async (req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const pathName = parsedUrl.pathname;
    const method = req.method;
    const query = parsedUrl.query;

    console.log(`${method} ${pathName}`);

    // Handle CORS preflight
    if (method === 'OPTIONS') {
        res.writeHead(200, {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        });
        res.end();
        return;
    }

    try {
        // Health check with database status
        if (pathName === '/health' && method === 'GET') {
            const dbHealth = await dbAdapter.healthCheck();
            const systemStats = await dbAdapter.getSystemStats();
            
            sendJSON(res, 200, {
                status: 'OK',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                service: 'Hybrid Business Case Management API',
                database: {
                    mode: dbHealth.mode,
                    status: dbHealth.status,
                    error: dbHealth.error || null
                },
                features: ['Hybrid Storage', 'CRUD Operations', 'Financial Calculations', 'Migration Support'],
                stats: systemStats
            });
            return;
        }

        // Database mode endpoint
        if (pathName === '/api/database/mode' && method === 'GET') {
            const config = dbConfig.getFullConfig();
            sendJSON(res, 200, {
                success: true,
                data: {
                    mode: config.mode,
                    environment: config.environment,
                    validation: config.validation
                }
            });
            return;
        }

        // Database statistics endpoint
        if (pathName === '/api/database/stats' && method === 'GET') {
            const stats = await dbAdapter.getSystemStats();
            sendJSON(res, 200, {
                success: true,
                data: stats
            });
            return;
        }

        // Business Cases endpoints
        if (pathName === '/api/business-cases' && method === 'GET') {
            const businessCases = await readCollection('businessCases');
            sendJSON(res, 200, {
                success: true,
                data: businessCases,
                count: businessCases.length,
                mode: dbAdapter.mode
            });
            return;
        }

        if (pathName === '/api/business-cases' && method === 'POST') {
            try {
                const body = await getRequestBody(req);
                const data = JSON.parse(body);
                
                const newBusinessCase = {
                    _id: generateId(),
                    ...data,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };

                const insertedId = await insertDocument('businessCases', newBusinessCase);
                
                if (insertedId) {
                    sendJSON(res, 201, {
                        success: true,
                        data: newBusinessCase,
                        message: 'Business case created successfully'
                    });
                } else {
                    throw new Error('Failed to insert business case');
                }
            } catch (error) {
                console.error('Error creating business case:', error);
                sendJSON(res, 500, {
                    success: false,
                    error: 'Failed to create business case'
                });
            }
            return;
        }

        // Master Business Cases endpoints
        if (pathName === '/api/master-business-cases' && method === 'GET') {
            const masterBCs = await readCollection('masterBusinessCases');
            sendJSON(res, 200, {
                success: true,
                data: masterBCs,
                count: masterBCs.length,
                mode: dbAdapter.mode
            });
            return;
        }

        if (pathName === '/api/master-business-cases' && method === 'POST') {
            try {
                const body = await getRequestBody(req);
                const data = JSON.parse(body);
                
                const newMasterBC = {
                    _id: generateId(),
                    ...data,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };

                const insertedId = await insertDocument('masterBusinessCases', newMasterBC);
                
                if (insertedId) {
                    sendJSON(res, 201, {
                        success: true,
                        data: newMasterBC,
                        message: 'Master business case created successfully'
                    });
                } else {
                    throw new Error('Failed to insert master business case');
                }
            } catch (error) {
                console.error('Error creating master business case:', error);
                sendJSON(res, 500, {
                    success: false,
                    error: 'Failed to create master business case'
                });
            }
            return;
        }

        // Projects endpoints
        if (pathName === '/api/projects' && method === 'GET') {
            const projects = await readCollection('projects');
            sendJSON(res, 200, {
                success: true,
                data: projects,
                count: projects.length,
                mode: dbAdapter.mode
            });
            return;
        }

        // Programs endpoints
        if (pathName === '/api/programs' && method === 'GET') {
            const programs = await readCollection('programs');
            sendJSON(res, 200, {
                success: true,
                data: programs,
                count: programs.length,
                mode: dbAdapter.mode
            });
            return;
        }

        // Ideas endpoints
        if (pathName === '/api/ideas' && method === 'GET') {
            const ideas = await readCollection('ideas');
            sendJSON(res, 200, {
                success: true,
                data: ideas,
                count: ideas.length,
                mode: dbAdapter.mode
            });
            return;
        }

        // Authentication endpoints
        if (pathName === '/api/auth/login' && method === 'POST') {
            try {
                const body = await getRequestBody(req);
                const { email, password } = JSON.parse(body);

                console.log('Login attempt:', { email, password });

                // Get users from database
                const users = await readCollection('users');
                const user = users.find(u => u.email === email && u.password === password);

                if (user) {
                    sendJSON(res, 200, {
                        success: true,
                        data: {
                            token: 'mock-jwt-token-' + user._id,
                            user: {
                                id: user._id,
                                name: user.name,
                                email: user.email,
                                role: user.role,
                                createdAt: user.createdAt || new Date(),
                                updatedAt: user.updatedAt || new Date()
                            }
                        }
                    });
                } else {
                    sendJSON(res, 400, {
                        success: false,
                        error: 'Invalid credentials'
                    });
                }
            } catch (error) {
                console.error('Login error:', error);
                sendJSON(res, 500, {
                    success: false,
                    error: 'Login failed'
                });
            }
            return;
        }

        // Token validation endpoint
        if (pathName === '/api/auth/validate' && method === 'GET') {
            try {
                const authHeader = req.headers.authorization;
                if (authHeader && authHeader.startsWith('Bearer ')) {
                    const token = authHeader.substring(7);
                    const userId = token.replace('mock-jwt-token-', '');

                    const users = await readCollection('users');
                    const user = users.find(u => u._id === userId);

                    if (user) {
                        sendJSON(res, 200, {
                            success: true,
                            data: {
                                user: {
                                    id: user._id,
                                    name: user.name,
                                    email: user.email,
                                    role: user.role,
                                    createdAt: user.createdAt || new Date(),
                                    updatedAt: user.updatedAt || new Date()
                                }
                            }
                        });
                    } else {
                        sendJSON(res, 401, {
                            success: false,
                            error: 'Invalid token'
                        });
                    }
                } else {
                    sendJSON(res, 401, {
                        success: false,
                        error: 'No token provided'
                    });
                }
            } catch (error) {
                console.error('Token validation error:', error);
                sendJSON(res, 500, {
                    success: false,
                    error: 'Token validation failed'
                });
            }
            return;
        }

        // 404 for unknown routes
        sendJSON(res, 404, {
            success: false,
            error: 'Route not found',
            availableEndpoints: [
                'GET /health',
                'GET /api/database/mode',
                'GET /api/database/stats',
                'POST /api/auth/login',
                'GET /api/auth/validate',
                'GET /api/business-cases',
                'POST /api/business-cases',
                'GET /api/master-business-cases',
                'POST /api/master-business-cases',
                'GET /api/projects',
                'GET /api/programs',
                'GET /api/ideas'
            ]
        });

    } catch (error) {
        console.error('Server error:', error);
        sendJSON(res, 500, {
            success: false,
            error: 'Internal server error'
        });
    }
});

// Start server
async function startServer() {
    const PORT = process.env.PORT || 5002;
    
    console.log('🚀 Initializing Hybrid Business Case Server...');
    
    // Initialize database
    const dbInitialized = await initializeDatabase();
    
    if (!dbInitialized) {
        console.error('❌ Failed to initialize database. Exiting...');
        process.exit(1);
    }
    
    // Start HTTP server
    server.listen(PORT, () => {
        console.log(`\n🌟 Hybrid Business Case Server running on port ${PORT}`);
        console.log(`📊 Database Mode: ${dbAdapter.mode.toUpperCase()}`);
        console.log(`🔗 Health Check: http://localhost:${PORT}/health`);
        console.log(`📡 API Base: http://localhost:${PORT}/api`);
        console.log('=====================================\n');
    });
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down server...');
    
    if (dbAdapter) {
        await dbAdapter.close();
    }
    
    server.close(() => {
        console.log('✅ Server shut down gracefully');
        process.exit(0);
    });
});

// Start the server
startServer().catch(error => {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
});

module.exports = { server, dbAdapter };
