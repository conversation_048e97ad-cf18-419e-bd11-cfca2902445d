import React, { useState, useEffect } from 'react';
import { projectService } from '../../../../services/projectService';
import './ProjectEpicForm.css';

const ProjectEpicForm = ({ project, businessCases, programs, onSuccess, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'project', // 'project' or 'epic'
    startDate: '',
    endDate: '',
    owner: '',
    status: 'planning',
    priority: 'medium',
    associatedProgram: '',
    linkedBusinessCases: [],
    milestones: []
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (project) {
      setFormData({
        name: project.name || '',
        description: project.description || '',
        type: project.type || 'project',
        startDate: project.startDate || '',
        endDate: project.endDate || '',
        owner: project.owner || '',
        status: project.status || 'planning',
        priority: project.priority || 'medium',
        associatedProgram: project.associatedProgram || '',
        linkedBusinessCases: project.linkedBusinessCases || [],
        milestones: project.milestones || []
      });
    }
  }, [project]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleBusinessCaseToggle = (bcId) => {
    setFormData(prev => ({
      ...prev,
      linkedBusinessCases: prev.linkedBusinessCases.includes(bcId)
        ? prev.linkedBusinessCases.filter(id => id !== bcId)
        : [...prev.linkedBusinessCases, bcId]
    }));
  };

  const handleAddMilestone = () => {
    const newMilestone = {
      id: Date.now().toString(),
      name: '',
      description: '',
      dueDate: '',
      status: 'pending'
    };
    setFormData(prev => ({
      ...prev,
      milestones: [...prev.milestones, newMilestone]
    }));
  };

  const handleMilestoneChange = (milestoneId, field, value) => {
    setFormData(prev => ({
      ...prev,
      milestones: prev.milestones.map(milestone =>
        milestone.id === milestoneId
          ? { ...milestone, [field]: value }
          : milestone
      )
    }));
  };

  const handleRemoveMilestone = (milestoneId) => {
    setFormData(prev => ({
      ...prev,
      milestones: prev.milestones.filter(milestone => milestone.id !== milestoneId)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      if (project) {
        await projectService.updateProject(project.id, formData);
      } else {
        await projectService.createProject(formData);
      }
      onSuccess();
    } catch (err) {
      setError(err.message || 'Failed to save project/epic');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="project-epic-form">
      <div className="form-header">
        <h3>{project ? 'Edit' : 'Create'} {formData.type === 'epic' ? 'Epic' : 'Project'}</h3>
        <p>Configure project/epic details, link business cases, and define milestones</p>
      </div>

      {error && (
        <div className="error-message">
          <i className="fas fa-exclamation-triangle"></i>
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="form-content">
        {/* Basic Information */}
        <div className="form-section">
          <h4>Basic Information</h4>
          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="type">Type *</label>
              <select
                id="type"
                name="type"
                value={formData.type}
                onChange={handleChange}
                required
              >
                <option value="project">Project</option>
                <option value="epic">Epic</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="name">Name *</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                placeholder={`Enter ${formData.type} name`}
              />
            </div>

            <div className="form-group full-width">
              <label htmlFor="description">Description</label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows={3}
                placeholder={`Enter ${formData.type} description`}
              />
            </div>

            <div className="form-group">
              <label htmlFor="startDate">Start Date</label>
              <input
                type="date"
                id="startDate"
                name="startDate"
                value={formData.startDate}
                onChange={handleChange}
              />
            </div>

            <div className="form-group">
              <label htmlFor="endDate">End Date</label>
              <input
                type="date"
                id="endDate"
                name="endDate"
                value={formData.endDate}
                onChange={handleChange}
              />
            </div>

            <div className="form-group">
              <label htmlFor="owner">Owner/Manager</label>
              <input
                type="text"
                id="owner"
                name="owner"
                value={formData.owner}
                onChange={handleChange}
                placeholder="Enter owner name or email"
              />
            </div>

            <div className="form-group">
              <label htmlFor="status">Status</label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
              >
                <option value="planning">Planning</option>
                <option value="active">Active</option>
                <option value="on-hold">On Hold</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="priority">Priority</label>
              <select
                id="priority"
                name="priority"
                value={formData.priority}
                onChange={handleChange}
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="critical">Critical</option>
              </select>
            </div>
          </div>
        </div>

        {/* Program Association */}
        <div className="form-section">
          <h4>Program Association</h4>
          <div className="form-group">
            <label htmlFor="associatedProgram">Associated Program</label>
            <select
              id="associatedProgram"
              name="associatedProgram"
              value={formData.associatedProgram}
              onChange={handleChange}
            >
              <option value="">Select a program (optional)</option>
              {programs && programs.map(program => (
                <option key={program.id} value={program.id}>
                  {program.name}
                </option>
              ))}
            </select>
            <small className="form-help">
              Link this {formData.type} to a program for better organization
            </small>
          </div>
        </div>

        {/* Business Case Linking */}
        <div className="form-section">
          <h4>Business Case Connections</h4>
          <p className="section-description">
            Select business cases that this {formData.type} will implement or support
          </p>
          <div className="business-cases-grid">
            {businessCases && businessCases.length > 0 ? (
              businessCases.map(bc => (
                <div key={bc.id} className="business-case-item">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={formData.linkedBusinessCases.includes(bc.id)}
                      onChange={() => handleBusinessCaseToggle(bc.id)}
                    />
                    <div className="checkbox-content">
                      <div className="bc-name">{bc.name}</div>
                      <div className="bc-details">
                        <span className="bc-unit">{bc.businessUnit}</span>
                        <span className="bc-status">{bc.status}</span>
                      </div>
                    </div>
                  </label>
                </div>
              ))
            ) : (
              <div className="no-business-cases">
                <p>No business cases available for linking</p>
              </div>
            )}
          </div>
        </div>

        {/* Milestones */}
        <div className="form-section">
          <div className="section-header">
            <h4>Milestones</h4>
            <button
              type="button"
              onClick={handleAddMilestone}
              className="btn btn-outline btn-sm"
            >
              <i className="fas fa-plus"></i>
              Add Milestone
            </button>
          </div>
          
          {formData.milestones.length > 0 ? (
            <div className="milestones-list">
              {formData.milestones.map(milestone => (
                <div key={milestone.id} className="milestone-item">
                  <div className="milestone-header">
                    <input
                      type="text"
                      value={milestone.name}
                      onChange={(e) => handleMilestoneChange(milestone.id, 'name', e.target.value)}
                      placeholder="Milestone name"
                      className="milestone-name"
                    />
                    <button
                      type="button"
                      onClick={() => handleRemoveMilestone(milestone.id)}
                      className="btn btn-danger btn-sm"
                    >
                      <i className="fas fa-trash"></i>
                    </button>
                  </div>
                  <div className="milestone-details">
                    <input
                      type="text"
                      value={milestone.description}
                      onChange={(e) => handleMilestoneChange(milestone.id, 'description', e.target.value)}
                      placeholder="Milestone description"
                      className="milestone-description"
                    />
                    <input
                      type="date"
                      value={milestone.dueDate}
                      onChange={(e) => handleMilestoneChange(milestone.id, 'dueDate', e.target.value)}
                      className="milestone-date"
                    />
                    <select
                      value={milestone.status}
                      onChange={(e) => handleMilestoneChange(milestone.id, 'status', e.target.value)}
                      className="milestone-status"
                    >
                      <option value="pending">Pending</option>
                      <option value="in-progress">In Progress</option>
                      <option value="completed">Completed</option>
                      <option value="delayed">Delayed</option>
                    </select>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="no-milestones">
              <p>No milestones defined. Click "Add Milestone" to create one.</p>
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="form-actions">
          <button type="button" onClick={onCancel} className="btn btn-secondary">
            Cancel
          </button>
          <button type="submit" disabled={loading} className="btn btn-primary">
            {loading ? (
              <>
                <i className="fas fa-spinner fa-spin"></i>
                Saving...
              </>
            ) : (
              <>
                <i className="fas fa-save"></i>
                {project ? 'Update' : 'Create'} {formData.type === 'epic' ? 'Epic' : 'Project'}
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProjectEpicForm;
