# 🚀 **Quick Launch Guide - Master Business Case Management**

## **⚡ Super Quick Start (2 Minutes)**

### **Step 1: Open Terminal/Command Prompt**
- **Windows**: Press `Win + R`, type `cmd`, press Enter
- **Mac**: Press `Cmd + Space`, type `terminal`, press Enter
- **Linux**: Press `Ctrl + Alt + T`

### **Step 2: Navigate to Frontend Directory**
```bash
cd spm
cd apps/frontend
```

### **Step 3: Install & Launch**
```bash
npm install && npm start
```

### **Step 4: Wait & Access**
- Wait for "Compiled successfully!" message
- Browser opens automatically to http://localhost:3000
- **Done! 🎉**

---

## **📋 Detailed Step-by-Step Instructions**

### **Prerequisites Check**
```bash
# Check if Node.js is installed (should show version 16+)
node --version

# Check if npm is installed
npm --version
```

**If not installed**: Download Node.js from https://nodejs.org/

### **Launch Process**

#### **Step 1: Open Terminal**
- Navigate to your project folder location

#### **Step 2: Navigate to Frontend Directory**
```bash
# Navigate to the project root
cd spm

# Go to the React frontend application
cd apps/frontend
```

#### **Step 3: Install Dependencies (First Time Only)**
```bash
npm install
```
*This downloads all required packages (takes 2-3 minutes)*

#### **Step 4: Start the Application**
```bash
npm start
```

#### **Step 5: Wait for Success Message**
Look for this in your terminal:
```
Compiled successfully!

You can now view business-case-management-react in the browser.

  Local:            http://localhost:3000
  On Your Network:  http://192.168.x.x:3000
```

#### **Step 6: Access Application**
- Browser should open automatically
- If not, manually go to: **http://localhost:3000**

---

## **🔧 Backend Setup (Optional)**

The application works with JSON files by default. For full API functionality:

### **Backend Launch Steps**
```bash
# Open a new terminal window
# Navigate to backend directory
cd spm/apps/backend

# Install backend dependencies
npm install

# Start the backend server
npm start
```

**Backend URL**: http://localhost:5000

---

## **🎯 What You'll See**

### **Application Interface**
- **Header**: Master Business Case Management title
- **Navigation Tabs**: Ideas, Business Cases, Master Business Cases, etc.
- **Sample Data**: Pre-loaded examples ready to explore

### **Main Features to Try**
1. **Ideas Tab**: Create new business ideas
2. **Business Cases Tab**: View financial models and calculations
3. **Strategic Portfolio Tab**: See executive dashboard
4. **Business Architecture Tab**: View financial formulas documentation

---

## **🛑 Common Issues & Quick Fixes**

### **Issue: Port 3000 Already in Use**
```bash
# Kill the process
npx kill-port 3000
# Then restart
npm start
```

### **Issue: Module Not Found**
```bash
# Clean reinstall
rm -rf node_modules
npm install
npm start
```

### **Issue: Browser Doesn't Open**
- Manually go to: http://localhost:3000
- Try different browser
- Clear browser cache (Ctrl+F5)

### **Issue: Application Loads Slowly**
- Close other applications
- Restart your computer
- Check internet connection

---

## **⚡ Quick Commands Reference**

```bash
# Start application
npm start

# Stop application (in terminal)
Ctrl+C

# Restart application
Ctrl+C, then npm start

# Check if running
# Look for "Compiled successfully!" message
```

---

## **🔧 System Requirements**

### **Minimum Requirements**
- **Node.js**: Version 16.0+
- **RAM**: 4GB minimum (8GB recommended)
- **Storage**: 1GB free space
- **Browser**: Chrome, Firefox, Safari, or Edge

### **Supported Operating Systems**
- Windows 10/11
- macOS 10.15+
- Linux (Ubuntu, CentOS, etc.)

---

## **📞 Need Help?**

### **Check These First**
1. **Terminal Messages**: Look for error messages in command prompt
2. **Browser Console**: Press F12, check Console tab for errors
3. **Node.js Version**: Run `node --version` (should be 16+)

### **Quick Restart**
```bash
# Stop the application
Ctrl+C

# Start again
npm start
```

### **Complete Reset**
```bash
# Stop application
Ctrl+C

# Clean install
rm -rf node_modules package-lock.json
npm install

# Start fresh
npm start
```

---

## **🎉 Success!**

When you see the Master Business Case Management interface with navigation tabs and sample data, you're ready to start exploring the application!

**Happy Business Case Management! 🚀**

---

*For detailed documentation, see the main README.md file*
