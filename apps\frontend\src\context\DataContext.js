import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { businessCaseService } from '../services/businessCaseService';
import { dashboardService } from '../services/dashboardService';
import { projectService } from '../services/projectService';
import { ideaService } from '../services/ideaService';
import { useAuth } from './AuthContext';
import { sampleMasterBCs, samplePrograms } from '../data/samplePortfolioData';

// Enhanced sample data for better fallback coverage
const sampleBusinessCases = [
  {
    id: 'bc_sample_1',
    name: 'Digital Customer Experience Platform',
    description: 'Comprehensive digital platform for enhanced customer experience',
    businessUnit: 'Customer Service',
    status: 'approved',
    totalCapex: 500000,
    totalOpex: 300000,
    totalRevenue: 1200000,
    irr: 25.5,
    npv: 450000,
    paybackPeriod: 2.1,
    grossMargin: 0.65,
    commercialMargin: 0.45,
    createdBy: '<EMAIL>',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'bc_sample_2',
    name: 'Supply Chain Optimization System',
    description: 'AI-powered supply chain optimization and tracking',
    businessUnit: 'Operations',
    status: 'approved',
    totalCapex: 750000,
    totalOpex: 450000,
    totalRevenue: 1800000,
    irr: 28.3,
    npv: 650000,
    paybackPeriod: 2.3,
    grossMargin: 0.7,
    commercialMargin: 0.5,
    createdBy: '<EMAIL>',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

const sampleProjects = [
  {
    id: 'proj_sample_1',
    name: 'Customer Portal Enhancement',
    type: 'project',
    businessUnit: 'Technology',
    status: 'active',
    owner: '<EMAIL>',
    linkedBusinessCases: ['bc_sample_1'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'epic_sample_1',
    name: 'Digital Transformation Epic',
    type: 'epic',
    businessUnit: 'Technology',
    status: 'active',
    owner: '<EMAIL>',
    linkedBusinessCases: ['bc_sample_2'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

const sampleIdeas = [
  {
    id: 'idea_sample_1',
    title: 'AI-Powered Customer Support',
    description: 'Implement AI chatbots for 24/7 customer support',
    businessUnit: 'Customer Service',
    status: 'submitted',
    priority: 'high',
    submittedBy: '<EMAIL>',
    submissionDate: new Date().toISOString(),
    businessValue: 'high',
    implementationComplexity: 'medium',
    estimatedCost: 150000,
    expectedBenefit: 'Reduce support costs by 30%'
  },
  {
    id: 'idea_sample_2',
    title: 'Green Energy Initiative',
    description: 'Transition to renewable energy sources',
    businessUnit: 'Operations',
    status: 'approved',
    priority: 'medium',
    submittedBy: '<EMAIL>',
    submissionDate: new Date().toISOString(),
    businessValue: 'high',
    implementationComplexity: 'high',
    estimatedCost: 500000,
    expectedBenefit: 'Reduce energy costs by 40%'
  }
];

const sampleDashboardData = {
  kpis: {
    totalPortfolioValue: 12500000,
    activeProjects: 15,
    totalNPV: 8500000,
    avgIRR: 26.5,
    completedProjects: 8,
    pendingApprovals: 3
  },
  charts: {
    portfolioPerformance: [],
    businessUnits: [],
    roiTrends: [],
    projectStatusDistribution: [],
    financialMetricsComparison: []
  },
  recentActivity: [
    {
      id: 1,
      type: 'business_case_created',
      title: 'New Business Case Created',
      description: 'Digital Customer Experience Platform business case was created',
      user: 'Demo User',
      timestamp: new Date().toISOString()
    },
    {
      id: 2,
      type: 'project_updated',
      title: 'Project Updated',
      description: 'Customer Portal Enhancement project milestones were updated',
      user: 'Demo User',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
    }
  ],
  insights: [
    'Portfolio performance is trending upward with 15% NPV growth',
    'Digital transformation initiatives show highest ROI potential',
    'Consider consolidating similar business cases for better resource allocation'
  ]
};

const DataContext = createContext();

export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export const DataProvider = ({ children }) => {
  const { isAuthenticated, authToken } = useAuth();
  
  // Business Cases
  const [businessCases, setBusinessCases] = useState([]);
  const [masterBusinessCases, setMasterBusinessCases] = useState([]);
  
  // Projects & Programs
  const [projects, setProjects] = useState([]);
  const [epics, setEpics] = useState([]);
  const [programs, setPrograms] = useState([]);
  
  // Ideas
  const [ideas, setIdeas] = useState([]);
  
  // Dashboard Data
  const [dashboardData, setDashboardData] = useState({
    kpis: {},
    charts: {},
    recentActivity: [],
    insights: []
  });
  
  // Loading states
  const [loading, setLoading] = useState({
    businessCases: false,
    masterBusinessCases: false,
    projects: false,
    programs: false,
    ideas: false,
    dashboard: false
  });

  // Error states
  const [errors, setErrors] = useState({});

  // Business Cases
  const loadBusinessCases = useCallback(async () => {
    setLoading(prev => ({ ...prev, businessCases: true }));
    try {
      console.log('🔍 Loading Business Cases from API...');
      const response = await businessCaseService.getAll();
      console.log('📊 Business Cases API response:', response);

      if (response.success) {
        // Extract the businessCases array from the nested data structure
        const businessCases = response.data.businessCases || response.data || [];
        console.log('✅ Loaded Business Cases:', businessCases.length, 'items');
        setBusinessCases(businessCases);
      } else {
        console.log('❌ API failed, using sample data:', response.error);
        setBusinessCases(sampleBusinessCases);
        setErrors(prev => ({ ...prev, businessCases: response.error }));
      }
    } catch (error) {
      console.log('❌ API error, using sample data:', error.message);
      setBusinessCases(sampleBusinessCases);
      setErrors(prev => ({ ...prev, businessCases: error.message }));
    } finally {
      setLoading(prev => ({ ...prev, businessCases: false }));
    }
  }, []);

  const createBusinessCase = async (businessCaseData) => {
    try {
      const response = await businessCaseService.create(businessCaseData);
      if (response.success) {
        setBusinessCases(prev => [...prev, response.data]);
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const updateBusinessCase = async (id, businessCaseData) => {
    try {
      const response = await businessCaseService.update(id, businessCaseData);
      if (response.success) {
        setBusinessCases(prev => 
          prev.map(bc => bc.id === id ? response.data : bc)
        );
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const deleteBusinessCase = async (id) => {
    try {
      const response = await businessCaseService.delete(id);
      if (response.success) {
        setBusinessCases(prev => prev.filter(bc => bc.id !== id));
        return { success: true };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // Master Business Cases
  const loadMasterBusinessCases = useCallback(async () => {
    setLoading(prev => ({ ...prev, masterBusinessCases: true }));
    try {
      console.log('🔍 Loading Master Business Cases from API...');
      const response = await businessCaseService.getMasterBusinessCases();
      console.log('📊 Master Business Cases API response:', response);

      if (response.success) {
        // Extract the masterBusinessCases array from the nested data structure
        const masterBCs = response.data.masterBusinessCases || [];
        console.log('✅ Loaded Master Business Cases:', masterBCs.length, 'items');
        setMasterBusinessCases(masterBCs);
      } else {
        console.log('❌ API failed, using sample data:', response.error);
        setMasterBusinessCases(sampleMasterBCs);
        setErrors(prev => ({ ...prev, masterBusinessCases: response.error }));
      }
    } catch (error) {
      console.log('❌ API error, using sample data:', error.message);
      setMasterBusinessCases(sampleMasterBCs);
      setErrors(prev => ({ ...prev, masterBusinessCases: error.message }));
    } finally {
      setLoading(prev => ({ ...prev, masterBusinessCases: false }));
    }
  }, []);

  const createMasterBusinessCase = async (masterBCData) => {
    try {
      const response = await businessCaseService.createMasterBusinessCase(masterBCData);
      if (response.success) {
        setMasterBusinessCases(prev => [...prev, response.data]);
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const updateMasterBusinessCase = async (id, masterBCData) => {
    try {
      const response = await businessCaseService.updateMasterBusinessCase(id, masterBCData);
      if (response.success) {
        setMasterBusinessCases(prev =>
          prev.map(mbc => mbc.id === id ? response.data : mbc)
        );
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const linkBusinessCasesToMasterBC = async (masterBCId, businessCaseIds) => {
    try {
      const response = await businessCaseService.linkBusinessCases(masterBCId, businessCaseIds);
      if (response.success) {
        // Refresh master business cases to get updated aggregated metrics
        await loadMasterBusinessCases();
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const linkProgramToMasterBC = async (programId, masterBCId) => {
    try {
      const response = await projectService.linkProgramToMasterBC(programId, masterBCId);
      if (response.success) {
        // Refresh programs and master business cases
        await Promise.all([loadPrograms(), loadMasterBusinessCases()]);
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // Projects
  const loadProjects = useCallback(async () => {
    setLoading(prev => ({ ...prev, projects: true }));
    try {
      const response = await projectService.getAll();
      if (response.success) {
        const allProjects = response.data.projects || [];
        setProjects(allProjects.filter(p => p.type === 'project'));
        setEpics(allProjects.filter(p => p.type === 'epic'));
      } else {
        console.log('❌ Projects API failed, using sample data:', response.error);
        setProjects(sampleProjects.filter(p => p.type === 'project'));
        setEpics(sampleProjects.filter(p => p.type === 'epic'));
        setErrors(prev => ({ ...prev, projects: response.error }));
      }
    } catch (error) {
      console.log('❌ Projects API error, using sample data:', error.message);
      setProjects(sampleProjects.filter(p => p.type === 'project'));
      setEpics(sampleProjects.filter(p => p.type === 'epic'));
      setErrors(prev => ({ ...prev, projects: error.message }));
    } finally {
      setLoading(prev => ({ ...prev, projects: false }));
    }
  }, []);

  // Programs
  const loadPrograms = useCallback(async () => {
    setLoading(prev => ({ ...prev, programs: true }));
    try {
      console.log('🔍 Loading Programs from API...');
      const response = await projectService.getPrograms();
      console.log('📊 Programs API response:', response);

      if (response.success) {
        // Extract the programs array from the nested data structure
        const programs = response.data.programs || response.data || [];
        console.log('✅ Loaded Programs:', programs.length, 'items');
        setPrograms(programs);
      } else {
        console.log('❌ API failed, using sample data:', response.error);
        setPrograms(samplePrograms);
        setErrors(prev => ({ ...prev, programs: response.error }));
      }
    } catch (error) {
      console.log('❌ API error, using sample data:', error.message);
      setPrograms(samplePrograms);
      setErrors(prev => ({ ...prev, programs: error.message }));
    } finally {
      setLoading(prev => ({ ...prev, programs: false }));
    }
  }, []);

  const createProgram = async (programData) => {
    try {
      const response = await projectService.createProgram(programData);
      if (response.success) {
        setPrograms(prev => [...prev, response.data]);
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const updateProgram = async (id, programData) => {
    try {
      const response = await projectService.updateProgram(id, programData);
      if (response.success) {
        setPrograms(prev =>
          prev.map(program => program.id === id ? response.data : program)
        );
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const deleteProgram = async (id) => {
    try {
      const response = await projectService.deleteProgram(id);
      if (response.success) {
        setPrograms(prev => prev.filter(program => program.id !== id));
        return { success: true };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // Ideas
  const loadIdeas = useCallback(async () => {
    setLoading(prev => ({ ...prev, ideas: true }));
    try {
      const response = await ideaService.getAll();
      if (response.success) {
        // Extract the ideas array from the nested data structure
        setIdeas(response.data.ideas || []);
      } else {
        console.log('❌ Ideas API failed, using sample data:', response.error);
        setIdeas(sampleIdeas);
        setErrors(prev => ({ ...prev, ideas: response.error }));
      }
    } catch (error) {
      console.log('❌ Ideas API error, using sample data:', error.message);
      setIdeas(sampleIdeas);
      setErrors(prev => ({ ...prev, ideas: error.message }));
    } finally {
      setLoading(prev => ({ ...prev, ideas: false }));
    }
  }, []);

  const createIdea = async (ideaData) => {
    console.log('🔄 DataContext.createIdea called with:', ideaData);
    try {
      console.log('📡 Calling ideaService.create...');
      const response = await ideaService.create(ideaData);
      console.log('📡 ideaService.create response:', response);

      if (response.success) {
        console.log('✅ Adding idea to state:', response.data);
        setIdeas(prev => {
          const newIdeas = [...prev, response.data];
          console.log('📊 Updated ideas state:', newIdeas);
          return newIdeas;
        });
        return { success: true, data: response.data };
      } else {
        console.log('❌ ideaService.create failed:', response.error);
        return { success: false, error: response.error };
      }
    } catch (error) {
      console.log('💥 createIdea error:', error);
      return { success: false, error: error.message };
    }
  };

  const updateIdea = async (id, ideaData) => {
    try {
      const response = await ideaService.update(id, ideaData);
      if (response.success) {
        setIdeas(prev =>
          prev.map(idea => idea.id === id ? response.data : idea)
        );
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const deleteIdea = async (id) => {
    try {
      const response = await ideaService.delete(id);
      if (response.success) {
        setIdeas(prev => prev.filter(idea => idea.id !== id));
        return { success: true };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // Dashboard Data
  const loadDashboardData = useCallback(async () => {
    setLoading(prev => ({ ...prev, dashboard: true }));
    try {
      const response = await dashboardService.getDashboardData();
      if (response.success) {
        setDashboardData(response.data);
      } else {
        console.log('❌ Dashboard API failed, using sample data:', response.error);
        setDashboardData(sampleDashboardData);
        setErrors(prev => ({ ...prev, dashboard: response.error }));
      }
    } catch (error) {
      console.log('❌ Dashboard API error, using sample data:', error.message);
      setDashboardData(sampleDashboardData);
      setErrors(prev => ({ ...prev, dashboard: error.message }));
    } finally {
      setLoading(prev => ({ ...prev, dashboard: false }));
    }
  }, []);

  // Load all initial data
  const loadInitialData = useCallback(async () => {
    try {
      await Promise.all([
        loadBusinessCases(),
        loadMasterBusinessCases(),
        loadProjects(),
        loadPrograms(),
        loadIdeas(),
        loadDashboardData()
      ]);
    } catch (error) {
      console.error('Failed to load initial data:', error);
    }
  }, [loadBusinessCases, loadMasterBusinessCases, loadProjects, loadPrograms, loadIdeas, loadDashboardData]);

  // Load data when authenticated OR when not authenticated (for demo purposes)
  useEffect(() => {
    // Always load data - if authenticated, try API first, if not authenticated or API fails, use sample data
    loadInitialData();
  }, [loadInitialData]);

  const value = {
    // Data
    businessCases,
    masterBusinessCases,
    projects,
    epics,
    programs,
    ideas,
    dashboardData,
    
    // Loading states
    loading,
    errors,
    
    // Actions
    loadBusinessCases,
    createBusinessCase,
    updateBusinessCase,
    deleteBusinessCase,
    loadMasterBusinessCases,
    createMasterBusinessCase,
    updateMasterBusinessCase,
    linkBusinessCasesToMasterBC,
    linkProgramToMasterBC,
    loadProjects,
    loadPrograms,
    createProgram,
    updateProgram,
    deleteProgram,
    loadIdeas,
    createIdea,
    updateIdea,
    deleteIdea,
    loadDashboardData,

    // Convenience functions for components
    fetchMasterBusinessCases: loadMasterBusinessCases,
    fetchBusinessCases: loadBusinessCases,
    fetchPrograms: loadPrograms,
    fetchProjects: loadProjects,
    fetchIdeas: loadIdeas,

    // Refresh all data
    refreshData: loadInitialData
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};
