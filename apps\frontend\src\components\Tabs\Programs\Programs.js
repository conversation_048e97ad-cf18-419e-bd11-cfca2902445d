import React, { useState, useEffect } from 'react';
import { useData } from '../../../context/DataContext';
import ProgramsList from './ProgramsList/ProgramsList';
import ProgramForm from './ProgramForm/ProgramForm';
import MasterBCLinkModal from './MasterBCLinkModal/MasterBCLinkModal';
import './Programs.css';

const Programs = () => {
  const {
    programs,
    masterBusinessCases,
    loading,
    errors,
    fetchPrograms,
    fetchMasterBusinessCases,
    createProgram,
    updateProgram,
    linkProgramToMasterBC
  } = useData();

  const [activeView, setActiveView] = useState('list');
  const [selectedProgram, setSelectedProgram] = useState(null);
  const [showLinkModal, setShowLinkModal] = useState(false);

  useEffect(() => {
    // Load data on component mount
    fetchPrograms();
    fetchMasterBusinessCases();
  }, [fetchPrograms, fetchMasterBusinessCases]);

  // Handle creating new program
  const handleCreateNew = () => {
    setSelectedProgram(null);
    setActiveView('create');
  };

  // Handle editing program
  const handleEdit = (program) => {
    setSelectedProgram(program);
    setActiveView('edit');
  };

  // Handle viewing program details
  const handleViewDetail = (program) => {
    setSelectedProgram(program);
    setActiveView('detail');
  };

  // Handle linking program to Master BC
  const handleLinkMasterBC = (program) => {
    setSelectedProgram(program);
    setShowLinkModal(true);
  };

  // Handle unlinking program from Master BC
  const handleUnlinkMasterBC = async (program) => {
    try {
      await linkProgramToMasterBC(program.id, null);
      // Refresh data
      await fetchPrograms();
    } catch (error) {
      console.error('Error unlinking Master BC:', error);
    }
  };

  // Handle saving program (create or update)
  const handleSave = async (programData) => {
    try {
      if (activeView === 'create') {
        await createProgram(programData);
      } else if (activeView === 'edit') {
        await updateProgram(selectedProgram.id, programData);
      }

      // Refresh data and return to list
      await fetchPrograms();
      handleBackToList();
    } catch (error) {
      console.error('Error saving program:', error);
      throw error;
    }
  };

  // Handle Master BC linking
  const handleConfirmLink = async (programId, masterBCId) => {
    try {
      await linkProgramToMasterBC(programId, masterBCId);
      // Refresh data
      await Promise.all([fetchPrograms(), fetchMasterBusinessCases()]);
    } catch (error) {
      console.error('Error linking Master BC:', error);
      throw error;
    }
  };

  // Handle back to list
  const handleBackToList = () => {
    setSelectedProgram(null);
    setActiveView('list');
  };

  // Handle closing link modal
  const handleCloseLinkModal = () => {
    setShowLinkModal(false);
    setSelectedProgram(null);
  };

  // Loading state
  if (loading.programs) {
    return (
      <div className="programs">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading Programs...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (errors.programs) {
    return (
      <div className="programs">
        <div className="error-container">
          <h3>Error Loading Programs</h3>
          <p>{errors.programs}</p>
          <button onClick={() => window.location.reload()}>Retry</button>
        </div>
      </div>
    );
  }

  return (
    <div className="programs">
      <div className="programs-header">
        <div>
          <h2>Programs</h2>
          <p>Program management with Master Business Case integration</p>
        </div>
      </div>

      {/* Main Content */}
      {activeView === 'list' && (
        <ProgramsList
          programs={programs}
          masterBusinessCases={masterBusinessCases}
          onCreateNew={handleCreateNew}
          onEdit={handleEdit}
          onViewDetail={handleViewDetail}
          onLinkMasterBC={handleLinkMasterBC}
          onUnlinkMasterBC={handleUnlinkMasterBC}
        />
      )}

      {(activeView === 'create' || activeView === 'edit') && (
        <ProgramForm
          program={selectedProgram}
          isEdit={activeView === 'edit'}
          masterBusinessCases={masterBusinessCases}
          onSave={handleSave}
          onCancel={handleBackToList}
        />
      )}

      {activeView === 'detail' && (
        <div className="card">
          <div className="card-body">
            <h3>Program Detail</h3>
            <p>Detail component will be implemented here.</p>
            <button className="btn btn-secondary" onClick={handleBackToList}>
              Back to List
            </button>
          </div>
        </div>
      )}

      {/* Master BC Link Modal */}
      <MasterBCLinkModal
        isOpen={showLinkModal}
        program={selectedProgram}
        masterBusinessCases={masterBusinessCases}
        onLink={handleConfirmLink}
        onClose={handleCloseLinkModal}
      />
    </div>
  );
};

export default Programs;
