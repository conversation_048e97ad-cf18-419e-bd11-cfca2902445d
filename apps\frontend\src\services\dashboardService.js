import { apiGet } from './api';
import { sampleRelationshipData } from '../data/sampleRelationshipData';

class DashboardService {
  // Get complete dashboard data
  async getDashboardData() {
    return await apiGet('/api/dashboard');
  }

  // Get KPI data
  async getKPIs() {
    return await apiGet('/api/dashboard/kpis');
  }

  // Get chart data
  async getChartData() {
    return await apiGet('/api/dashboard/charts');
  }

  // Get recent activity
  async getRecentActivity() {
    return await apiGet('/api/dashboard/activity');
  }

  // Get insights
  async getInsights() {
    return await apiGet('/api/dashboard/insights');
  }

  // Get business unit summary
  async getBusinessUnitSummary() {
    return await apiGet('/api/dashboard/business-units');
  }

  // Get financial summary
  async getFinancialSummary() {
    return await apiGet('/api/dashboard/financial-summary');
  }

  // Get comprehensive relationship data
  async getRelationshipData() {
    try {
      console.log('🔍 Loading relationship data from API...');
      const response = await apiGet('/api/relationships');

      if (response.success && response.data) {
        console.log('✅ Relationship data loaded from API:', response.data);
        return response;
      } else {
        console.log('❌ API failed, using sample relationship data:', response.error);
        return {
          success: true,
          data: sampleRelationshipData
        };
      }
    } catch (error) {
      console.log('❌ API error, using sample relationship data:', error.message);
      return {
        success: true,
        data: sampleRelationshipData
      };
    }
  }

  // Get project portfolio status
  async getPortfolioStatus() {
    return await apiGet('/api/dashboard/portfolio-status');
  }
}

export const dashboardService = new DashboardService();
export default dashboardService;
