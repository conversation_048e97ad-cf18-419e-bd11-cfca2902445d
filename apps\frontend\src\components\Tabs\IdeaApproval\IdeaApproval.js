import React, { useState, useEffect } from 'react';
import { useData } from '../../../context/DataContext';
import { useAuth } from '../../../context/AuthContext';
import IdeaApprovalList from './IdeaApprovalList';
import IdeaApprovalStats from './IdeaApprovalStats';

const IdeaApproval = () => {
  const { ideas, loading, loadIdeas, updateIdea } = useData();
  const { user } = useAuth();
  const [activeView, setActiveView] = useState('pending'); // 'pending', 'all', 'stats'
  const [filters, setFilters] = useState({
    status: 'submitted',
    priority: '',
    businessUnit: '',
    search: ''
  });
  const [notification, setNotification] = useState(null);

  useEffect(() => {
    loadIdeas();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Filter ideas based on current view and filters
  const getFilteredIdeas = () => {
    if (!ideas) return [];

    return ideas.filter(idea => {
      const matchesSearch = !filters.search || 
        idea.title.toLowerCase().includes(filters.search.toLowerCase()) ||
        idea.description?.toLowerCase().includes(filters.search.toLowerCase());
      
      const matchesPriority = !filters.priority || idea.priority === filters.priority;
      const matchesBusinessUnit = !filters.businessUnit || idea.businessUnitId === filters.businessUnit;
      
      // Status filter based on active view
      let matchesStatus = true;
      if (activeView === 'pending') {
        matchesStatus = idea.status === 'submitted' || idea.status === 'under-review';
      } else if (filters.status) {
        matchesStatus = idea.status === filters.status;
      }

      return matchesSearch && matchesPriority && matchesBusinessUnit && matchesStatus;
    });
  };

  const handleApproveIdea = async (ideaId, approvalData) => {
    try {
      const result = await updateIdea(ideaId, {
        status: 'approved',
        approvedBy: user?.email || '<EMAIL>',
        approvedAt: new Date().toISOString(),
        approvalComments: approvalData.comments || ''
      });

      if (result.success) {
        setNotification({
          type: 'success',
          message: 'Idea approved successfully!'
        });
        setTimeout(() => setNotification(null), 3000);
      } else {
        setNotification({
          type: 'error',
          message: result.error || 'Failed to approve idea'
        });
        setTimeout(() => setNotification(null), 3000);
      }
    } catch (error) {
      setNotification({
        type: 'error',
        message: 'Error approving idea: ' + error.message
      });
      setTimeout(() => setNotification(null), 3000);
    }
  };

  const handleRejectIdea = async (ideaId, rejectionData) => {
    try {
      const result = await updateIdea(ideaId, {
        status: 'rejected',
        rejectedBy: user?.email || '<EMAIL>',
        rejectedAt: new Date().toISOString(),
        rejectionComments: rejectionData.comments || '',
        rejectionReason: rejectionData.reason || ''
      });

      if (result.success) {
        setNotification({
          type: 'success',
          message: 'Idea rejected with feedback provided.'
        });
        setTimeout(() => setNotification(null), 3000);
      } else {
        setNotification({
          type: 'error',
          message: result.error || 'Failed to reject idea'
        });
        setTimeout(() => setNotification(null), 3000);
      }
    } catch (error) {
      setNotification({
        type: 'error',
        message: 'Error rejecting idea: ' + error.message
      });
      setTimeout(() => setNotification(null), 3000);
    }
  };

  const handleRequestMoreInfo = async (ideaId, requestData) => {
    try {
      const result = await updateIdea(ideaId, {
        status: 'under-review',
        reviewedBy: user?.email || '<EMAIL>',
        reviewedAt: new Date().toISOString(),
        reviewComments: requestData.comments || '',
        additionalInfoRequested: true
      });

      if (result.success) {
        setNotification({
          type: 'info',
          message: 'Additional information requested from submitter.'
        });
        setTimeout(() => setNotification(null), 3000);
      } else {
        setNotification({
          type: 'error',
          message: result.error || 'Failed to request additional information'
        });
        setTimeout(() => setNotification(null), 3000);
      }
    } catch (error) {
      setNotification({
        type: 'error',
        message: 'Error requesting additional information: ' + error.message
      });
      setTimeout(() => setNotification(null), 3000);
    }
  };

  const filteredIdeas = getFilteredIdeas();
  const pendingCount = ideas?.filter(idea => idea.status === 'submitted').length || 0;
  const underReviewCount = ideas?.filter(idea => idea.status === 'under-review').length || 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <i className="fas fa-check-circle text-green-600 mr-3"></i>
              Idea Approval Center
            </h1>
            <p className="text-gray-600 mt-1">
              Review and approve submitted ideas for business case development
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{pendingCount}</div>
              <div className="text-sm text-gray-600">Pending</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{underReviewCount}</div>
              <div className="text-sm text-gray-600">Under Review</div>
            </div>
          </div>
        </div>
      </div>

      {/* Notification */}
      {notification && (
        <div className={`rounded-lg p-4 ${
          notification.type === 'success' ? 'bg-green-50 border border-green-200' :
          notification.type === 'error' ? 'bg-red-50 border border-red-200' :
          'bg-blue-50 border border-blue-200'
        }`}>
          <div className="flex items-center">
            <i className={`fas ${
              notification.type === 'success' ? 'fa-check-circle text-green-600' :
              notification.type === 'error' ? 'fa-exclamation-circle text-red-600' :
              'fa-info-circle text-blue-600'
            } mr-2`}></i>
            <span className={`${
              notification.type === 'success' ? 'text-green-800' :
              notification.type === 'error' ? 'text-red-800' :
              'text-blue-800'
            }`}>
              {notification.message}
            </span>
          </div>
        </div>
      )}

      {/* View Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'pending', name: 'Pending Approval', icon: 'fas fa-clock', count: pendingCount },
              { id: 'all', name: 'All Ideas', icon: 'fas fa-list', count: ideas?.length || 0 },
              { id: 'stats', name: 'Statistics', icon: 'fas fa-chart-bar', count: null }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveView(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeView === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <i className={`${tab.icon} mr-2`}></i>
                {tab.name}
                {tab.count !== null && (
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                    activeView === tab.id ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6">
          {loading.ideas ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="loading-spinner mx-auto mb-4"></div>
                <p className="text-gray-600">Loading ideas...</p>
              </div>
            </div>
          ) : activeView === 'stats' ? (
            <IdeaApprovalStats ideas={ideas || []} />
          ) : (
            <IdeaApprovalList
              ideas={filteredIdeas}
              filters={filters}
              onFiltersChange={setFilters}
              onApprove={handleApproveIdea}
              onReject={handleRejectIdea}
              onRequestMoreInfo={handleRequestMoreInfo}
              showPendingOnly={activeView === 'pending'}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default IdeaApproval;
