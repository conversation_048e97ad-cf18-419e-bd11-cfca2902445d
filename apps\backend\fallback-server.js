const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// Sample data for fallback
const sampleData = {
  businessCases: [
    {
      id: 'bc_1',
      name: 'Digital Customer Experience Platform',
      description: 'Comprehensive digital platform for enhanced customer experience',
      businessUnit: 'Customer Service',
      status: 'approved',
      totalCapex: 500000,
      totalOpex: 300000,
      totalRevenue: 1200000,
      irr: 25.5,
      npv: 450000,
      paybackPeriod: 2.1,
      grossMargin: 0.65,
      commercialMargin: 0.45,
      createdBy: '<EMAIL>',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'bc_2',
      name: 'Supply Chain Optimization System',
      description: 'AI-powered supply chain optimization and tracking',
      businessUnit: 'Operations',
      status: 'approved',
      totalCapex: 750000,
      totalOpex: 450000,
      totalRevenue: 1800000,
      irr: 28.3,
      npv: 650000,
      paybackPeriod: 2.3,
      grossMargin: 0.7,
      commercialMargin: 0.5,
      createdBy: '<EMAIL>',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ],
  masterBusinessCases: [
    {
      id: 'mbc_1',
      name: 'Digital Transformation Initiative',
      description: 'Comprehensive digital transformation across all business units',
      businessUnit: 'Technology',
      linkedBusinessCases: ['bc_1', 'bc_2'],
      aggregatedMetrics: {
        totalCapex: 1250000,
        totalOpex: 750000,
        totalRevenue: 3000000,
        totalNPV: 1100000,
        avgIRR: 26.9,
        avgPaybackPeriod: 2.2,
        avgGrossMargin: 0.675,
        avgCommercialMargin: 0.475
      },
      status: 'active',
      createdBy: '<EMAIL>',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ],
  projects: [
    {
      id: 'proj_1',
      name: 'Customer Portal Enhancement',
      type: 'project',
      businessUnit: 'Technology',
      status: 'active',
      owner: '<EMAIL>',
      linkedBusinessCases: ['bc_1'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'epic_1',
      name: 'Digital Transformation Epic',
      type: 'epic',
      businessUnit: 'Technology',
      status: 'active',
      owner: '<EMAIL>',
      linkedBusinessCases: ['bc_2'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ],
  programs: [
    {
      id: 'prog_1',
      name: 'Digital Excellence Program',
      description: 'Comprehensive program for digital transformation',
      businessUnit: 'Technology',
      status: 'active',
      owner: '<EMAIL>',
      linkedMasterBusinessCase: 'mbc_1',
      linkedProjects: ['proj_1'],
      linkedEpics: ['epic_1'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ],
  ideas: [
    {
      id: 'idea_1',
      title: 'AI-Powered Customer Support',
      description: 'Implement AI chatbots for 24/7 customer support',
      businessUnit: 'Customer Service',
      status: 'submitted',
      priority: 'high',
      submittedBy: '<EMAIL>',
      submissionDate: new Date().toISOString(),
      businessValue: 'high',
      implementationComplexity: 'medium',
      estimatedCost: 150000,
      expectedBenefit: 'Reduce support costs by 30%'
    },
    {
      id: 'idea_2',
      title: 'Green Energy Initiative',
      description: 'Transition to renewable energy sources',
      businessUnit: 'Operations',
      status: 'approved',
      priority: 'medium',
      submittedBy: '<EMAIL>',
      submissionDate: new Date().toISOString(),
      businessValue: 'high',
      implementationComplexity: 'high',
      estimatedCost: 500000,
      expectedBenefit: 'Reduce energy costs by 40%'
    }
  ]
};

// Routes
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Fallback server is running' });
});

app.get('/api/dashboard', (req, res) => {
  res.json({
    success: true,
    data: {
      kpis: {
        totalPortfolioValue: 12500000,
        activeProjects: 15,
        totalNPV: 8500000,
        avgIRR: 26.5,
        completedProjects: 8,
        pendingApprovals: 3
      },
      charts: {
        portfolioPerformance: [],
        businessUnits: [],
        roiTrends: [],
        projectStatusDistribution: [],
        financialMetricsComparison: []
      },
      recentActivity: [
        {
          id: 1,
          type: 'business_case_created',
          title: 'New Business Case Created',
          description: 'Digital Customer Experience Platform business case was created',
          user: 'Demo User',
          timestamp: new Date().toISOString()
        }
      ],
      insights: [
        'Portfolio performance is trending upward with 15% NPV growth',
        'Digital transformation initiatives show highest ROI potential'
      ]
    }
  });
});

app.get('/api/business-cases', (req, res) => {
  res.json({
    success: true,
    data: sampleData.businessCases
  });
});

app.get('/api/master-bc', (req, res) => {
  res.json({
    success: true,
    data: sampleData.masterBusinessCases
  });
});

app.get('/api/projects', (req, res) => {
  res.json({
    success: true,
    data: {
      projects: sampleData.projects.filter(p => p.type === 'project'),
      epics: sampleData.projects.filter(p => p.type === 'epic')
    }
  });
});

app.get('/api/programs', (req, res) => {
  res.json({
    success: true,
    data: sampleData.programs
  });
});

app.get('/api/ideas', (req, res) => {
  res.json({
    success: true,
    data: sampleData.ideas
  });
});

// POST routes for creating data
app.post('/api/business-cases', (req, res) => {
  const newBC = {
    id: `bc_${Date.now()}`,
    ...req.body,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  sampleData.businessCases.push(newBC);
  res.json({ success: true, data: newBC });
});

app.post('/api/master-bc', (req, res) => {
  const newMBC = {
    id: `mbc_${Date.now()}`,
    ...req.body,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  sampleData.masterBusinessCases.push(newMBC);
  res.json({ success: true, data: newMBC });
});

app.post('/api/projects', (req, res) => {
  const newProject = {
    id: `proj_${Date.now()}`,
    ...req.body,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  sampleData.projects.push(newProject);
  res.json({ success: true, data: newProject });
});

app.post('/api/programs', (req, res) => {
  const newProgram = {
    id: `prog_${Date.now()}`,
    ...req.body,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  sampleData.programs.push(newProgram);
  res.json({ success: true, data: newProgram });
});

app.post('/api/ideas', (req, res) => {
  const newIdea = {
    id: `idea_${Date.now()}`,
    ...req.body,
    submissionDate: new Date().toISOString()
  };
  sampleData.ideas.push(newIdea);
  res.json({ success: true, data: newIdea });
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    error: 'Something went wrong!'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found'
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Fallback server running on port ${PORT}`);
  console.log(`📊 Serving sample data for development`);
  console.log(`🌐 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
