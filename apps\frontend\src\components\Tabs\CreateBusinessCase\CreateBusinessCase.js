import React, { useState, useEffect } from 'react';
import { useData } from '../../../context/DataContext';
import BusinessCaseEmptyState from './BusinessCaseEmptyState';
import BusinessCaseForm from './BusinessCaseForm';
import BusinessCaseList from './BusinessCaseList';
import BusinessCaseStats from './BusinessCaseStats';
import IdeaPromotionModal from './IdeaPromotionModal';
import businessCaseService from '../../../services/businessCaseService';
import ideaService from '../../../services/ideaService';

const CreateBusinessCase = () => {
  const { businessCases, ideas, loadBusinessCases, loadIdeas } = useData();
  const [currentView, setCurrentView] = useState('list'); // 'list', 'create', 'edit', 'stats'
  const [editingBusinessCase, setEditingBusinessCase] = useState(null);
  const [promotedIdea, setPromotedIdea] = useState(null);
  const [showPromotionModal, setShowPromotionModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [notification, setNotification] = useState(null);

  useEffect(() => {
    // Load data when component mounts
    if (!businessCases || businessCases.length === 0) {
      loadBusinessCases();
    }
    if (!ideas || ideas.length === 0) {
      loadIdeas();
    }
  }, [businessCases, ideas, loadBusinessCases, loadIdeas]);

  const showNotification = (message, type = 'success') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  };

  const handleCreateNew = () => {
    setEditingBusinessCase(null);
    setPromotedIdea(null);
    setCurrentView('create');
  };

  const handlePromoteIdea = () => {
    setShowPromotionModal(true);
  };

  const handleIdeaSelected = (idea) => {
    setPromotedIdea(idea);
    setEditingBusinessCase(null);
    setCurrentView('create');
    setShowPromotionModal(false);
  };

  const handleEdit = (businessCase) => {
    setEditingBusinessCase(businessCase);
    setPromotedIdea(null);
    setCurrentView('create');
  };

  const handleSubmit = async (formData) => {
    setIsLoading(true);
    try {
      let response;
      if (editingBusinessCase) {
        response = await businessCaseService.update(editingBusinessCase.id, formData);
      } else {
        response = await businessCaseService.create(formData);
      }

      if (response.success) {
        showNotification(
          editingBusinessCase
            ? 'Business case updated successfully!'
            : 'Business case created successfully!'
        );

        // If promoted from idea, mark the idea as promoted
        if (promotedIdea && !editingBusinessCase) {
          try {
            await ideaService.update(promotedIdea.id, {
              ...promotedIdea,
              promotedToBusinessCase: response.data.id,
              status: 'promoted'
            });
          } catch (error) {
            console.error('Error updating idea promotion status:', error);
          }
        }

        // Reload data and return to list
        await loadBusinessCases();
        setCurrentView('list');
        setEditingBusinessCase(null);
        setPromotedIdea(null);
      } else {
        showNotification(response.error || 'Failed to save business case', 'error');
      }
    } catch (error) {
      console.error('Error saving business case:', error);
      showNotification('An error occurred while saving the business case', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setCurrentView('list');
    setEditingBusinessCase(null);
    setPromotedIdea(null);
  };

  const handleDelete = async (businessCase) => {
    if (window.confirm(`Are you sure you want to delete "${businessCase.name}"?`)) {
      setIsLoading(true);
      try {
        const response = await businessCaseService.delete(businessCase.id);
        if (response.success) {
          showNotification('Business case deleted successfully!');
          await loadBusinessCases();
        } else {
          showNotification(response.error || 'Failed to delete business case', 'error');
        }
      } catch (error) {
        console.error('Error deleting business case:', error);
        showNotification('An error occurred while deleting the business case', 'error');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleView = (businessCase) => {
    // TODO: Implement view modal or navigate to detail page
    console.log('View business case:', businessCase);
  };

  const handleExport = async (businessCase) => {
    try {
      const response = await businessCaseService.exportBusinessCase(businessCase.id);
      if (response.success) {
        showNotification('Business case exported successfully!');
      } else {
        showNotification(response.error || 'Failed to export business case', 'error');
      }
    } catch (error) {
      console.error('Error exporting business case:', error);
      showNotification('An error occurred while exporting the business case', 'error');
    }
  };

  const handleImportExcel = () => {
    // TODO: Implement Excel import functionality
    showNotification('Excel import functionality coming soon!', 'info');
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'create':
        return (
          <BusinessCaseForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            initialData={editingBusinessCase}
            promotedIdea={promotedIdea}
          />
        );
      case 'stats':
        return <BusinessCaseStats businessCases={businessCases || []} />;
      case 'list':
      default:
        if (!businessCases || businessCases.length === 0) {
          return (
            <BusinessCaseEmptyState
              onCreateNew={handleCreateNew}
              onPromoteIdea={handlePromoteIdea}
              onImportExcel={handleImportExcel}
            />
          );
        }
        return (
          <BusinessCaseList
            businessCases={businessCases}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onView={handleView}
            onExport={handleExport}
          />
        );
    }
  };

  return (
    <div className="space-y-6">
      {/* Notification */}
      {notification && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
          notification.type === 'success' ? 'bg-green-500 text-white' :
          notification.type === 'error' ? 'bg-red-500 text-white' :
          'bg-blue-500 text-white'
        }`}>
          <div className="flex items-center">
            <i className={`fas ${
              notification.type === 'success' ? 'fa-check-circle' :
              notification.type === 'error' ? 'fa-exclamation-circle' :
              'fa-info-circle'
            } mr-2`}></i>
            {notification.message}
          </div>
        </div>
      )}

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Business Case Management</h2>
          <p className="text-gray-600">Create, manage, and track comprehensive business cases</p>
        </div>

        {currentView === 'list' && businessCases && businessCases.length > 0 && (
          <div className="flex space-x-3">
            <button
              onClick={() => setCurrentView('stats')}
              className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700"
            >
              <i className="fas fa-chart-bar mr-2"></i>
              Analytics
            </button>
            <button
              onClick={handlePromoteIdea}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
            >
              <i className="fas fa-lightbulb mr-2"></i>
              Promote Idea
            </button>
            <button
              onClick={handleCreateNew}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              <i className="fas fa-plus mr-2"></i>
              Create New
            </button>
          </div>
        )}

        {currentView !== 'list' && (
          <button
            onClick={() => setCurrentView('list')}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
          >
            <i className="fas fa-arrow-left mr-2"></i>
            Back to List
          </button>
        )}
      </div>

      {/* Main Content */}
      {renderCurrentView()}

      {/* Idea Promotion Modal */}
      <IdeaPromotionModal
        isOpen={showPromotionModal}
        onClose={() => setShowPromotionModal(false)}
        onPromote={handleIdeaSelected}
        ideas={ideas || []}
      />

      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 flex items-center">
            <i className="fas fa-spinner fa-spin text-2xl text-blue-600 mr-4"></i>
            <span className="text-lg">Processing...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreateBusinessCase;
