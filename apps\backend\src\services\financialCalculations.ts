import { FinancialResults, CashFlowItem, FinancialModel } from '../../../shared/src/types';

export class FinancialCalculationService {
  /**
   * Calculate Net Present Value (NPV)
   */
  static calculateNPV(cashFlows: CashFlowItem[], discountRate: number): number {
    return cashFlows.reduce((npv, cashFlow, index) => {
      const discountFactor = Math.pow(1 + discountRate, index);
      return npv + (cashFlow.netCashFlow / discountFactor);
    }, 0);
  }

  /**
   * Calculate Internal Rate of Return (IRR) using Newton-Raphson method
   */
  static calculateIRR(cashFlows: CashFlowItem[], initialGuess: number = 0.1): number {
    const maxIterations = 100;
    const tolerance = 1e-6;
    let rate = initialGuess;

    for (let i = 0; i < maxIterations; i++) {
      const npv = this.calculateNPVForRate(cashFlows, rate);
      const derivative = this.calculateNPVDerivative(cashFlows, rate);
      
      if (Math.abs(derivative) < tolerance) {
        break;
      }
      
      const newRate = rate - (npv / derivative);
      
      if (Math.abs(newRate - rate) < tolerance) {
        return newRate;
      }
      
      rate = newRate;
    }
    
    return rate;
  }

  /**
   * Calculate NPV for a specific rate (helper for IRR calculation)
   */
  private static calculateNPVForRate(cashFlows: CashFlowItem[], rate: number): number {
    return cashFlows.reduce((npv, cashFlow, index) => {
      const discountFactor = Math.pow(1 + rate, index);
      return npv + (cashFlow.netCashFlow / discountFactor);
    }, 0);
  }

  /**
   * Calculate derivative of NPV (helper for IRR calculation)
   */
  private static calculateNPVDerivative(cashFlows: CashFlowItem[], rate: number): number {
    return cashFlows.reduce((derivative, cashFlow, index) => {
      if (index === 0) return derivative;
      const discountFactor = Math.pow(1 + rate, index + 1);
      return derivative - (index * cashFlow.netCashFlow / discountFactor);
    }, 0);
  }

  /**
   * Calculate Payback Period
   */
  static calculatePaybackPeriod(cashFlows: CashFlowItem[]): number {
    let cumulativeCashFlow = 0;
    
    for (let i = 0; i < cashFlows.length; i++) {
      cumulativeCashFlow += cashFlows[i]!.netCashFlow;
      
      if (cumulativeCashFlow >= 0) {
        if (i === 0) return 0;
        
        // Linear interpolation for more precise payback period
        const previousCumulative = cumulativeCashFlow - cashFlows[i]!.netCashFlow;
        const fraction = Math.abs(previousCumulative) / cashFlows[i]!.netCashFlow;
        return i - 1 + fraction;
      }
    }
    
    return -1; // Payback period not achieved within the time frame
  }

  /**
   * Calculate Yield Index (Profitability Index)
   */
  static calculateYieldIndex(cashFlows: CashFlowItem[], discountRate: number): number {
    const initialInvestment = Math.abs(cashFlows[0]?.netCashFlow || 0);
    if (initialInvestment === 0) return 0;
    
    const presentValueOfFutureCashFlows = cashFlows.slice(1).reduce((pv, cashFlow, index) => {
      const discountFactor = Math.pow(1 + discountRate, index + 1);
      return pv + (cashFlow.netCashFlow / discountFactor);
    }, 0);
    
    return presentValueOfFutureCashFlows / initialInvestment;
  }

  /**
   * Calculate Gross Margin
   */
  static calculateGrossMargin(revenue: number, costOfGoodsSold: number): number {
    if (revenue === 0) return 0;
    return ((revenue - costOfGoodsSold) / revenue) * 100;
  }

  /**
   * Calculate Sales at Mature Year
   */
  static calculateSalesAtMaturity(cashFlows: CashFlowItem[]): number {
    // Assume mature year is the last year with positive growth
    const revenues = cashFlows.map(cf => cf.revenue);
    return Math.max(...revenues);
  }

  /**
   * Calculate Break Even Sales
   */
  static calculateBreakEvenSales(fixedCosts: number, variableCostPerUnit: number, pricePerUnit: number): number {
    const contributionMargin = pricePerUnit - variableCostPerUnit;
    if (contributionMargin <= 0) return -1;
    return fixedCosts / contributionMargin;
  }

  /**
   * Generate cash flow projections
   */
  static generateCashFlow(model: FinancialModel, years: number = 10): CashFlowItem[] {
    const cashFlows: CashFlowItem[] = [];
    let cumulativeCashFlow = 0;

    for (let year = 0; year < years; year++) {
      const revenue = this.calculateYearlyRevenue(model, year);
      const costs = this.calculateYearlyCosts(model, year);
      const netCashFlow = revenue - costs;
      cumulativeCashFlow += netCashFlow;

      cashFlows.push({
        year,
        revenue,
        costs,
        netCashFlow,
        cumulativeCashFlow
      });
    }

    return cashFlows;
  }

  /**
   * Calculate yearly revenue based on sales structure
   */
  private static calculateYearlyRevenue(model: FinancialModel, year: number): number {
    const { salesStructure } = model;
    let totalRevenue = 0;

    // Calculate revenue from regions
    salesStructure.regions.forEach(region => {
      if (region.year <= year) {
        const yearsFromStart = year - region.year;
        const adjustedVolume = region.volume * Math.pow(1 + region.growth, yearsFromStart);
        totalRevenue += adjustedVolume * region.price;
      }
    });

    // Calculate revenue from offers
    salesStructure.offers.forEach(offer => {
      if (offer.year <= year) {
        totalRevenue += offer.volume * offer.unitPrice;
      }
    });

    return totalRevenue;
  }

  /**
   * Calculate yearly costs based on cost structure
   */
  private static calculateYearlyCosts(model: FinancialModel, year: number): number {
    const { costStructure, parameters } = model;
    let totalCosts = 0;

    // CAPEX costs
    costStructure.capex.forEach(capex => {
      if (capex.year === year) {
        totalCosts += capex.amount;
      }
    });

    // OPEX costs
    costStructure.opex.forEach(opex => {
      if (year >= opex.startYear && year <= opex.endYear) {
        const inflationAdjustment = Math.pow(1 + parameters.inflationRates.production, year - opex.startYear);
        totalCosts += opex.amount * inflationAdjustment;
      }
    });

    // Tools costs (depreciation)
    costStructure.tools.forEach(tool => {
      const annualDepreciation = (tool.cost * tool.quantity) / tool.lifespan;
      if (year < tool.lifespan) {
        totalCosts += annualDepreciation;
      }
    });

    // Machinery costs (depreciation + maintenance)
    costStructure.machinery.forEach(machinery => {
      const annualDepreciation = machinery.cost / machinery.lifespan;
      if (year < machinery.lifespan) {
        totalCosts += annualDepreciation + machinery.maintenanceCost;
      }
    });

    return totalCosts;
  }

  /**
   * Calculate complete financial results for a model
   */
  static calculateFinancialResults(model: FinancialModel): FinancialResults {
    const cashFlow = this.generateCashFlow(model);
    const discountRate = model.parameters.discountRate;

    const npv = this.calculateNPV(cashFlow, discountRate);
    const irr = this.calculateIRR(cashFlow);
    const paybackPeriod = this.calculatePaybackPeriod(cashFlow);
    const yieldIndex = this.calculateYieldIndex(cashFlow, discountRate);

    const totalRevenue = cashFlow.reduce((sum, cf) => sum + cf.revenue, 0);
    const totalCosts = cashFlow.reduce((sum, cf) => sum + cf.costs, 0);
    const grossMargin = this.calculateGrossMargin(totalRevenue, totalCosts);

    const salesAtMaturity = this.calculateSalesAtMaturity(cashFlow);
    const breakEvenSales = this.calculateBreakEvenSales(
      totalCosts * 0.3, // Assume 30% fixed costs
      totalCosts * 0.7 / totalRevenue, // Variable cost per unit
      totalRevenue / (totalRevenue / 100) // Average price per unit
    );

    return {
      irr,
      npv,
      paybackPeriod,
      yieldIndex,
      grossMargin,
      salesAtMaturity,
      breakEvenSales,
      cashFlow
    };
  }

  /**
   * Calculate financial metrics for business case
   */
  static calculateBusinessCaseMetrics(businessCaseData: {
    timeframe: { startYear: number; endYear: number };
    financialData: {
      capex: Array<{ year: number; amount: number }>;
      opex: Array<{ year: number; amount: number }>;
      revenue?: Array<{ year: number; amount: number }>;
    };
  }) {
    const { timeframe, financialData } = businessCaseData;
    const discountRate = 0.10; // Default 10% discount rate

    // Generate cash flow for each year
    const cashFlow: CashFlowItem[] = [];
    for (let year = timeframe.startYear; year <= timeframe.endYear; year++) {
      const capexItem = financialData.capex.find(item => item.year === year);
      const opexItem = financialData.opex.find(item => item.year === year);
      const revenueItem = financialData.revenue?.find(item => item.year === year);

      const capex = capexItem ? capexItem.amount : 0;
      const opex = opexItem ? opexItem.amount : 0;
      const revenue = revenueItem ? revenueItem.amount : 0;
      const costs = capex + opex;
      const netCashFlow = revenue - costs;

      cashFlow.push({
        year: year - timeframe.startYear, // Normalize to 0-based index
        revenue,
        costs,
        netCashFlow,
        cumulativeCashFlow: 0 // Will be calculated below
      });
    }

    // Calculate cumulative cash flow
    let cumulative = 0;
    cashFlow.forEach(cf => {
      cumulative += cf.netCashFlow;
      cf.cumulativeCashFlow = cumulative;
    });

    // Calculate metrics
    const npv = this.calculateNPV(cashFlow, discountRate);
    const irr = this.calculateIRR(cashFlow);
    const paybackPeriod = this.calculatePaybackPeriod(cashFlow);

    const totalRevenue = cashFlow.reduce((sum, cf) => sum + cf.revenue, 0);
    const totalCosts = cashFlow.reduce((sum, cf) => sum + cf.costs, 0);
    const grossMargin = totalRevenue > 0 ? this.calculateGrossMargin(totalRevenue, totalCosts) : 0;

    // Commercial margin (custom business logic)
    const commercialMargin = this.calculateCommercialMargin(totalRevenue, totalCosts, financialData);

    return {
      irr,
      npv,
      paybackPeriod,
      grossMargin,
      commercialMargin
    };
  }

  /**
   * Calculate commercial margin based on business logic
   */
  private static calculateCommercialMargin(
    totalRevenue: number,
    totalCosts: number,
    financialData: any
  ): number {
    if (totalRevenue === 0) return 0;

    // Custom business logic for commercial margin
    // This could include factors like:
    // - Risk adjustments
    // - Market conditions
    // - Strategic value
    // - Operational efficiency factors

    const baseMargin = ((totalRevenue - totalCosts) / totalRevenue) * 100;

    // Apply risk adjustment (example: reduce margin by 5% for high CAPEX projects)
    const totalCapex = financialData.capex.reduce((sum: number, item: any) => sum + item.amount, 0);
    const riskAdjustment = totalCapex > 1000000 ? -5 : 0; // 5% reduction for high CAPEX

    // Apply strategic value bonus (example: 2% bonus for multi-year projects)
    const strategicBonus = financialData.capex.length > 1 ? 2 : 0;

    return baseMargin + riskAdjustment + strategicBonus;
  }
}
