import React from 'react';
import PropTypes from 'prop-types';

const KPICards = ({ kpis = {} }) => {
  console.log('🔍 KPICards received kpis:', kpis);

  // Helper function to extract value from KPI object or use fallback
  const getKPIValue = (kpiData, fallback = 0) => {
    if (typeof kpiData === 'object' && kpiData !== null && 'value' in kpiData) {
      return kpiData.value;
    }
    return typeof kpiData === 'number' ? kpiData : fallback;
  };

  // Helper function to extract change from KPI object or use fallback
  const getKPIChange = (kpiData, fallback = '+0%') => {
    if (typeof kpiData === 'object' && kpiData !== null && 'change' in kpiData) {
      return kpiData.change;
    }
    return fallback;
  };

  // Helper function to determine change type from change string
  const getChangeType = (change) => {
    if (typeof change === 'string') {
      return change.startsWith('+') || change.startsWith('↑') ? 'positive' : 'negative';
    }
    return 'positive';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${Number(value).toFixed(1)}%`;
  };

  const kpiData = [
    {
      title: 'Total Business Cases',
      value: getKPIValue(kpis.totalBusinessCases, 0),
      icon: 'fas fa-briefcase',
      color: 'blue',
      change: getKPIChange(kpis.totalBusinessCases, '+12%'),
      changeType: getChangeType(getKPIChange(kpis.totalBusinessCases, '+12%'))
    },
    {
      title: 'Total Investment',
      value: formatCurrency(getKPIValue(kpis.totalInvestment, 0)),
      icon: 'fas fa-dollar-sign',
      color: 'green',
      change: getKPIChange(kpis.totalInvestment, '+8%'),
      changeType: getChangeType(getKPIChange(kpis.totalInvestment, '+8%'))
    },
    {
      title: 'Average IRR',
      value: formatPercentage(getKPIValue(kpis.averageIRR, 0)),
      icon: 'fas fa-chart-line',
      color: 'purple',
      change: getKPIChange(kpis.averageIRR, '+2.3%'),
      changeType: getChangeType(getKPIChange(kpis.averageIRR, '+2.3%'))
    },
    {
      title: 'Total NPV',
      value: formatCurrency(getKPIValue(kpis.totalNPV, 0)),
      icon: 'fas fa-trending-up',
      color: 'indigo',
      change: getKPIChange(kpis.totalNPV, '+15%'),
      changeType: getChangeType(getKPIChange(kpis.totalNPV, '+15%'))
    },
    {
      title: 'Active Projects',
      value: getKPIValue(kpis.activeProjects, 0),
      icon: 'fas fa-project-diagram',
      color: 'orange',
      change: getKPIChange(kpis.activeProjects, '+5'),
      changeType: getChangeType(getKPIChange(kpis.activeProjects, '+5'))
    },
    {
      title: 'Completed Projects',
      value: getKPIValue(kpis.completedProjects, 0),
      icon: 'fas fa-check-circle',
      color: 'teal',
      change: getKPIChange(kpis.completedProjects, '+3'),
      changeType: getChangeType(getKPIChange(kpis.completedProjects, '+3'))
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      blue: 'bg-blue-500 text-blue-600 bg-blue-50',
      green: 'bg-green-500 text-green-600 bg-green-50',
      purple: 'bg-purple-500 text-purple-600 bg-purple-50',
      indigo: 'bg-indigo-500 text-indigo-600 bg-indigo-50',
      orange: 'bg-orange-500 text-orange-600 bg-orange-50',
      teal: 'bg-teal-500 text-teal-600 bg-teal-50'
    };
    return colors[color] || colors.blue;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {kpiData.map((kpi, index) => {
        const colorClasses = getColorClasses(kpi.color).split(' ');
        return (
          <div key={index} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">{kpi.title}</p>
                <p className="text-2xl font-bold text-gray-900">{kpi.value}</p>
                <div className="flex items-center mt-2">
                  <span className={`text-sm font-medium ${kpi.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
                    <i className={`fas ${kpi.changeType === 'positive' ? 'fa-arrow-up' : 'fa-arrow-down'} mr-1`}></i>
                    {kpi.change}
                  </span>
                  <span className="text-sm text-gray-500 ml-2">vs last month</span>
                </div>
              </div>
              <div className={`p-3 rounded-full ${colorClasses[2]}`}>
                <i className={`${kpi.icon} text-xl ${colorClasses[1]}`}></i>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

KPICards.propTypes = {
  kpis: PropTypes.object
};

export default KPICards;
