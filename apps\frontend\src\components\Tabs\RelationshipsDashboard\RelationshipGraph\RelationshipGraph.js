import React, { useMemo, useState } from 'react';
import './RelationshipGraph.css';

const RelationshipGraph = ({ data }) => {
  const [selectedNode, setSelectedNode] = useState(null);
  const [hoveredNode, setHoveredNode] = useState(null);

  // Process data for graph visualization
  const graphData = useMemo(() => {
    if (!data || !data.nodes || !data.edges) {
      return { nodes: [], edges: [], levels: {} };
    }

    // Define hierarchy levels based on your diagram
    const levelConfig = {
      idea: { level: 0, color: '#4CAF50', icon: '💡' },
      businessCase: { level: 1, color: '#2196F3', icon: '📊' },
      masterBusinessCase: { level: 2, color: '#9C27B0', icon: '🎯' },
      program: { level: 3, color: '#FF9800', icon: '🏢' },
      project: { level: 4, color: '#F44336', icon: '🚀' },
      epic: { level: 4, color: '#E91E63', icon: '⚡' }
    };

    // Group nodes by level
    const levels = {};
    data.nodes.forEach(node => {
      const config = levelConfig[node.type] || { level: 5, color: '#757575', icon: '❓' };
      const level = config.level;
      
      if (!levels[level]) {
        levels[level] = [];
      }
      
      levels[level].push({
        ...node,
        ...config,
        x: 0, // Will be calculated
        y: 0  // Will be calculated
      });
    });

    // Calculate positions
    const svgWidth = 1200;
    const svgHeight = 800;
    const levelHeight = svgHeight / (Object.keys(levels).length + 1);
    
    Object.keys(levels).forEach(level => {
      const levelNodes = levels[level];
      const nodeWidth = svgWidth / (levelNodes.length + 1);
      
      levelNodes.forEach((node, index) => {
        node.x = nodeWidth * (index + 1);
        node.y = levelHeight * (parseInt(level) + 1);
      });
    });

    // Process edges for drawing
    const processedEdges = data.edges.map(edge => {
      const sourceNode = data.nodes.find(n => n.id === edge.source);
      const targetNode = data.nodes.find(n => n.id === edge.target);
      
      if (!sourceNode || !targetNode) return null;
      
      const sourceConfig = levelConfig[sourceNode.type] || { level: 5 };
      const targetConfig = levelConfig[targetNode.type] || { level: 5 };
      
      const sourceLevel = levels[sourceConfig.level];
      const targetLevel = levels[targetConfig.level];
      
      const sourcePos = sourceLevel?.find(n => n.id === edge.source);
      const targetPos = targetLevel?.find(n => n.id === edge.target);
      
      if (!sourcePos || !targetPos) return null;
      
      return {
        ...edge,
        x1: sourcePos.x,
        y1: sourcePos.y,
        x2: targetPos.x,
        y2: targetPos.y,
        isBidirectional: edge.type === 'updates' || edge.type === 'includes'
      };
    }).filter(Boolean);

    return {
      nodes: Object.values(levels).flat(),
      edges: processedEdges,
      levels,
      svgWidth,
      svgHeight
    };
  }, [data]);

  const handleNodeClick = (node) => {
    setSelectedNode(selectedNode?.id === node.id ? null : node);
  };

  const getNodeRadius = (node) => {
    return selectedNode?.id === node.id ? 35 : hoveredNode?.id === node.id ? 30 : 25;
  };

  const getEdgeColor = (edge) => {
    if (edge.type === 'leads_to') return '#4CAF50';
    if (edge.type === 'connects_to') return '#2196F3';
    if (edge.type === 'includes') return '#FF9800';
    if (edge.type === 'updates') return '#F44336';
    if (edge.type === 'implements') return '#9C27B0';
    return '#757575';
  };

  const getEdgeWidth = (edge) => {
    return edge.isBidirectional ? 3 : 2;
  };

  if (!data || !data.nodes || data.nodes.length === 0) {
    return (
      <div className="relationship-graph">
        <div className="no-data">
          <i className="fas fa-project-diagram text-4xl text-gray-400 mb-4"></i>
          <p className="text-gray-600">No relationship data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relationship-graph">
      <div className="graph-header">
        <h3>
          <i className="fas fa-project-diagram mr-2"></i>
          Relationship Graph
        </h3>
        <div className="graph-stats">
          <span className="stat">
            <i className="fas fa-circle mr-1"></i>
            {graphData.nodes.length} Entities
          </span>
          <span className="stat">
            <i className="fas fa-arrow-right mr-1"></i>
            {graphData.edges.length} Connections
          </span>
        </div>
      </div>

      <div className="graph-container">
        <svg 
          width={graphData.svgWidth} 
          height={graphData.svgHeight}
          className="relationship-svg"
        >
          {/* Grid background */}
          <defs>
            <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
              <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#f0f0f0" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />

          {/* Edges */}
          {graphData.edges.map((edge, index) => (
            <g key={`edge-${index}`}>
              <line
                x1={edge.x1}
                y1={edge.y1}
                x2={edge.x2}
                y2={edge.y2}
                stroke={getEdgeColor(edge)}
                strokeWidth={getEdgeWidth(edge)}
                strokeDasharray={edge.isBidirectional ? "5,5" : "none"}
                className="graph-edge"
              />
              
              {/* Edge label */}
              <text
                x={(edge.x1 + edge.x2) / 2}
                y={(edge.y1 + edge.y2) / 2 - 5}
                textAnchor="middle"
                className="edge-label"
                fontSize="10"
                fill="#666"
              >
                {edge.label}
              </text>

              {/* Arrow for direction */}
              {!edge.isBidirectional && (
                <polygon
                  points={`${edge.x2-8},${edge.y2-4} ${edge.x2},${edge.y2} ${edge.x2-8},${edge.y2+4}`}
                  fill={getEdgeColor(edge)}
                  className="arrow"
                />
              )}
            </g>
          ))}

          {/* Nodes */}
          {graphData.nodes.map((node) => (
            <g 
              key={node.id}
              className="graph-node"
              onClick={() => handleNodeClick(node)}
              onMouseEnter={() => setHoveredNode(node)}
              onMouseLeave={() => setHoveredNode(null)}
              style={{ cursor: 'pointer' }}
            >
              <circle
                cx={node.x}
                cy={node.y}
                r={getNodeRadius(node)}
                fill={node.color}
                stroke={selectedNode?.id === node.id ? '#333' : '#fff'}
                strokeWidth={selectedNode?.id === node.id ? 3 : 2}
                className="node-circle"
              />
              
              <text
                x={node.x}
                y={node.y}
                textAnchor="middle"
                dominantBaseline="middle"
                fontSize="16"
                className="node-icon"
              >
                {node.icon}
              </text>
              
              <text
                x={node.x}
                y={node.y + getNodeRadius(node) + 15}
                textAnchor="middle"
                className="node-label"
                fontSize="12"
                fill="#333"
              >
                {node.label.length > 20 ? `${node.label.substring(0, 20)}...` : node.label}
              </text>
            </g>
          ))}
        </svg>
      </div>

      {/* Legend */}
      <div className="graph-legend">
        <h4>Entity Types</h4>
        <div className="legend-items">
          <div className="legend-item">
            <span className="legend-icon" style={{backgroundColor: '#4CAF50'}}>💡</span>
            <span>Ideas</span>
          </div>
          <div className="legend-item">
            <span className="legend-icon" style={{backgroundColor: '#2196F3'}}>📊</span>
            <span>Business Cases</span>
          </div>
          <div className="legend-item">
            <span className="legend-icon" style={{backgroundColor: '#9C27B0'}}>🎯</span>
            <span>Master Business Cases</span>
          </div>
          <div className="legend-item">
            <span className="legend-icon" style={{backgroundColor: '#FF9800'}}>🏢</span>
            <span>Programs</span>
          </div>
          <div className="legend-item">
            <span className="legend-icon" style={{backgroundColor: '#F44336'}}>🚀</span>
            <span>Projects</span>
          </div>
          <div className="legend-item">
            <span className="legend-icon" style={{backgroundColor: '#E91E63'}}>⚡</span>
            <span>Epics</span>
          </div>
        </div>
      </div>

      {/* Selected Node Details */}
      {selectedNode && (
        <div className="node-details-panel">
          <div className="details-header">
            <span className="details-icon" style={{backgroundColor: selectedNode.color}}>
              {selectedNode.icon}
            </span>
            <div>
              <h4>{selectedNode.label}</h4>
              <p>{selectedNode.type}</p>
            </div>
            <button onClick={() => setSelectedNode(null)}>
              <i className="fas fa-times"></i>
            </button>
          </div>
          
          <div className="details-content">
            {selectedNode.data && (
              <div className="detail-section">
                <h5>Details</h5>
                {selectedNode.data.businessUnit && (
                  <p><strong>Business Unit:</strong> {selectedNode.data.businessUnit}</p>
                )}
                {selectedNode.data.status && (
                  <p><strong>Status:</strong> {selectedNode.data.status}</p>
                )}
                {selectedNode.data.totalCapex && (
                  <p><strong>Total CAPEX:</strong> ${selectedNode.data.totalCapex.toLocaleString()}</p>
                )}
                {selectedNode.data.totalOpex && (
                  <p><strong>Total OPEX:</strong> ${selectedNode.data.totalOpex.toLocaleString()}</p>
                )}
                {selectedNode.data.totalRevenue && (
                  <p><strong>Total Revenue:</strong> ${selectedNode.data.totalRevenue.toLocaleString()}</p>
                )}
                {selectedNode.data.irr && (
                  <p><strong>IRR:</strong> {selectedNode.data.irr}%</p>
                )}
                {selectedNode.data.npv && (
                  <p><strong>NPV:</strong> ${selectedNode.data.npv.toLocaleString()}</p>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default RelationshipGraph;
