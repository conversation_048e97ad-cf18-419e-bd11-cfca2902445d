import React, { useMemo } from 'react';

const RequestStats = ({ requests }) => {
  const stats = useMemo(() => {
    const total = requests.length;
    
    // Status distribution
    const statusCounts = requests.reduce((acc, req) => {
      acc[req.status] = (acc[req.status] || 0) + 1;
      return acc;
    }, {});

    // Priority distribution
    const priorityCounts = requests.reduce((acc, req) => {
      acc[req.priority] = (acc[req.priority] || 0) + 1;
      return acc;
    }, {});

    // Request type distribution
    const typeCounts = requests.reduce((acc, req) => {
      acc[req.requestType] = (acc[req.requestType] || 0) + 1;
      return acc;
    }, {});

    // Average scores
    const avgUrgency = requests.reduce((sum, req) => sum + (req.urgencyScore || 0), 0) / total || 0;
    const avgCustomerValue = requests.reduce((sum, req) => sum + (req.customerValueScore || 0), 0) / total || 0;
    const avgStrategicAlignment = requests.reduce((sum, req) => sum + (req.strategicAlignmentScore || 0), 0) / total || 0;

    // Recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentRequests = requests.filter(req => new Date(req.dateSubmitted) >= thirtyDaysAgo);

    return {
      total,
      statusCounts,
      priorityCounts,
      typeCounts,
      avgUrgency: avgUrgency.toFixed(1),
      avgCustomerValue: avgCustomerValue.toFixed(1),
      avgStrategicAlignment: avgStrategicAlignment.toFixed(1),
      recentCount: recentRequests.length
    };
  }, [requests]);

  const renderStatusChart = () => (
    <div className="chart-container">
      <h4>Status Distribution</h4>
      <div className="status-chart">
        {Object.entries(stats.statusCounts).map(([status, count]) => {
          const percentage = ((count / stats.total) * 100).toFixed(1);
          return (
            <div key={status} className="status-bar">
              <div className="status-info">
                <span className="status-name">{status}</span>
                <span className="status-count">{count} ({percentage}%)</span>
              </div>
              <div className="progress-bar">
                <div 
                  className={`progress-fill status-${status.toLowerCase().replace(' ', '-')}`}
                  style={{ width: `${percentage}%` }}
                ></div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );

  const renderPriorityChart = () => (
    <div className="chart-container">
      <h4>Priority Distribution</h4>
      <div className="priority-chart">
        {Object.entries(stats.priorityCounts).map(([priority, count]) => {
          const percentage = ((count / stats.total) * 100).toFixed(1);
          return (
            <div key={priority} className="priority-item">
              <div className="priority-header">
                <span className={`priority-badge priority-${priority.toLowerCase()}`}>
                  {priority}
                </span>
                <span className="priority-count">{count}</span>
              </div>
              <div className="priority-percentage">{percentage}%</div>
            </div>
          );
        })}
      </div>
    </div>
  );

  const renderTypeChart = () => (
    <div className="chart-container">
      <h4>Request Type Distribution</h4>
      <div className="type-chart">
        {Object.entries(stats.typeCounts).map(([type, count]) => {
          const percentage = ((count / stats.total) * 100).toFixed(1);
          return (
            <div key={type} className="type-item">
              <div className="type-info">
                <span className="type-name">{type}</span>
                <span className="type-stats">{count} requests ({percentage}%)</span>
              </div>
              <div className="type-bar">
                <div 
                  className="type-fill"
                  style={{ width: `${percentage}%` }}
                ></div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );

  return (
    <div className="request-stats">
      <div className="stats-header">
        <h3>Request Statistics & Analytics</h3>
        <p>Comprehensive overview of request management metrics</p>
      </div>

      {/* Key Metrics */}
      <div className="key-metrics">
        <div className="metric-card">
          <div className="metric-icon">
            <i className="fas fa-clipboard-list text-blue-500"></i>
          </div>
          <div className="metric-content">
            <span className="metric-number">{stats.total}</span>
            <span className="metric-label">Total Requests</span>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon">
            <i className="fas fa-clock text-green-500"></i>
          </div>
          <div className="metric-content">
            <span className="metric-number">{stats.recentCount}</span>
            <span className="metric-label">Last 30 Days</span>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon">
            <i className="fas fa-exclamation-triangle text-orange-500"></i>
          </div>
          <div className="metric-content">
            <span className="metric-number">{stats.avgUrgency}</span>
            <span className="metric-label">Avg Urgency Score</span>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon">
            <i className="fas fa-star text-purple-500"></i>
          </div>
          <div className="metric-content">
            <span className="metric-number">{stats.avgCustomerValue}</span>
            <span className="metric-label">Avg Customer Value</span>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon">
            <i className="fas fa-bullseye text-red-500"></i>
          </div>
          <div className="metric-content">
            <span className="metric-number">{stats.avgStrategicAlignment}</span>
            <span className="metric-label">Avg Strategic Alignment</span>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="charts-grid">
        {renderStatusChart()}
        {renderPriorityChart()}
        {renderTypeChart()}
      </div>

      {/* Validation Metrics */}
      <div className="validation-metrics">
        <h4>Validation & Quality Metrics</h4>
        <div className="validation-grid">
          <div className="validation-item">
            <i className="fas fa-check-circle text-green-500"></i>
            <div>
              <span className="validation-label">Requests with Clear Description</span>
              <span className="validation-value">
                {requests.filter(r => r.isRequestClear).length} / {stats.total}
              </span>
            </div>
          </div>

          <div className="validation-item">
            <i className="fas fa-dollar-sign text-blue-500"></i>
            <div>
              <span className="validation-label">Business Value Defined</span>
              <span className="validation-value">
                {requests.filter(r => r.isBusinessValueDefined).length} / {stats.total}
              </span>
            </div>
          </div>

          <div className="validation-item">
            <i className="fas fa-bullseye text-purple-500"></i>
            <div>
              <span className="validation-label">Strategically Aligned</span>
              <span className="validation-value">
                {requests.filter(r => r.isStrategicallyAligned).length} / {stats.total}
              </span>
            </div>
          </div>

          <div className="validation-item">
            <i className="fas fa-users text-orange-500"></i>
            <div>
              <span className="validation-label">Stakeholders Involved</span>
              <span className="validation-value">
                {requests.filter(r => r.areStakeholdersInvolved).length} / {stats.total}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="recent-activity">
        <h4>Recent Activity</h4>
        <div className="activity-list">
          {requests
            .sort((a, b) => new Date(b.dateSubmitted) - new Date(a.dateSubmitted))
            .slice(0, 5)
            .map(request => (
              <div key={request.id} className="activity-item">
                <div className="activity-icon">
                  <i className="fas fa-plus-circle text-blue-500"></i>
                </div>
                <div className="activity-content">
                  <span className="activity-title">{request.title}</span>
                  <span className="activity-meta">
                    {request.requestType} • {request.priority} Priority • {request.submittedBy}
                  </span>
                  <span className="activity-time">
                    {new Date(request.dateSubmitted).toLocaleDateString()}
                  </span>
                </div>
                <div className={`activity-status status-${request.status.toLowerCase().replace(' ', '-')}`}>
                  {request.status}
                </div>
              </div>
            ))}
        </div>
      </div>

      {/* Export Options */}
      <div className="export-options">
        <h4>Export & Reports</h4>
        <div className="export-buttons">
          <button className="export-btn">
            <i className="fas fa-file-excel"></i>
            Export to Excel
          </button>
          <button className="export-btn">
            <i className="fas fa-file-pdf"></i>
            Generate Report
          </button>
          <button className="export-btn">
            <i className="fas fa-chart-bar"></i>
            Dashboard View
          </button>
        </div>
      </div>
    </div>
  );
};

export default RequestStats;
