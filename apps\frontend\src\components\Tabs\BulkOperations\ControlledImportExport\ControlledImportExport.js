import React, { useState } from 'react';
import DataTypeSelector from './DataTypeSelector';
import TargetObjectSelector from './TargetObjectSelector';
import ScopeSelector from './ScopeSelector';
import PreviewValidation from './PreviewValidation';
import ImportExportActions from './ImportExportActions';
import './ControlledImportExport.css';

const ControlledImportExport = () => {
  const [operation, setOperation] = useState('import'); // 'import' or 'export'
  const [selectedDataType, setSelectedDataType] = useState('');
  const [selectedTargetObject, setSelectedTargetObject] = useState('');
  const [selectedScope, setSelectedScope] = useState({});
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewData, setPreviewData] = useState(null);
  const [validationResults, setValidationResults] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Reset form when operation changes
  const handleOperationChange = (newOperation) => {
    setOperation(newOperation);
    setSelectedDataType('');
    setSelectedTargetObject('');
    setSelectedScope({});
    setSelectedFile(null);
    setPreviewData(null);
    setValidationResults(null);
  };

  // Handle data type selection
  const handleDataTypeChange = (dataType) => {
    setSelectedDataType(dataType);
    // Reset dependent selections
    setSelectedTargetObject('');
    setSelectedScope({});
    setPreviewData(null);
    setValidationResults(null);
  };

  // Handle target object selection
  const handleTargetObjectChange = (targetObject) => {
    setSelectedTargetObject(targetObject);
    // Reset dependent selections
    setSelectedScope({});
    setPreviewData(null);
    setValidationResults(null);
  };

  // Handle scope selection
  const handleScopeChange = (scope) => {
    setSelectedScope(scope);
    setPreviewData(null);
    setValidationResults(null);
  };

  // Handle file selection for import
  const handleFileSelect = (file) => {
    setSelectedFile(file);
    setPreviewData(null);
    setValidationResults(null);
  };

  // Handle preview generation
  const handlePreview = async () => {
    if (operation === 'import' && selectedFile) {
      // Generate import preview
      setIsProcessing(true);
      try {
        // TODO: Implement file parsing and preview generation
        const mockPreview = {
          totalRows: 150,
          validRows: 145,
          invalidRows: 5,
          columns: ['Year', 'Amount', 'Description'],
          sampleData: [
            { Year: 2025, Amount: 100000, Description: 'OPEX Year 1' },
            { Year: 2026, Amount: 120000, Description: 'OPEX Year 2' },
            { Year: 2027, Amount: 140000, Description: 'OPEX Year 3' }
          ]
        };
        setPreviewData(mockPreview);
        
        const mockValidation = {
          errors: [
            { row: 15, column: 'Amount', message: 'Invalid number format' },
            { row: 23, column: 'Year', message: 'Year must be between 2020-2030' }
          ],
          warnings: [
            { row: 45, column: 'Description', message: 'Description is empty' }
          ]
        };
        setValidationResults(mockValidation);
      } catch (error) {
        console.error('Preview generation failed:', error);
      } finally {
        setIsProcessing(false);
      }
    } else if (operation === 'export') {
      // Generate export preview
      setIsProcessing(true);
      try {
        // TODO: Implement export data preview
        const mockPreview = {
          totalRecords: 25,
          selectedRecords: 25,
          dataTypes: [selectedDataType],
          estimatedFileSize: '2.5 MB'
        };
        setPreviewData(mockPreview);
      } catch (error) {
        console.error('Export preview failed:', error);
      } finally {
        setIsProcessing(false);
      }
    }
  };

  // Check if preview can be generated
  const canPreview = () => {
    const hasBasicSelections = selectedDataType && selectedTargetObject && Object.keys(selectedScope).length > 0;
    
    if (operation === 'import') {
      return hasBasicSelections && selectedFile;
    } else {
      return hasBasicSelections;
    }
  };

  return (
    <div className="controlled-import-export">
      {/* Operation Type Selection */}
      <div className="operation-selector">
        <h3>Select Operation Type</h3>
        <div className="operation-buttons">
          <button
            className={`operation-btn ${operation === 'import' ? 'active' : ''}`}
            onClick={() => handleOperationChange('import')}
          >
            <i className="fas fa-upload"></i>
            <div>
              <span className="btn-title">Import Data</span>
              <span className="btn-description">Upload and import data from files</span>
            </div>
          </button>
          <button
            className={`operation-btn ${operation === 'export' ? 'active' : ''}`}
            onClick={() => handleOperationChange('export')}
          >
            <i className="fas fa-download"></i>
            <div>
              <span className="btn-title">Export Data</span>
              <span className="btn-description">Export selected data to files</span>
            </div>
          </button>
        </div>
      </div>

      {/* Configuration Steps */}
      <div className="configuration-steps">
        {/* Step 1: Data Type Selection */}
        <div className="config-step">
          <div className="step-header">
            <span className="step-number">1</span>
            <h4>Select Data Type</h4>
          </div>
          <DataTypeSelector
            selectedDataType={selectedDataType}
            onDataTypeChange={handleDataTypeChange}
            operation={operation}
          />
        </div>

        {/* Step 2: Target Object Selection */}
        {selectedDataType && (
          <div className="config-step">
            <div className="step-header">
              <span className="step-number">2</span>
              <h4>Select Target Object</h4>
            </div>
            <TargetObjectSelector
              selectedTargetObject={selectedTargetObject}
              onTargetObjectChange={handleTargetObjectChange}
              dataType={selectedDataType}
              operation={operation}
            />
          </div>
        )}

        {/* Step 3: Scope Selection */}
        {selectedTargetObject && (
          <div className="config-step">
            <div className="step-header">
              <span className="step-number">3</span>
              <h4>Define Scope</h4>
            </div>
            <ScopeSelector
              selectedScope={selectedScope}
              onScopeChange={handleScopeChange}
              targetObject={selectedTargetObject}
              operation={operation}
            />
          </div>
        )}

        {/* Step 4: File Selection (Import only) */}
        {operation === 'import' && selectedTargetObject && (
          <div className="config-step">
            <div className="step-header">
              <span className="step-number">4</span>
              <h4>Select File</h4>
            </div>
            <div className="file-selector">
              <input
                type="file"
                id="import-file"
                accept=".xlsx,.xls,.csv,.json"
                onChange={(e) => handleFileSelect(e.target.files[0])}
                style={{ display: 'none' }}
              />
              <label htmlFor="import-file" className="file-upload-btn">
                <i className="fas fa-file-upload"></i>
                {selectedFile ? selectedFile.name : 'Choose File'}
              </label>
              <div className="file-info">
                <p>Supported formats: Excel (.xlsx, .xls), CSV (.csv), JSON (.json)</p>
                {selectedFile && (
                  <div className="selected-file-info">
                    <span>Size: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB</span>
                    <span>Type: {selectedFile.type}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Step 5: Preview & Validation */}
        {canPreview() && (
          <div className="config-step">
            <div className="step-header">
              <span className="step-number">{operation === 'import' ? '5' : '4'}</span>
              <h4>Preview & Validation</h4>
            </div>
            <div className="preview-section">
              <button
                className="preview-btn"
                onClick={handlePreview}
                disabled={isProcessing}
              >
                <i className={`fas ${isProcessing ? 'fa-spinner fa-spin' : 'fa-eye'}`}></i>
                {isProcessing ? 'Generating Preview...' : `Generate ${operation === 'import' ? 'Import' : 'Export'} Preview`}
              </button>
              
              {previewData && (
                <PreviewValidation
                  previewData={previewData}
                  validationResults={validationResults}
                  operation={operation}
                />
              )}
            </div>
          </div>
        )}

        {/* Step 6: Execute Operation */}
        {previewData && (
          <div className="config-step">
            <div className="step-header">
              <span className="step-number">{operation === 'import' ? '6' : '5'}</span>
              <h4>Execute Operation</h4>
            </div>
            <ImportExportActions
              operation={operation}
              selectedDataType={selectedDataType}
              selectedTargetObject={selectedTargetObject}
              selectedScope={selectedScope}
              selectedFile={selectedFile}
              previewData={previewData}
              validationResults={validationResults}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ControlledImportExport;
