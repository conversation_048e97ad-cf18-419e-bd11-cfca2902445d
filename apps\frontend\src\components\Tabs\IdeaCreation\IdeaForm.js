import React, { useState, useEffect } from 'react';
import { ideaService } from '../../../services/ideaService';

const IdeaForm = ({ idea, onSubmit, onCancel, isEditing = false }) => {
  const [formData, setFormData] = useState({
    title: '',
    problemStatement: '',
    opportunityDescription: '',
    highLevelBusinessRequirements: '',
    businessUnitId: '',
    priority: 'medium',
    category: '',
    estimatedCost: '',
    expectedBenefit: '',
    timeframe: '',
    tags: '',
    description: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [businessUnits] = useState([
    { id: 'bu1', name: 'Technology' },
    { id: 'bu2', name: 'Marketing' },
    { id: 'bu3', name: 'Operations' },
    { id: 'bu4', name: 'Finance' },
    { id: 'bu5', name: 'Human Resources' },
    { id: 'bu6', name: 'Sales' }
  ]);

  const categories = [
    'Process Improvement',
    'Technology Innovation',
    'Cost Reduction',
    'Revenue Generation',
    'Customer Experience',
    'Employee Experience',
    'Sustainability',
    'Digital Transformation',
    'Product Development',
    'Market Expansion'
  ];

  const priorities = [
    { value: 'low', label: 'Low', color: 'text-green-600' },
    { value: 'medium', label: 'Medium', color: 'text-yellow-600' },
    { value: 'high', label: 'High', color: 'text-orange-600' },
    { value: 'critical', label: 'Critical', color: 'text-red-600' }
  ];

  useEffect(() => {
    if (idea && isEditing) {
      setFormData({
        title: idea.title || '',
        problemStatement: idea.problemStatement || '',
        opportunityDescription: idea.opportunityDescription || '',
        highLevelBusinessRequirements: idea.highLevelBusinessRequirements || '',
        businessUnitId: idea.businessUnitId || '',
        priority: idea.priority || 'medium',
        category: idea.category || '',
        estimatedCost: idea.estimatedCost || '',
        expectedBenefit: idea.expectedBenefit || '',
        timeframe: idea.timeframe || '',
        tags: idea.tags ? idea.tags.join(', ') : '',
        description: idea.description || ''
      });
    }
  }, [idea, isEditing]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const dataToValidate = {
      ...formData,
      submitterName: 'temp' // This will be set by parent component
    };

    console.log('🔍 Validating form data:', dataToValidate);
    const validation = ideaService.validateIdeaData(dataToValidate);
    console.log('🔍 Validation result:', validation);

    setErrors(validation.errors);
    return validation.isValid;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('🚀 Form submission started');
    console.log('📝 Form data:', formData);

    if (!validateForm()) {
      console.log('❌ Form validation failed');
      console.log('🔍 Current errors:', errors);
      // TEMPORARY: Skip validation for debugging
      console.log('⚠️ BYPASSING VALIDATION FOR DEBUGGING');
      // return;
    }

    console.log('✅ Form validation passed');
    setIsSubmitting(true);

    try {
      const submitData = {
        ...formData,
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [],
        estimatedCost: formData.estimatedCost ? parseFloat(formData.estimatedCost) : null,
        expectedBenefit: formData.expectedBenefit ? parseFloat(formData.expectedBenefit) : null
      };

      console.log('📤 Submitting data:', submitData);
      const result = await onSubmit(submitData);
      console.log('📥 Submission result:', result);

      if (result.success) {
        console.log('✅ Idea created successfully');
        // Form will be closed by parent component
      } else {
        console.log('❌ Submission failed:', result.error);
        setErrors({ submit: result.error });
      }
    } catch (error) {
      console.log('💥 Submission error:', error);
      setErrors({ submit: error.message });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">
          <i className={`fas ${isEditing ? 'fa-edit' : 'fa-plus'} mr-2 text-green-600`}></i>
          {isEditing ? 'Edit Idea' : 'Submit New Idea'}
        </h3>
        <p className="text-gray-600 mt-1">
          {isEditing ? 'Update your idea details' : 'Share your innovative idea with the team'}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        {/* Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Idea Title *
          </label>
          <input
            type="text"
            name="title"
            value={formData.title}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
              errors.title ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Enter a clear, descriptive title for your idea"
          />
          {errors.title && <p className="text-red-600 text-sm mt-1">{errors.title}</p>}
        </div>

        {/* Business Unit and Priority */}
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Business Unit *
            </label>
            <select
              name="businessUnitId"
              value={formData.businessUnitId}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                errors.businessUnitId ? 'border-red-300' : 'border-gray-300'
              }`}
            >
              <option value="">Select Business Unit</option>
              {businessUnits.map(unit => (
                <option key={unit.id} value={unit.id}>{unit.name}</option>
              ))}
            </select>
            {errors.businessUnitId && <p className="text-red-600 text-sm mt-1">{errors.businessUnitId}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Priority
            </label>
            <select
              name="priority"
              value={formData.priority}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            >
              {priorities.map(priority => (
                <option key={priority.value} value={priority.value}>
                  {priority.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Category */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Category
          </label>
          <select
            name="category"
            value={formData.category}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
          >
            <option value="">Select Category</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>

        {/* Problem Statement */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Problem Statement *
          </label>
          <textarea
            name="problemStatement"
            value={formData.problemStatement}
            onChange={handleInputChange}
            rows={3}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
              errors.problemStatement ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Describe the problem or challenge this idea addresses"
          />
          {errors.problemStatement && <p className="text-red-600 text-sm mt-1">{errors.problemStatement}</p>}
        </div>

        {/* Opportunity Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Opportunity Description *
          </label>
          <textarea
            name="opportunityDescription"
            value={formData.opportunityDescription}
            onChange={handleInputChange}
            rows={3}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
              errors.opportunityDescription ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Explain the opportunity and potential benefits"
          />
          {errors.opportunityDescription && <p className="text-red-600 text-sm mt-1">{errors.opportunityDescription}</p>}
        </div>

        {/* Business Requirements */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            High-Level Business Requirements
          </label>
          <textarea
            name="highLevelBusinessRequirements"
            value={formData.highLevelBusinessRequirements}
            onChange={handleInputChange}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Outline the key business requirements for implementing this idea"
          />
        </div>

        {/* Financial Estimates */}
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Estimated Cost ($)
            </label>
            <input
              type="number"
              name="estimatedCost"
              value={formData.estimatedCost}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="0"
              min="0"
              step="0.01"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Expected Benefit ($)
            </label>
            <input
              type="number"
              name="expectedBenefit"
              value={formData.expectedBenefit}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="0"
              min="0"
              step="0.01"
            />
          </div>
        </div>

        {/* Timeframe and Tags */}
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Implementation Timeframe
            </label>
            <input
              type="text"
              name="timeframe"
              value={formData.timeframe}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="e.g., 3-6 months"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tags
            </label>
            <input
              type="text"
              name="tags"
              value={formData.tags}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="innovation, efficiency, automation (comma-separated)"
            />
          </div>
        </div>

        {/* Additional Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Additional Details
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Any additional information or context about your idea"
          />
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-red-600 text-sm">{errors.submit}</p>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSubmitting ? (
              <>
                <i className="fas fa-spinner fa-spin mr-2"></i>
                {isEditing ? 'Updating...' : 'Submitting...'}
              </>
            ) : (
              <>
                <i className={`fas ${isEditing ? 'fa-save' : 'fa-paper-plane'} mr-2`}></i>
                {isEditing ? 'Update Idea' : 'Submit Idea'}
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default IdeaForm;
