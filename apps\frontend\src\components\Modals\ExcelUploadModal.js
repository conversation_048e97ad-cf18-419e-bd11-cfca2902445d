import React, { useState } from 'react';
import PropTypes from 'prop-types';
import Modal from './Modal';

const ExcelUploadModal = ({ isOpen, onClose }) => {
  const [file, setFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [previewData, setPreviewData] = useState(null);

  const handleFileSelect = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      setFile(selectedFile);
      // Here you would typically parse the Excel file for preview
      // For now, we'll just show a placeholder
      setPreviewData({
        fileName: selectedFile.name,
        rowCount: 'Analyzing...',
        columns: ['Loading...']
      });
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile && (droppedFile.type.includes('excel') || droppedFile.name.endsWith('.xlsx') || droppedFile.name.endsWith('.xls'))) {
      setFile(droppedFile);
      setPreviewData({
        fileName: droppedFile.name,
        rowCount: 'Analyzing...',
        columns: ['Loading...']
      });
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleUpload = async () => {
    if (!file) return;

    setIsUploading(true);
    try {
      // Simulate upload process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Reset and close
      setFile(null);
      setPreviewData(null);
      onClose();
      
      // Show success message
      alert('Excel file uploaded successfully!');
    } catch (error) {
      alert('Upload failed. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleClose = () => {
    setFile(null);
    setPreviewData(null);
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Excel Import" size="lg">
      <div className="space-y-6">
        {/* File Upload Area */}
        <div
          className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors"
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          <input
            type="file"
            accept=".xlsx,.xls"
            onChange={handleFileSelect}
            className="hidden"
            id="excel-file-input"
          />
          <i className="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
          <p className="text-lg text-gray-600 mb-2">Drop your Excel file here or click to browse</p>
          <p className="text-sm text-gray-500 mb-4">Supports .xlsx files up to 10MB</p>
          <button
            type="button"
            onClick={() => document.getElementById('excel-file-input').click()}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            disabled={isUploading}
          >
            Choose File
          </button>
        </div>

        {/* File Preview */}
        {previewData && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Preview Data</h3>
            <div className="flex justify-between items-center mb-4">
              <span className="text-sm text-gray-600">
                File: <span className="font-medium">{previewData.fileName}</span>
              </span>
              <span className="text-sm text-gray-600">
                Rows: <span className="font-medium">{previewData.rowCount}</span>
              </span>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="text-center text-gray-500 py-8">
                <i className="fas fa-file-excel text-3xl mb-3"></i>
                <p>Excel preview will be displayed here</p>
                <p className="text-sm">Showing first 10 rows with column headers</p>
              </div>
            </div>

            {/* Validation Results */}
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center">
                <i className="fas fa-check-circle text-green-600 mr-2"></i>
                <span className="text-green-800 font-medium">Validation Passed</span>
              </div>
              <p className="text-green-700 text-sm mt-1">
                File format is valid and ready for import
              </p>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={handleClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
            disabled={isUploading}
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleUpload}
            disabled={!file || isUploading}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isUploading ? (
              <>
                <div className="loading-spinner mr-2"></div>
                Importing...
              </>
            ) : (
              <>
                <i className="fas fa-check mr-2"></i>
                Import Data
              </>
            )}
          </button>
        </div>
      </div>
    </Modal>
  );
};

ExcelUploadModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default ExcelUploadModal;
