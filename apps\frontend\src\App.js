import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>outer as Router } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Components
import Navigation from './components/Navigation/Navigation';
import WelcomeSection from './components/WelcomeSection/WelcomeSection';
import MainApplication from './components/MainApplication/MainApplication';
import LoginModal from './components/Modals/LoginModal';
import FeedbackModal from './components/Modals/FeedbackModal';
import ExcelUploadModal from './components/Modals/ExcelUploadModal';
import TourModal from './components/Tour/TourModal';

// Context
import { AuthProvider } from './context/AuthContext';
import { DataProvider } from './context/DataContext';
import { UIProvider } from './context/UIContext';

// Services
import { authService } from './services/authService';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Modal states
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [showExcelUploadModal, setShowExcelUploadModal] = useState(false);
  const [showTourModal, setShowTourModal] = useState(false);

  useEffect(() => {
    // Check if user is already authenticated
    const checkAuthStatus = async () => {
      try {
        const token = localStorage.getItem('authToken');
        if (token) {
          const user = await authService.validateToken(token);
          if (user) {
            setIsAuthenticated(true);
            setCurrentUser(user);
          }
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        localStorage.removeItem('authToken');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  const handleLogin = async (credentials) => {
    try {
      const response = await authService.login(credentials);
      if (response.success) {
        setIsAuthenticated(true);
        setCurrentUser(response.data.user);
        setShowLoginModal(false);
        localStorage.setItem('authToken', response.data.token);
        return { success: true };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    setCurrentUser(null);
    localStorage.removeItem('authToken');
    authService.logout();
  };

  const openLoginModal = () => setShowLoginModal(true);
  const closeLoginModal = () => setShowLoginModal(false);

  const openFeedbackModal = () => setShowFeedbackModal(true);
  const closeFeedbackModal = () => setShowFeedbackModal(false);

  const openExcelUploadModal = () => setShowExcelUploadModal(true);
  const closeExcelUploadModal = () => setShowExcelUploadModal(false);

  const openTourModal = () => setShowTourModal(true);
  const closeTourModal = () => setShowTourModal(false);

  // Use the functions to avoid unused variable warnings
  const handleExcelUpload = () => openExcelUploadModal();
  const handleTour = () => openTourModal();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Business Case Management System...</p>
        </div>
      </div>
    );
  }

  return (
    <Router>
      <AuthProvider>
        <DataProvider>
          <UIProvider>
            <div className="min-h-screen bg-gray-50">
              {/* Navigation */}
              <Navigation
                isAuthenticated={isAuthenticated}
                currentUser={currentUser}
                onLogin={openLoginModal}
                onLogout={handleLogout}
                onFeedback={openFeedbackModal}
              />

              {/* Main Content */}
              <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                {/* Temporarily bypass authentication for testing */}
                <MainApplication />
              </main>

              {/* Modals */}
              <LoginModal
                isOpen={showLoginModal}
                onClose={closeLoginModal}
                onLogin={handleLogin}
              />

              <FeedbackModal
                isOpen={showFeedbackModal}
                onClose={closeFeedbackModal}
              />

              <ExcelUploadModal
                isOpen={showExcelUploadModal}
                onClose={closeExcelUploadModal}
              />

              <TourModal
                isOpen={showTourModal}
                onClose={closeTourModal}
              />

              {/* Toast Notifications */}
              <ToastContainer
                position="top-right"
                autoClose={5000}
                hideProgressBar={false}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
                className="mt-16"
              />
            </div>
          </UIProvider>
        </DataProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;
