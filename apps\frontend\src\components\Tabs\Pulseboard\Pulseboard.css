/* Pulseboard Main Styles */
.pulseboard {
  padding: 24px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Header */
.pulseboard-header {
  margin-bottom: 32px;
}

.header-content {
  text-align: center;
  margin-bottom: 24px;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.header-content p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
  max-width: 600px;
  margin: 0 auto;
}

/* Error Banner */
.error-banner {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #fee2e2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  color: #dc2626;
  margin-bottom: 20px;
}

.close-error {
  background: none;
  border: none;
  color: #dc2626;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  margin-left: auto;
}

.close-error:hover {
  background: #fecaca;
}

/* Header Actions */
.header-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.view-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.view-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.view-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.create-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #10b981;
  border: 1px solid #10b981;
  border-radius: 6px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.create-btn:hover {
  background: #059669;
  border-color: #059669;
}

/* Content */
.pulseboard-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Loading States */
.pulseboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.pulseboard-loading p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Excel-like Features */
.excel-features {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  color: #374151;
}

.feature-item i {
  color: #10b981;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .pulseboard {
    padding: 16px;
  }
  
  .header-content h1 {
    font-size: 28px;
  }
  
  .header-actions {
    gap: 12px;
  }
  
  .view-btn,
  .create-btn {
    padding: 10px 16px;
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .pulseboard {
    padding: 12px;
  }
  
  .header-content h1 {
    font-size: 24px;
    flex-direction: column;
    gap: 8px;
  }
  
  .header-content p {
    font-size: 14px;
  }
  
  .header-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .view-btn,
  .create-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
  
  .excel-features {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .header-content h1 {
    font-size: 20px;
  }
  
  .error-banner {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
  
  .close-error {
    margin-left: 0;
  }
}

/* Additional Excel-like Grid Enhancements */
.grid-cell-category,
.grid-cell-department,
.grid-cell-business-unit {
  font-size: 12px;
  color: #374151;
  background: #f9fafb;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.grid-cell-cost {
  font-family: monospace;
  font-weight: 500;
  color: #059669;
  text-align: right;
}

/* Enhanced Linking Indicators */
.linked-entity-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 12px;
  font-size: 10px;
  color: #1e40af;
  margin: 1px;
}

.linked-entity-badge i {
  font-size: 8px;
}

/* Multi-select Enhancement */
.bulk-selection-info {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selection-count {
  font-weight: 500;
  color: #1e40af;
}

.bulk-actions-quick {
  display: flex;
  gap: 8px;
}

.bulk-action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.bulk-action-btn.export {
  background: #f0f9ff;
  color: #0369a1;
}

.bulk-action-btn.delete {
  background: #fee2e2;
  color: #dc2626;
}

.bulk-action-btn:hover {
  opacity: 0.8;
}
