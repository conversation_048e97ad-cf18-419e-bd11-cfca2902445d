#!/bin/bash

# MasterBC - Development Startup Script
# 
# Author: <PERSON><PERSON><PERSON>
# Repository: https://github.com/mahegyaneshpandey/spm
# Date: January 27, 2025

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1  # Port is in use
    else
        return 0  # Port is available
    fi
}

# Function to kill process on port
kill_port() {
    local port=$1
    local pid=$(lsof -ti:$port)
    if [ ! -z "$pid" ]; then
        print_warning "Killing process on port $port (PID: $pid)"
        kill -9 $pid
        sleep 2
    fi
}

# Function to start backend
start_backend() {
    print_status "Starting backend server..."
    
    # Check if backend port is available
    if ! check_port 5000; then
        print_warning "Port 5000 is already in use"
        read -p "Kill existing process and continue? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            kill_port 5000
        else
            print_error "Cannot start backend on port 5000"
            return 1
        fi
    fi
    
    # Start backend in background
    cd apps/backend
    print_status "Installing backend dependencies if needed..."
    npm install --silent
    
    print_status "Starting backend on port 5000..."
    npm start &
    BACKEND_PID=$!
    cd ../..
    
    # Wait for backend to start
    print_status "Waiting for backend to start..."
    for i in {1..30}; do
        if curl -s http://localhost:5000/health >/dev/null 2>&1; then
            print_success "Backend started successfully on http://localhost:5000"
            return 0
        fi
        sleep 1
    done
    
    print_error "Backend failed to start within 30 seconds"
    return 1
}

# Function to start frontend
start_frontend() {
    print_status "Starting frontend server..."
    
    # Check if frontend port is available
    if ! check_port 3000; then
        print_warning "Port 3000 is already in use"
        read -p "Kill existing process and continue? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            kill_port 3000
        else
            print_error "Cannot start frontend on port 3000"
            return 1
        fi
    fi
    
    # Start frontend in background
    cd apps/frontend
    print_status "Installing frontend dependencies if needed..."
    npm install --silent
    
    print_status "Starting frontend on port 3000..."
    npm start &
    FRONTEND_PID=$!
    cd ../..
    
    # Wait for frontend to start
    print_status "Waiting for frontend to start..."
    for i in {1..60}; do
        if curl -s http://localhost:3000 >/dev/null 2>&1; then
            print_success "Frontend started successfully on http://localhost:3000"
            return 0
        fi
        sleep 1
    done
    
    print_error "Frontend failed to start within 60 seconds"
    return 1
}

# Function to open browser
open_browser() {
    print_status "Opening browser..."
    
    # Detect OS and open browser
    case "$(uname -s)" in
        Darwin)  # macOS
            open http://localhost:3000
            ;;
        Linux)   # Linux
            if command -v xdg-open > /dev/null; then
                xdg-open http://localhost:3000
            fi
            ;;
        CYGWIN*|MINGW32*|MSYS*|MINGW*)  # Windows
            start http://localhost:3000
            ;;
        *)
            print_warning "Could not detect OS. Please open http://localhost:3000 manually"
            ;;
    esac
}

# Function to display running info
show_running_info() {
    echo ""
    echo "🎉 MasterBC Development Environment Started!"
    echo ""
    echo "📊 Services:"
    echo "   Frontend: http://localhost:3000"
    echo "   Backend:  http://localhost:5000"
    echo ""
    echo "🔐 Demo Credentials:"
    echo "   admin/admin     - Administrator access"
    echo "   test/test       - Standard user access"
    echo "   gyanesh/gyanesh123 - Administrator access"
    echo ""
    echo "📚 Quick Links:"
    echo "   Dashboard:      http://localhost:3000/dashboard"
    echo "   Business Cases: http://localhost:3000/business-cases"
    echo "   Projects:       http://localhost:3000/projects"
    echo "   API Health:     http://localhost:5000/health"
    echo ""
    echo "🛠️  Development Commands:"
    echo "   Stop servers:   Ctrl+C"
    echo "   View logs:      Check terminal output"
    echo "   Restart:        ./scripts/start-dev.sh"
    echo ""
    echo "👨‍💻 Author: Gyanesh K Pandey"
    echo "🔗 Repository: https://github.com/mahegyaneshpandey/spm"
    echo ""
    echo "Press Ctrl+C to stop all servers..."
}

# Function to cleanup on exit
cleanup() {
    echo ""
    print_status "Shutting down development servers..."
    
    if [ ! -z "$BACKEND_PID" ]; then
        print_status "Stopping backend (PID: $BACKEND_PID)..."
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        print_status "Stopping frontend (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    # Kill any remaining processes on our ports
    kill_port 3000
    kill_port 5000
    
    print_success "Development servers stopped"
    exit 0
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ] || [ ! -d "apps/frontend" ] || [ ! -d "apps/backend" ]; then
        print_error "Please run this script from the MasterBC root directory"
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node >/dev/null 2>&1; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm >/dev/null 2>&1; then
        print_error "npm is not installed"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Main function
main() {
    echo "🚀 MasterBC Development Environment"
    echo "Author: Gyanesh K Pandey"
    echo "Repository: https://github.com/mahegyaneshpandey/spm"
    echo ""
    
    # Setup signal handlers for cleanup
    trap cleanup SIGINT SIGTERM
    
    # Check prerequisites
    check_prerequisites
    
    # Start services
    if start_backend && start_frontend; then
        show_running_info
        
        # Open browser after a short delay
        sleep 3
        open_browser
        
        # Wait for user to stop
        wait
    else
        print_error "Failed to start development environment"
        cleanup
        exit 1
    fi
}

# Run main function
main "$@"
