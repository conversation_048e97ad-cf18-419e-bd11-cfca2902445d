import React, { useState, useMemo } from 'react';
import './RelationshipMatrix.css';

const RelationshipMatrix = ({ data }) => {
  const [selectedCell, setSelectedCell] = useState(null);
  const [viewMode, setViewMode] = useState('connections'); // 'connections' or 'heatmap'

  // Process data for matrix view
  const matrixData = useMemo(() => {
    if (!data || !data.nodes || !data.edges) {
      return { matrix: [], rowLabels: [], colLabels: [], entityTypes: [] };
    }

    // Group entities by type
    const entityTypes = ['idea', 'businessCase', 'project', 'program', 'masterBusinessCase'];
    const entityGroups = {};
    
    entityTypes.forEach(type => {
      entityGroups[type] = data.nodes.filter(node => node.type === type);
    });

    // Create adjacency matrix
    const nodeMap = new Map();
    data.nodes.forEach((node, index) => {
      nodeMap.set(node.id, index);
    });

    const matrix = Array(data.nodes.length).fill(null).map(() => 
      Array(data.nodes.length).fill(0)
    );

    // Fill matrix with connections
    data.edges.forEach(edge => {
      const sourceIndex = nodeMap.get(edge.source);
      const targetIndex = nodeMap.get(edge.target);
      if (sourceIndex !== undefined && targetIndex !== undefined) {
        matrix[sourceIndex][targetIndex] = 1;
        // For undirected relationships, also set the reverse
        matrix[targetIndex][sourceIndex] = 1;
      }
    });

    return {
      matrix,
      nodes: data.nodes,
      entityGroups,
      entityTypes: entityTypes.filter(type => entityGroups[type].length > 0)
    };
  }, [data]);

  // Calculate connection statistics for each entity
  const connectionStats = useMemo(() => {
    if (!matrixData.matrix.length) return [];

    return matrixData.nodes.map((node, index) => {
      const connections = matrixData.matrix[index].reduce((sum, val) => sum + val, 0);
      return {
        node,
        connections,
        connectivityRate: matrixData.nodes.length > 1 
          ? (connections / (matrixData.nodes.length - 1) * 100).toFixed(1)
          : 0
      };
    });
  }, [matrixData]);

  const getEntityTypeIcon = (type) => {
    const icons = {
      idea: 'fas fa-lightbulb',
      businessCase: 'fas fa-briefcase',
      project: 'fas fa-project-diagram',
      program: 'fas fa-layer-group',
      masterBusinessCase: 'fas fa-crown'
    };
    return icons[type] || 'fas fa-circle';
  };

  const getEntityTypeColor = (type) => {
    const colors = {
      idea: '#fbbf24',
      businessCase: '#3b82f6',
      project: '#10b981',
      program: '#8b5cf6',
      masterBusinessCase: '#f59e0b'
    };
    return colors[type] || '#6b7280';
  };

  const getEntityTypeLabel = (type) => {
    const labels = {
      idea: 'Ideas',
      businessCase: 'Business Cases',
      project: 'Projects',
      program: 'Programs',
      masterBusinessCase: 'Master BCs'
    };
    return labels[type] || type;
  };

  const handleCellClick = (rowIndex, colIndex) => {
    if (rowIndex === colIndex) return; // Skip diagonal cells
    
    const sourceNode = matrixData.nodes[rowIndex];
    const targetNode = matrixData.nodes[colIndex];
    const hasConnection = matrixData.matrix[rowIndex][colIndex] === 1;

    setSelectedCell({
      rowIndex,
      colIndex,
      sourceNode,
      targetNode,
      hasConnection
    });
  };

  const getCellClass = (rowIndex, colIndex, value) => {
    let className = 'matrix-cell';
    
    if (rowIndex === colIndex) {
      className += ' diagonal';
    } else if (value === 1) {
      className += ' connected';
    } else {
      className += ' disconnected';
    }

    if (selectedCell && 
        ((selectedCell.rowIndex === rowIndex && selectedCell.colIndex === colIndex) ||
         (selectedCell.rowIndex === colIndex && selectedCell.colIndex === rowIndex))) {
      className += ' selected';
    }

    return className;
  };

  if (!data || !data.nodes || data.nodes.length === 0) {
    return (
      <div className="relationship-matrix">
        <div className="empty-state">
          <i className="fas fa-table"></i>
          <h3>No Matrix Data</h3>
          <p>No entities found to display in matrix view.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relationship-matrix">
      <div className="matrix-header">
        <div className="matrix-title">
          <h3>Entity Relationship Matrix</h3>
          <p>Interactive matrix showing connections between entities</p>
        </div>
        <div className="matrix-controls">
          <div className="view-mode-toggle">
            <button
              className={`toggle-btn ${viewMode === 'connections' ? 'active' : ''}`}
              onClick={() => setViewMode('connections')}
            >
              <i className="fas fa-link"></i>
              Connections
            </button>
            <button
              className={`toggle-btn ${viewMode === 'heatmap' ? 'active' : ''}`}
              onClick={() => setViewMode('heatmap')}
            >
              <i className="fas fa-fire"></i>
              Heatmap
            </button>
          </div>
        </div>
      </div>

      <div className="matrix-content">
        <div className="matrix-wrapper">
          <div className="matrix-container">
            {/* Column Headers */}
            <div className="matrix-col-headers">
              <div className="corner-cell"></div>
              {matrixData.nodes.map((node, index) => (
                <div 
                  key={`col-${index}`} 
                  className="col-header"
                  style={{ backgroundColor: getEntityTypeColor(node.type) }}
                  title={`${node.label} (${getEntityTypeLabel(node.type)})`}
                >
                  <i className={getEntityTypeIcon(node.type)}></i>
                </div>
              ))}
            </div>

            {/* Matrix Rows */}
            <div className="matrix-rows">
              {matrixData.nodes.map((rowNode, rowIndex) => (
                <div key={`row-${rowIndex}`} className="matrix-row">
                  {/* Row Header */}
                  <div 
                    className="row-header"
                    style={{ backgroundColor: getEntityTypeColor(rowNode.type) }}
                    title={`${rowNode.label} (${getEntityTypeLabel(rowNode.type)})`}
                  >
                    <i className={getEntityTypeIcon(rowNode.type)}></i>
                    <span className="row-label">{rowNode.label}</span>
                  </div>

                  {/* Matrix Cells */}
                  {matrixData.matrix[rowIndex].map((value, colIndex) => (
                    <div
                      key={`cell-${rowIndex}-${colIndex}`}
                      className={getCellClass(rowIndex, colIndex, value)}
                      onClick={() => handleCellClick(rowIndex, colIndex)}
                      title={
                        rowIndex === colIndex 
                          ? `${rowNode.label} (self)`
                          : `${rowNode.label} → ${matrixData.nodes[colIndex].label}: ${value ? 'Connected' : 'Not Connected'}`
                      }
                    >
                      {rowIndex === colIndex ? (
                        <i className={getEntityTypeIcon(rowNode.type)}></i>
                      ) : value === 1 ? (
                        <i className="fas fa-check"></i>
                      ) : (
                        <span className="empty-cell">·</span>
                      )}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Connection Details Panel */}
        {selectedCell && (
          <div className="connection-details">
            <div className="details-header">
              <h4>Connection Details</h4>
              <button
                className="close-details"
                onClick={() => setSelectedCell(null)}
              >
                <i className="fas fa-times"></i>
              </button>
            </div>

            <div className="details-content">
              <div className="connection-entities">
                <div className="entity-card">
                  <div 
                    className="entity-icon"
                    style={{ backgroundColor: getEntityTypeColor(selectedCell.sourceNode.type) }}
                  >
                    <i className={getEntityTypeIcon(selectedCell.sourceNode.type)}></i>
                  </div>
                  <div className="entity-info">
                    <div className="entity-name">{selectedCell.sourceNode.label}</div>
                    <div className="entity-type">{getEntityTypeLabel(selectedCell.sourceNode.type)}</div>
                  </div>
                </div>

                <div className="connection-indicator">
                  {selectedCell.hasConnection ? (
                    <div className="connected-indicator">
                      <i className="fas fa-link"></i>
                      <span>Connected</span>
                    </div>
                  ) : (
                    <div className="disconnected-indicator">
                      <i className="fas fa-unlink"></i>
                      <span>Not Connected</span>
                    </div>
                  )}
                </div>

                <div className="entity-card">
                  <div 
                    className="entity-icon"
                    style={{ backgroundColor: getEntityTypeColor(selectedCell.targetNode.type) }}
                  >
                    <i className={getEntityTypeIcon(selectedCell.targetNode.type)}></i>
                  </div>
                  <div className="entity-info">
                    <div className="entity-name">{selectedCell.targetNode.label}</div>
                    <div className="entity-type">{getEntityTypeLabel(selectedCell.targetNode.type)}</div>
                  </div>
                </div>
              </div>

              {selectedCell.hasConnection && (
                <div className="connection-info">
                  <div className="info-item">
                    <label>Connection Type:</label>
                    <span>Direct Relationship</span>
                  </div>
                  <div className="info-item">
                    <label>Relationship Direction:</label>
                    <span>Bidirectional</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Legend */}
      <div className="matrix-legend">
        <div className="legend-section">
          <h5>Entity Types</h5>
          <div className="legend-items">
            {matrixData.entityTypes.map(type => (
              <div key={type} className="legend-item">
                <div 
                  className="legend-icon"
                  style={{ backgroundColor: getEntityTypeColor(type) }}
                >
                  <i className={getEntityTypeIcon(type)}></i>
                </div>
                <span>{getEntityTypeLabel(type)}</span>
                <span className="count">({matrixData.entityGroups[type].length})</span>
              </div>
            ))}
          </div>
        </div>

        <div className="legend-section">
          <h5>Connection Status</h5>
          <div className="legend-items">
            <div className="legend-item">
              <div className="legend-symbol connected">
                <i className="fas fa-check"></i>
              </div>
              <span>Connected</span>
            </div>
            <div className="legend-item">
              <div className="legend-symbol disconnected">
                <span>·</span>
              </div>
              <span>Not Connected</span>
            </div>
            <div className="legend-item">
              <div className="legend-symbol diagonal">
                <i className="fas fa-circle"></i>
              </div>
              <span>Self Reference</span>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Summary */}
      <div className="matrix-stats">
        <div className="stats-grid">
          <div className="stat-item">
            <div className="stat-value">{matrixData.nodes.length}</div>
            <div className="stat-label">Total Entities</div>
          </div>
          <div className="stat-item">
            <div className="stat-value">
              {data.edges ? data.edges.length : 0}
            </div>
            <div className="stat-label">Total Connections</div>
          </div>
          <div className="stat-item">
            <div className="stat-value">
              {connectionStats.length > 0 
                ? Math.max(...connectionStats.map(s => s.connections))
                : 0
              }
            </div>
            <div className="stat-label">Max Connections</div>
          </div>
          <div className="stat-item">
            <div className="stat-value">
              {connectionStats.length > 0 
                ? (connectionStats.reduce((sum, s) => sum + s.connections, 0) / connectionStats.length / 2).toFixed(1)
                : 0
              }
            </div>
            <div className="stat-label">Avg Connections</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RelationshipMatrix;
