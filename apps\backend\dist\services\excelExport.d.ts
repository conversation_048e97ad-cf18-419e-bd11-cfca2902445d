import { IBusinessCase } from '../models/BusinessCase';
export declare class ExcelExportService {
    static exportBusinessCase(businessCase: IBusinessCase): Promise<Buffer>;
    private static createSummarySheet;
    private static createFinancialDataSheet;
    private static createCashFlowAnalysisSheet;
    private static createMetricsCalculationSheet;
    private static getExcelColumn;
    private static createPaybackFormula;
}
//# sourceMappingURL=excelExport.d.ts.map