import React from 'react';
import { useData } from '../../context/DataContext';

const DataSummaryReport = () => {
  const {
    businessCases,
    masterBusinessCases,
    projects,
    epics,
    programs,
    ideas,
    dashboardData
  } = useData();

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            📊 Enhanced Sample Data Summary
          </h1>
          <p className="text-gray-600">
            Comprehensive overview of all sample data now available in the application
          </p>
        </div>

        {/* Dashboard KPIs */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            🎯 Dashboard KPIs
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {formatCurrency(dashboardData.kpis?.totalPortfolioValue || 0)}
              </div>
              <div className="text-sm text-gray-600">Total Portfolio Value</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {dashboardData.kpis?.totalBusinessCases || 0}
              </div>
              <div className="text-sm text-gray-600">Business Cases</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {dashboardData.kpis?.avgIRR || 0}%
              </div>
              <div className="text-sm text-gray-600">Average IRR</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {dashboardData.kpis?.activeProjects || 0}
              </div>
              <div className="text-sm text-gray-600">Active Projects</div>
            </div>
          </div>
        </div>

        {/* Business Cases */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            💼 Business Cases ({businessCases.length})
          </h2>
          <div className="overflow-x-auto">
            <table className="min-w-full table-auto">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Name</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Business Unit</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Investment</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">NPV</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">IRR</th>
                </tr>
              </thead>
              <tbody>
                {businessCases.map((bc, index) => (
                  <tr key={bc.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-4 py-2 text-sm text-gray-900">{bc.name}</td>
                    <td className="px-4 py-2 text-sm text-gray-600">{bc.businessUnit}</td>
                    <td className="px-4 py-2 text-sm text-gray-900">
                      {formatCurrency(bc.totalCapex + bc.totalOpex)}
                    </td>
                    <td className="px-4 py-2 text-sm text-green-600">
                      {formatCurrency(bc.npv)}
                    </td>
                    <td className="px-4 py-2 text-sm text-blue-600">{bc.irr}%</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Master Business Cases */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            🏢 Master Business Cases ({masterBusinessCases.length})
          </h2>
          <div className="space-y-4">
            {masterBusinessCases.map((mbc) => (
              <div key={mbc.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-semibold text-gray-900">{mbc.name}</h3>
                  <span className="text-sm text-gray-500">{mbc.businessUnit}</span>
                </div>
                <p className="text-sm text-gray-600 mb-3">{mbc.description}</p>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <div className="text-sm text-gray-500">Total Investment</div>
                    <div className="font-semibold">
                      {formatCurrency(mbc.aggregatedMetrics?.totalInvestment || 0)}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Total NPV</div>
                    <div className="font-semibold text-green-600">
                      {formatCurrency(mbc.aggregatedMetrics?.totalNPV || 0)}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Avg IRR</div>
                    <div className="font-semibold text-blue-600">
                      {mbc.aggregatedMetrics?.avgIRR || 0}%
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Linked BCs</div>
                    <div className="font-semibold">
                      {mbc.linkedBusinessCases?.length || 0}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Projects and Epics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              🚀 Projects ({projects.length})
            </h2>
            <div className="space-y-3">
              {projects.map((project) => (
                <div key={project.id} className="border-l-4 border-blue-500 pl-4">
                  <h3 className="font-semibold text-gray-900">{project.name}</h3>
                  <p className="text-sm text-gray-600">{project.businessUnit}</p>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm text-gray-500">Progress: {project.progress}%</span>
                    <span className={`px-2 py-1 rounded text-xs ${
                      project.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {project.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              ⚡ Epics ({epics.length})
            </h2>
            <div className="space-y-3">
              {epics.map((epic) => (
                <div key={epic.id} className="border-l-4 border-purple-500 pl-4">
                  <h3 className="font-semibold text-gray-900">{epic.name}</h3>
                  <p className="text-sm text-gray-600">{epic.businessUnit}</p>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm text-gray-500">Progress: {epic.progress}%</span>
                    <span className={`px-2 py-1 rounded text-xs ${
                      epic.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {epic.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Programs */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            🎯 Programs ({programs.length})
          </h2>
          <div className="space-y-4">
            {programs.map((program) => (
              <div key={program.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-semibold text-gray-900">{program.name}</h3>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">{program.businessUnit}</span>
                    <span className={`px-2 py-1 rounded text-xs ${
                      program.status === 'active' ? 'bg-green-100 text-green-800' : 
                      program.status === 'planning' ? 'bg-yellow-100 text-yellow-800' : 
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {program.status}
                    </span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-3">{program.description}</p>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">
                    Master BC: {program.linkedMasterBusinessCase ? '✅ Linked' : '❌ Not Linked'}
                  </span>
                  <span className="text-sm text-gray-500">Progress: {program.progress}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Ideas */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            💡 Ideas ({ideas.length})
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {ideas.map((idea) => (
              <div key={idea.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-semibold text-gray-900">{idea.title}</h3>
                  <span className={`px-2 py-1 rounded text-xs ${
                    idea.status === 'approved' ? 'bg-green-100 text-green-800' :
                    idea.status === 'under_review' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {idea.status.replace('_', ' ')}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{idea.businessUnit}</p>
                <p className="text-sm text-gray-700 mb-3">{idea.description}</p>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-500">
                    Cost: {formatCurrency(idea.estimatedCost)}
                  </span>
                  <span className={`px-2 py-1 rounded ${
                    idea.priority === 'high' ? 'bg-red-100 text-red-800' :
                    idea.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {idea.priority} priority
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataSummaryReport;
