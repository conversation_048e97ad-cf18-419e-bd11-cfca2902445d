import React, { useState } from 'react';
import JiraIntegration from './JiraIntegration';
import ErpIntegration from './ErpIntegration';
import OtherDataSources from './OtherDataSources';
import './ThirdPartyIntegrations.css';

const ThirdPartyIntegrations = () => {
  const [activeIntegration, setActiveIntegration] = useState('jira');

  const integrations = [
    {
      id: 'jira',
      name: 'JIRA Integration',
      description: 'Sync with Atlassian JIRA for project management',
      icon: 'fab fa-jira',
      color: 'text-blue-500',
      status: 'configured',
      features: ['Epic sync', 'Status updates', 'Progress tracking', 'Bi-directional sync']
    },
    {
      id: 'erp',
      name: 'ERP Integration',
      description: 'Connect with ERP systems for financial data',
      icon: 'fas fa-chart-bar',
      color: 'text-green-500',
      status: 'available',
      features: ['Financial data', 'Resource allocation', 'Cost tracking', 'Budget sync']
    },
    {
      id: 'other',
      name: 'Other Data Sources',
      description: 'CRM, HRMS, and custom integrations',
      icon: 'fas fa-plug',
      color: 'text-purple-500',
      status: 'available',
      features: ['CRM data', 'HR systems', 'Custom APIs', 'Data warehouses']
    }
  ];

  const getStatusBadge = (status) => {
    const badges = {
      'configured': { class: 'status-configured', text: 'Configured', icon: 'fas fa-check-circle' },
      'available': { class: 'status-available', text: 'Available', icon: 'fas fa-circle' },
      'disabled': { class: 'status-disabled', text: 'Disabled', icon: 'fas fa-times-circle' }
    };
    return badges[status] || badges.available;
  };

  return (
    <div className="third-party-integrations">
      {/* Header */}
      <div className="integrations-header">
        <div className="header-content">
          <h3>Third-Party System Integrations</h3>
          <p>Connect with external systems for seamless data exchange</p>
        </div>
        <div className="header-actions">
          <button className="btn btn-outline">
            <i className="fas fa-sync-alt"></i>
            Sync All
          </button>
          <button className="btn btn-primary">
            <i className="fas fa-plus"></i>
            Add Integration
          </button>
        </div>
      </div>

      {/* Integration Navigation */}
      <div className="integration-nav">
        {integrations.map(integration => {
          const statusBadge = getStatusBadge(integration.status);
          return (
            <div
              key={integration.id}
              className={`integration-card ${activeIntegration === integration.id ? 'active' : ''}`}
              onClick={() => setActiveIntegration(integration.id)}
            >
              <div className="card-header">
                <div className="card-icon">
                  <i className={`${integration.icon} ${integration.color}`}></i>
                </div>
                <div className="card-info">
                  <h5>{integration.name}</h5>
                  <p>{integration.description}</p>
                </div>
                <div className={`status-badge ${statusBadge.class}`}>
                  <i className={statusBadge.icon}></i>
                  <span>{statusBadge.text}</span>
                </div>
              </div>
              
              <div className="card-features">
                <div className="features-list">
                  {integration.features.map(feature => (
                    <span key={feature} className="feature-tag">{feature}</span>
                  ))}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Integration Content */}
      <div className="integration-content">
        {activeIntegration === 'jira' && <JiraIntegration />}
        {activeIntegration === 'erp' && <ErpIntegration />}
        {activeIntegration === 'other' && <OtherDataSources />}
      </div>

      {/* Integration Overview */}
      <div className="integration-overview">
        <div className="overview-header">
          <h4>Integration Status Overview</h4>
        </div>
        
        <div className="overview-stats">
          <div className="stat-card">
            <i className="fas fa-plug text-blue-500"></i>
            <div>
              <span className="stat-number">3</span>
              <span className="stat-label">Available Integrations</span>
            </div>
          </div>
          
          <div className="stat-card">
            <i className="fas fa-check-circle text-green-500"></i>
            <div>
              <span className="stat-number">1</span>
              <span className="stat-label">Configured</span>
            </div>
          </div>
          
          <div className="stat-card">
            <i className="fas fa-sync-alt text-orange-500"></i>
            <div>
              <span className="stat-number">24h</span>
              <span className="stat-label">Last Sync</span>
            </div>
          </div>
          
          <div className="stat-card">
            <i className="fas fa-exchange-alt text-purple-500"></i>
            <div>
              <span className="stat-number">1,247</span>
              <span className="stat-label">Records Synced</span>
            </div>
          </div>
        </div>

        <div className="recent-activity">
          <h5>Recent Integration Activity</h5>
          <div className="activity-list">
            <div className="activity-item">
              <div className="activity-icon">
                <i className="fab fa-jira text-blue-500"></i>
              </div>
              <div className="activity-content">
                <span className="activity-title">JIRA Epic Status Updated</span>
                <span className="activity-description">Digital Transformation Epic status changed to "In Progress"</span>
                <span className="activity-time">2 hours ago</span>
              </div>
            </div>
            
            <div className="activity-item">
              <div className="activity-icon">
                <i className="fas fa-upload text-green-500"></i>
              </div>
              <div className="activity-content">
                <span className="activity-title">Data Export to ERP</span>
                <span className="activity-description">Financial data exported to ERP system successfully</span>
                <span className="activity-time">6 hours ago</span>
              </div>
            </div>
            
            <div className="activity-item">
              <div className="activity-icon">
                <i className="fas fa-sync-alt text-purple-500"></i>
              </div>
              <div className="activity-content">
                <span className="activity-title">Scheduled Sync Completed</span>
                <span className="activity-description">Daily sync with all configured systems completed</span>
                <span className="activity-time">1 day ago</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThirdPartyIntegrations;
