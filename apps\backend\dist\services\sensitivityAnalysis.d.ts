import { SensitivityAnalysis, SensitivityVariable, FinancialModel } from '../../../shared/src/types';
export declare class SensitivityAnalysisService {
    static performSensitivityAnalysis(baseModel: FinancialModel, variables: SensitivityVariable[]): SensitivityAnalysis;
    private static generateScenarios;
    private static calculateSensitivityResults;
    private static calculateImpact;
    private static calculateCorrelation;
    private static calculateElasticity;
    private static applyVariablesToModel;
    private static applyVariableToModel;
    private static getBestCaseValue;
    private static getWorstCaseValue;
    private static findVariableById;
    private static generateId;
}
//# sourceMappingURL=sensitivityAnalysis.d.ts.map