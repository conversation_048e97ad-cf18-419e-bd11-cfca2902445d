@echo off
title Master Business Case Management - Full Application Launcher
color 0E

echo ========================================
echo  Master Business Case Management
echo  Full Application Launcher
echo  (Frontend + Backend)
echo ========================================
echo.

echo [INFO] Starting Full Application (Frontend + Backend)...
echo [INFO] Frontend: http://localhost:3000
echo [INFO] Backend:  http://localhost:5000
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo [INFO] Please install Node.js from: https://nodejs.org/
    echo [INFO] Press any key to exit...
    pause >nul
    exit /b 1
)

echo [INFO] Node.js version:
node --version
echo.

REM Check if frontend directory exists
if not exist "apps\frontend\package.json" (
    echo [ERROR] Frontend directory not found
    echo [INFO] Make sure you're running this from the spm project root
    echo [INFO] Press any key to exit...
    pause >nul
    exit /b 1
)

REM Check if backend directory exists
if not exist "apps\backend\package.json" (
    echo [ERROR] Backend directory not found
    echo [INFO] Make sure you're running this from the spm project root
    echo [INFO] Press any key to exit...
    pause >nul
    exit /b 1
)

echo [INFO] Installing dependencies if needed...
echo.

REM Install frontend dependencies if needed
cd /d "%~dp0apps\frontend"
if not exist "node_modules" (
    echo [INFO] Installing frontend dependencies...
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install frontend dependencies
        pause >nul
        exit /b 1
    )
)

REM Install backend dependencies if needed
cd /d "%~dp0apps\backend"
if not exist "node_modules" (
    echo [INFO] Installing backend dependencies...
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install backend dependencies
        pause >nul
        exit /b 1
    )
)

echo [SUCCESS] All dependencies are ready!
echo.

echo [INFO] Starting Backend Server...
cd /d "%~dp0apps\backend"
start "Backend Server" cmd /k "echo Backend Server Starting... && npm start"

echo [INFO] Waiting 3 seconds for backend to initialize...
timeout /t 3 /nobreak >nul

echo [INFO] Starting Frontend Application...
cd /d "%~dp0apps\frontend"
start "Frontend Application" cmd /k "echo Frontend Application Starting... && npm start"

echo.
echo ========================================
echo  APPLICATION STARTED SUCCESSFULLY!
echo ========================================
echo.
echo Frontend URL: http://localhost:3000
echo Backend URL:  http://localhost:5000
echo.
echo Two new command windows have opened:
echo 1. Backend Server (Port 5000)
echo 2. Frontend Application (Port 3000)
echo.
echo To stop the application:
echo - Close both command windows, or
echo - Press Ctrl+C in each window
echo.
echo [INFO] Press any key to close this launcher...
pause >nul
