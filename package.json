{"name": "masterbc-workspace", "version": "1.0.0", "description": "Master Business Case Management System - Strategic Portfolio Management Suite", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/mahegyaneshpandey/spm.git"}, "homepage": "https://github.com/mahegyaneshpandey/spm", "license": "MIT", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "npm run check-ports && concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd apps/frontend && npm start", "dev:backend": "cd apps/backend && npm run dev", "check-ports": "echo 'Checking port configuration...' && node -e \"console.log('Frontend API URL:', process.env.REACT_APP_API_BASE_URL || 'http://localhost:5001'); console.log('Backend Port:', process.env.PORT || '5001');\"", "health-check": "node scripts/health-check.js", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd apps/frontend && npm run build", "build:backend": "cd apps/backend && npm run build", "start": "npm run start:backend", "start:frontend": "cd apps/frontend && npm run start", "start:backend": "cd apps/backend && npm start", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd apps/frontend && npm test", "test:backend": "cd apps/backend && npm test", "test:integration": "jest --config=tests/jest.integration.config.js", "test:e2e": "cypress run", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd apps/frontend && npm run lint", "lint:backend": "cd apps/backend && npm run lint", "lint:fix": "npm run lint:frontend -- --fix && npm run lint:backend -- --fix", "clean": "npm run clean:frontend && npm run clean:backend", "clean:frontend": "cd apps/frontend && rm -rf build node_modules", "clean:backend": "cd apps/backend && rm -rf dist node_modules", "setup": "npm install && npm run setup:frontend && npm run setup:backend", "setup:frontend": "cd apps/frontend && npm install", "setup:backend": "cd apps/backend && npm install", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:prod": "docker-compose -f docker-compose.prod.yml up", "migrate": "cd apps/backend && npm run migrate", "seed": "cd apps/backend && npm run seed"}, "devDependencies": {"concurrently": "^7.6.0", "cypress": "^12.0.0", "jest": "^29.0.0", "lerna": "^6.0.0", "prettier": "^2.8.0", "eslint": "^8.0.0", "husky": "^8.0.0", "lint-staged": "^13.0.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["business-case", "portfolio-management", "strategic-planning", "financial-modeling", "project-management", "react", "nodejs", "spm"], "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}}