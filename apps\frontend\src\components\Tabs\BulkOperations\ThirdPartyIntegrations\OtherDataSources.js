import React, { useState } from 'react';

const OtherDataSources = () => {
  const [selectedSource, setSelectedSource] = useState('crm');

  const dataSources = [
    {
      id: 'crm',
      name: 'CRM Systems',
      description: 'Customer Relationship Management integration',
      icon: 'fas fa-handshake',
      color: 'text-blue-500',
      status: 'available',
      features: ['Customer data', 'Sales pipeline', 'Revenue forecasting', 'Account management']
    },
    {
      id: 'hrms',
      name: 'HRMS Integration',
      description: 'Human Resource Management Systems',
      icon: 'fas fa-user-tie',
      color: 'text-green-500',
      status: 'available',
      features: ['Employee data', 'Resource planning', 'Cost allocation', 'Skills matrix']
    },
    {
      id: 'custom-api',
      name: 'Custom APIs',
      description: 'Custom API integrations and webhooks',
      icon: 'fas fa-code',
      color: 'text-purple-500',
      status: 'available',
      features: ['REST APIs', 'Webhooks', 'Custom endpoints', 'Data transformation']
    },
    {
      id: 'data-warehouse',
      name: 'Data Warehouses',
      description: 'Enterprise data warehouse connections',
      icon: 'fas fa-database',
      color: 'text-orange-500',
      status: 'available',
      features: ['Historical data', 'Analytics', 'Reporting', 'Data mining']
    },
    {
      id: 'cloud-storage',
      name: 'Cloud Storage',
      description: 'Cloud storage and file sharing services',
      icon: 'fas fa-cloud',
      color: 'text-teal-500',
      status: 'available',
      features: ['File storage', 'Document sync', 'Backup', 'Collaboration']
    },
    {
      id: 'bi-tools',
      name: 'BI Tools',
      description: 'Business Intelligence and analytics tools',
      icon: 'fas fa-chart-bar',
      color: 'text-red-500',
      status: 'available',
      features: ['Dashboards', 'Reports', 'Analytics', 'Visualizations']
    }
  ];

  const renderSourceOverview = (source) => (
    <div className="source-overview">
      <div className="source-header">
        <div className="source-icon">
          <i className={`${source.icon} ${source.color}`}></i>
        </div>
        <div className="source-info">
          <h5>{source.name}</h5>
          <p>{source.description}</p>
        </div>
        <div className="source-status available">
          <i className="fas fa-circle"></i>
          <span>Available</span>
        </div>
      </div>

      <div className="source-features">
        <h6>Integration Features</h6>
        <div className="features-list">
          {source.features.map(feature => (
            <div key={feature} className="feature-item">
              <i className="fas fa-check-circle text-green-500"></i>
              <span>{feature}</span>
            </div>
          ))}
        </div>
      </div>

      <div className="integration-options">
        <h6>Integration Options</h6>
        <div className="options-grid">
          <div className="option-card">
            <i className="fas fa-download text-blue-500"></i>
            <h7>Import Data</h7>
            <p>Import data from {source.name.toLowerCase()}</p>
            <button className="btn btn-outline">Configure Import</button>
          </div>
          
          <div className="option-card">
            <i className="fas fa-upload text-green-500"></i>
            <h7>Export Data</h7>
            <p>Export data to {source.name.toLowerCase()}</p>
            <button className="btn btn-outline">Configure Export</button>
          </div>
          
          <div className="option-card">
            <i className="fas fa-exchange-alt text-purple-500"></i>
            <h7>Field Mapping</h7>
            <p>Configure field mappings</p>
            <button className="btn btn-outline">Setup Mapping</button>
          </div>
          
          <div className="option-card">
            <i className="fas fa-sync-alt text-orange-500"></i>
            <h7>Bi-directional Sync</h7>
            <p>Enable automatic synchronization</p>
            <button className="btn btn-outline">Enable Sync</button>
          </div>
        </div>
      </div>

      <div className="setup-guide">
        <h6>Setup Guide</h6>
        <div className="guide-steps">
          <div className="guide-step">
            <span className="step-number">1</span>
            <div className="step-content">
              <h7>Connection Setup</h7>
              <p>Configure connection parameters and authentication</p>
            </div>
          </div>
          <div className="guide-step">
            <span className="step-number">2</span>
            <div className="step-content">
              <h7>Data Mapping</h7>
              <p>Map fields between systems for data consistency</p>
            </div>
          </div>
          <div className="guide-step">
            <span className="step-number">3</span>
            <div className="step-content">
              <h7>Test Integration</h7>
              <p>Perform test data exchange to verify configuration</p>
            </div>
          </div>
          <div className="guide-step">
            <span className="step-number">4</span>
            <div className="step-content">
              <h7>Go Live</h7>
              <p>Activate the integration for production use</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const selectedSourceData = dataSources.find(source => source.id === selectedSource);

  return (
    <div className="other-data-sources">
      {/* Source Selection */}
      <div className="source-selection">
        <h6>Select Data Source</h6>
        <div className="sources-grid">
          {dataSources.map(source => (
            <div
              key={source.id}
              className={`source-card ${selectedSource === source.id ? 'selected' : ''}`}
              onClick={() => setSelectedSource(source.id)}
            >
              <div className="card-icon">
                <i className={`${source.icon} ${source.color}`}></i>
              </div>
              <div className="card-content">
                <h7>{source.name}</h7>
                <p>{source.description}</p>
              </div>
              <div className="card-status">
                <span className="status-badge available">Available</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Selected Source Details */}
      {selectedSourceData && (
        <div className="source-details">
          {renderSourceOverview(selectedSourceData)}
        </div>
      )}

      {/* Integration Templates */}
      <div className="integration-templates">
        <h6>Integration Templates</h6>
        <p>Pre-configured templates for common integration scenarios</p>
        
        <div className="templates-grid">
          <div className="template-card">
            <i className="fas fa-file-alt text-blue-500"></i>
            <h7>Standard Data Exchange</h7>
            <p>Basic import/export configuration template</p>
            <button className="btn btn-outline">Use Template</button>
          </div>
          
          <div className="template-card">
            <i className="fas fa-sync-alt text-green-500"></i>
            <h7>Real-time Sync</h7>
            <p>Bi-directional real-time synchronization template</p>
            <button className="btn btn-outline">Use Template</button>
          </div>
          
          <div className="template-card">
            <i className="fas fa-clock text-purple-500"></i>
            <h7>Scheduled Batch</h7>
            <p>Scheduled batch processing template</p>
            <button className="btn btn-outline">Use Template</button>
          </div>
          
          <div className="template-card">
            <i className="fas fa-cog text-orange-500"></i>
            <h7>Custom Integration</h7>
            <p>Build custom integration from scratch</p>
            <button className="btn btn-outline">Create Custom</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OtherDataSources;
