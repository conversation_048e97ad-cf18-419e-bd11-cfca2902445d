import React, { useState } from 'react';
import './MasterBCLinkModal.css';

const MasterBCLinkModal = ({ 
  isOpen, 
  program, 
  masterBusinessCases, 
  onLink, 
  onClose 
}) => {
  const [selectedMasterBC, setSelectedMasterBC] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (!isOpen) return null;

  // Filter available Master BCs (exclude already linked ones)
  const availableMasterBCs = masterBusinessCases.filter(mbc => 
    !mbc.linkedPrograms || mbc.linkedPrograms.length === 0
  );

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!selectedMasterBC) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      await onLink(program.id, selectedMasterBC);
      onClose();
    } catch (error) {
      console.error('Error linking Master BC:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount || 0);
  };

  const selectedMBC = masterBusinessCases.find(mbc => mbc.id === selectedMasterBC);

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Link Program to Master Business Case</h3>
          <button className="modal-close" onClick={onClose}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        <div className="modal-body">
          <div className="program-info">
            <h4>Program Details</h4>
            <div className="info-grid">
              <div className="info-item">
                <label>Name:</label>
                <span>{program.name}</span>
              </div>
              <div className="info-item">
                <label>Owner:</label>
                <span>{program.owner}</span>
              </div>
              <div className="info-item">
                <label>Status:</label>
                <span className={`status-badge status-${program.status?.toLowerCase()}`}>
                  {program.status}
                </span>
              </div>
              <div className="info-item">
                <label>Budget:</label>
                <span>{formatCurrency(program.budget)}</span>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="masterBC">Select Master Business Case</label>
              <select
                id="masterBC"
                value={selectedMasterBC}
                onChange={(e) => setSelectedMasterBC(e.target.value)}
                className="form-select"
                required
              >
                <option value="">Choose a Master Business Case...</option>
                {availableMasterBCs.map(mbc => (
                  <option key={mbc.id} value={mbc.id}>
                    {mbc.name}
                  </option>
                ))}
              </select>
              
              {availableMasterBCs.length === 0 && (
                <div className="no-available-message">
                  <i className="fas fa-info-circle"></i>
                  No Master Business Cases available for linking. All existing Master BCs are already linked to other programs.
                </div>
              )}
            </div>

            {selectedMBC && (
              <div className="selected-mbc-preview">
                <h4>Selected Master Business Case</h4>
                <div className="mbc-card">
                  <div className="mbc-header">
                    <h5>{selectedMBC.name}</h5>
                    <span className={`status-badge status-${selectedMBC.status?.toLowerCase()}`}>
                      {selectedMBC.status}
                    </span>
                  </div>
                  <p className="mbc-description">{selectedMBC.description}</p>
                  
                  {selectedMBC.aggregatedMetrics && (
                    <div className="mbc-metrics">
                      <div className="metric">
                        <label>Total Investment:</label>
                        <span>{formatCurrency(selectedMBC.aggregatedMetrics.totalInvestment)}</span>
                      </div>
                      <div className="metric">
                        <label>NPV:</label>
                        <span>{formatCurrency(selectedMBC.aggregatedMetrics.totalNPV)}</span>
                      </div>
                      <div className="metric">
                        <label>Linked BCs:</label>
                        <span>{selectedMBC.aggregatedMetrics.linkedCount || 0}</span>
                      </div>
                      <div className="metric">
                        <label>Avg IRR:</label>
                        <span>{selectedMBC.aggregatedMetrics.avgIRR?.toFixed(1) || 0}%</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            <div className="linking-info">
              <div className="info-box">
                <i className="fas fa-info-circle"></i>
                <div>
                  <strong>Linking Rules:</strong>
                  <ul>
                    <li>Each Program can be linked to only one Master Business Case</li>
                    <li>Each Master Business Case can be linked to only one Program</li>
                    <li>This creates a one-to-one relationship for strategic alignment</li>
                  </ul>
                </div>
              </div>
            </div>
          </form>
        </div>

        <div className="modal-footer">
          <button
            type="button"
            onClick={onClose}
            className="btn btn-secondary"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            onClick={handleSubmit}
            className="btn btn-primary"
            disabled={!selectedMasterBC || isSubmitting || availableMasterBCs.length === 0}
          >
            {isSubmitting ? (
              <>
                <i className="fas fa-spinner fa-spin"></i>
                Linking...
              </>
            ) : (
              <>
                <i className="fas fa-link"></i>
                Link to Master BC
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default MasterBCLinkModal;
