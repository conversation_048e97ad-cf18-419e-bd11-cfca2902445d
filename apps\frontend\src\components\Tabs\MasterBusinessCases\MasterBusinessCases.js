import React, { useState, useEffect } from 'react';
import { useData } from '../../../context/DataContext';
import MasterBCList from './MasterBCList/MasterBCList';
import MasterBCForm from './MasterBCForm/MasterBCForm';
import MasterBCDetail from './MasterBCDetail/MasterBCDetail';
import LinkingModal from './LinkingModal/LinkingModal';
import './MasterBusinessCases.css';

const MasterBusinessCases = () => {
  const {
    masterBusinessCases,
    businessCases,
    programs,
    loading,
    errors,
    fetchMasterBusinessCases,
    fetchBusinessCases,
    fetchPrograms,
    createMasterBusinessCase,
    linkBusinessCasesToMasterBC,
    linkProgramToMasterBC
  } = useData();

  const [activeView, setActiveView] = useState('list'); // 'list', 'create', 'edit', 'detail'
  const [selectedMasterBC, setSelectedMasterBC] = useState(null);
  const [showLinkingModal, setShowLinkingModal] = useState(false);
  const [linkingType, setLinkingType] = useState(''); // 'business-cases' or 'programs'

  useEffect(() => {
    // Fetch all required data
    console.log('🔄 MasterBusinessCases: Loading data...');
    console.log('📊 Current masterBusinessCases:', masterBusinessCases);
    fetchMasterBusinessCases();
    fetchBusinessCases();
    fetchPrograms();
  }, [fetchMasterBusinessCases, fetchBusinessCases, fetchPrograms]);

  const handleCreateNew = () => {
    setSelectedMasterBC(null);
    setActiveView('create');
  };

  const handleEdit = (masterBC) => {
    setSelectedMasterBC(masterBC);
    setActiveView('edit');
  };

  const handleViewDetail = (masterBC) => {
    setSelectedMasterBC(masterBC);
    setActiveView('detail');
  };

  const handleBackToList = () => {
    setActiveView('list');
    setSelectedMasterBC(null);
  };

  const handleSave = async (masterBCData) => {
    try {
      if (activeView === 'create') {
        await createMasterBusinessCase(masterBCData);
      } else if (activeView === 'edit') {
        // Update functionality would be implemented here
        console.log('Update Master BC:', masterBCData);
      }
      setActiveView('list');
      setSelectedMasterBC(null);
    } catch (error) {
      console.error('Error saving Master BC:', error);
    }
  };

  const handleLinkBusinessCases = (masterBC) => {
    setSelectedMasterBC(masterBC);
    setLinkingType('business-cases');
    setShowLinkingModal(true);
  };

  const handleLinkPrograms = (masterBC) => {
    setSelectedMasterBC(masterBC);
    setLinkingType('programs');
    setShowLinkingModal(true);
  };

  const handleConfirmLinking = async (selectedIds) => {
    try {
      if (linkingType === 'business-cases') {
        await linkBusinessCasesToMasterBC(selectedMasterBC.id, selectedIds);
      } else if (linkingType === 'programs') {
        // For programs, we link each selected program to the Master BC
        // Note: Only one program should be selected due to one-to-one relationship
        if (selectedIds.length > 0) {
          await linkProgramToMasterBC(selectedIds[0], selectedMasterBC.id);
        }
      }
      setShowLinkingModal(false);
      setSelectedMasterBC(null);
      setLinkingType('');
      // Refresh data
      fetchMasterBusinessCases();
      fetchPrograms();
    } catch (error) {
      console.error('Error linking:', error);
    }
  };

  if (loading.masterBusinessCases) {
    return (
      <div className="master-business-cases">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading Master Business Cases...</p>
        </div>
      </div>
    );
  }

  if (errors.masterBusinessCases) {
    return (
      <div className="master-business-cases">
        <div className="error-container">
          <h3>Error Loading Data</h3>
          <p>{errors.masterBusinessCases}</p>
          <button onClick={() => window.location.reload()}>Retry</button>
        </div>
      </div>
    );
  }

  return (
    <div className="master-business-cases">
      <div className="master-bc-header">
        <h2>Master Business Cases Management</h2>
        <p className="master-bc-subtitle">
          Manage Master Business Cases and their relationships with Business Cases and Programs
        </p>
      </div>

      {activeView === 'list' && (
        <MasterBCList
          masterBusinessCases={masterBusinessCases}
          onCreateNew={handleCreateNew}
          onEdit={handleEdit}
          onViewDetail={handleViewDetail}
          onLinkBusinessCases={handleLinkBusinessCases}
          onLinkPrograms={handleLinkPrograms}
        />
      )}

      {(activeView === 'create' || activeView === 'edit') && (
        <MasterBCForm
          onSave={handleSave}
          onCancel={handleBackToList}
          initialData={activeView === 'edit' ? selectedMasterBC : null}
        />
      )}

      {activeView === 'detail' && selectedMasterBC && (
        <MasterBCDetail
          masterBC={selectedMasterBC}
          businessCases={businessCases}
          programs={programs}
          onBack={handleBackToList}
          onEdit={() => handleEdit(selectedMasterBC)}
          onLinkBusinessCases={() => handleLinkBusinessCases(selectedMasterBC)}
          onLinkPrograms={() => handleLinkPrograms(selectedMasterBC)}
        />
      )}

      {/* Linking Modal */}
      <LinkingModal
        isOpen={showLinkingModal}
        onClose={() => setShowLinkingModal(false)}
        onConfirm={handleConfirmLinking}
        masterBC={selectedMasterBC}
        linkingType={linkingType}
        businessCases={businessCases}
        programs={programs}
      />
    </div>
  );
};

export default MasterBusinessCases;
