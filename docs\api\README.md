# MasterBC API Documentation

**Author: <PERSON><PERSON><PERSON>dey**  
**Repository: https://github.com/mahegyaneshpandey/spm**  
**Date: January 27, 2025**

## 🎯 **Overview**

The MasterBC API provides RESTful endpoints for managing business cases, projects, programs, and strategic portfolio data. Built with Node.js and Express, it offers comprehensive CRUD operations with robust validation and error handling.

## 🔗 **Base URL**

```
Development: http://localhost:5000
Production:  https://api.masterbc.com
```

## 🔐 **Authentication**

### **Demo Credentials**
```json
{
  "admin": { "email": "admin", "password": "admin", "role": "admin" },
  "test": { "email": "test", "password": "test", "role": "user" },
  "gyanesh": { "email": "gyanesh", "password": "gyanesh123", "role": "admin" }
}
```

### **Login Endpoint**
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "admin",
  "password": "admin"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "jwt-token-here",
    "user": {
      "id": "user-id",
      "email": "admin",
      "role": "admin",
      "name": "Administrator"
    }
  }
}
```

## 📊 **Business Cases API**

### **Get All Business Cases**
```http
GET /api/business-cases
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "businessCases": [
      {
        "id": "bc-1",
        "name": "Digital Transformation Initiative",
        "description": "Company-wide digital transformation",
        "capex": 500000,
        "opex": 100000,
        "timeframe": {
          "startYear": 2025,
          "endYear": 2027
        },
        "financialMetrics": {
          "irr": 15.5,
          "npv": 250000,
          "paybackPeriod": 2.5,
          "grossMargin": 35.0,
          "commercialMargin": 28.0
        },
        "status": "active",
        "businessUnit": "IT",
        "createdAt": "2025-01-27T00:00:00Z",
        "updatedAt": "2025-01-27T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 1,
      "pages": 1
    }
  }
}
```

### **Create Business Case**
```http
POST /api/business-cases
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "New Business Case",
  "description": "Description of the business case",
  "capex": 100000,
  "opex": 20000,
  "timeframe": {
    "startYear": 2025,
    "endYear": 2026
  },
  "businessUnit": "Operations"
}
```

### **Update Business Case**
```http
PUT /api/business-cases/{id}
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "Updated Business Case Name",
  "status": "approved"
}
```

### **Delete Business Case**
```http
DELETE /api/business-cases/{id}
Authorization: Bearer {token}
```

## 🏢 **Master Business Cases API**

### **Get All Master Business Cases**
```http
GET /api/master-bc
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "masterBusinessCases": [
      {
        "id": "mbc-1",
        "name": "Strategic Initiative 2025",
        "description": "Comprehensive strategic initiative",
        "linkedBusinessCases": ["bc-1", "bc-2"],
        "aggregatedMetrics": {
          "totalCapex": 750000,
          "totalOpex": 150000,
          "averageIRR": 16.2,
          "totalNPV": 400000,
          "averagePaybackPeriod": 2.3
        },
        "status": "active",
        "createdAt": "2025-01-27T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 1,
      "pages": 1
    }
  }
}
```

### **Create Master Business Case**
```http
POST /api/master-bc
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "New Master Business Case",
  "description": "Strategic portfolio initiative",
  "linkedBusinessCases": ["bc-1", "bc-2"]
}
```

### **Link Business Cases**
```http
PUT /api/master-bc/{id}/link
Content-Type: application/json
Authorization: Bearer {token}

{
  "businessCaseIds": ["bc-1", "bc-2", "bc-3"]
}
```

## 🎯 **Projects & Epics API**

### **Get All Projects**
```http
GET /api/projects
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "projects": [
      {
        "id": "proj-1",
        "name": "Customer Portal Development",
        "type": "project",
        "description": "New customer-facing portal",
        "startDate": "2025-02-01",
        "endDate": "2025-08-31",
        "owner": "John Doe",
        "status": "active",
        "linkedBusinessCases": ["bc-1"],
        "linkedProgram": "prog-1",
        "milestones": [
          {
            "id": "ms-1",
            "name": "Requirements Complete",
            "dueDate": "2025-03-15",
            "status": "completed"
          }
        ]
      }
    ],
    "epics": [
      {
        "id": "epic-1",
        "name": "User Authentication Epic",
        "type": "epic",
        "description": "Complete user authentication system",
        "startDate": "2025-02-01",
        "endDate": "2025-04-30",
        "owner": "Jane Smith",
        "status": "active",
        "linkedBusinessCases": ["bc-1"],
        "linkedProgram": "prog-1"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 2,
      "pages": 1
    }
  }
}
```

### **Create Project/Epic**
```http
POST /api/projects
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "New Project",
  "type": "project",
  "description": "Project description",
  "startDate": "2025-02-01",
  "endDate": "2025-12-31",
  "owner": "Project Manager",
  "linkedBusinessCases": ["bc-1"],
  "linkedProgram": "prog-1",
  "milestones": [
    {
      "name": "Phase 1 Complete",
      "dueDate": "2025-06-30",
      "description": "First phase milestone"
    }
  ]
}
```

## 🏛️ **Programs API**

### **Get All Programs**
```http
GET /api/programs
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "programs": [
      {
        "id": "prog-1",
        "name": "Digital Transformation Program",
        "description": "Comprehensive digital transformation",
        "owner": "Program Director",
        "linkedMasterBC": "mbc-1",
        "linkedProjects": ["proj-1", "proj-2"],
        "status": "active",
        "startDate": "2025-01-01",
        "endDate": "2025-12-31"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 1,
      "pages": 1
    }
  }
}
```

### **Create Program**
```http
POST /api/programs
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "New Program",
  "description": "Program description",
  "owner": "Program Manager",
  "linkedMasterBC": "mbc-1",
  "startDate": "2025-01-01",
  "endDate": "2025-12-31"
}
```

## 💡 **Ideas API**

### **Get All Ideas**
```http
GET /api/ideas
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "ideas": [
      {
        "id": "idea-1",
        "title": "AI-Powered Analytics",
        "description": "Implement AI for business analytics",
        "submittedBy": "John Doe",
        "priority": "high",
        "status": "under-review",
        "businessUnit": "IT",
        "estimatedValue": 500000,
        "submittedAt": "2025-01-27T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 1,
      "pages": 1
    }
  }
}
```

## 📈 **Dashboard API**

### **Get Dashboard Data**
```http
GET /api/dashboard
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "kpis": {
      "totalBusinessCases": 15,
      "activeProjects": 8,
      "totalInvestment": 2500000,
      "averageROI": 18.5
    },
    "charts": {
      "investmentByBU": [...],
      "projectTimeline": [...],
      "riskDistribution": [...]
    },
    "recentActivity": [...],
    "insights": [...]
  }
}
```

## 🔧 **Utility Endpoints**

### **Health Check**
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-27T00:00:00Z",
  "version": "1.0.0",
  "author": "Gyanesh K Pandey"
}
```

## ❌ **Error Responses**

All API endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error message description",
  "code": "ERROR_CODE",
  "timestamp": "2025-01-27T00:00:00Z"
}
```

### **Common HTTP Status Codes**
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

## 📚 **Related Documentation**

- [Authentication Guide](./authentication.md)
- [Business Cases API](./business-cases.md)
- [Projects API](./projects.md)
- [Error Handling](./error-handling.md)
