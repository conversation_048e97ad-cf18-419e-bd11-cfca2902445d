import React from 'react';
import PropTypes from 'prop-types';

const RecentActivity = ({ activities = [] }) => {
  const defaultActivities = activities.length > 0 ? activities : [
    {
      id: 1,
      type: 'business_case_created',
      title: 'New Business Case Created',
      description: 'Digital Transformation Initiative business case was created',
      user: '<PERSON>',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      icon: 'fas fa-plus-circle',
      color: 'green'
    },
    {
      id: 2,
      type: 'project_updated',
      title: 'Project Updated',
      description: 'Cloud Migration project milestones were updated',
      user: '<PERSON>',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
      icon: 'fas fa-edit',
      color: 'blue'
    },
    {
      id: 3,
      type: 'master_bc_approved',
      title: 'Master Business Case Approved',
      description: 'IT Infrastructure Master BC received final approval',
      user: '<PERSON>',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
      icon: 'fas fa-check-circle',
      color: 'purple'
    },
    {
      id: 4,
      type: 'export_completed',
      title: 'Export Completed',
      description: 'Q4 Business Cases exported to Excel successfully',
      user: '<PERSON>',
      timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
      icon: 'fas fa-file-excel',
      color: 'orange'
    },
    {
      id: 5,
      type: 'program_created',
      title: 'New Program Created',
      description: 'Digital Innovation Program was created with 5 linked projects',
      user: 'David Brown',
      timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
      icon: 'fas fa-layer-group',
      color: 'indigo'
    }
  ];

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };

  const getColorClasses = (color) => {
    const colors = {
      green: 'text-green-600 bg-green-100',
      blue: 'text-blue-600 bg-blue-100',
      purple: 'text-purple-600 bg-purple-100',
      orange: 'text-orange-600 bg-orange-100',
      indigo: 'text-indigo-600 bg-indigo-100',
      red: 'text-red-600 bg-red-100'
    };
    return colors[color] || colors.blue;
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          <i className="fas fa-clock mr-2 text-gray-600"></i>
          Recent Activity
        </h3>
        <button className="text-sm text-blue-600 hover:text-blue-800">
          View All
        </button>
      </div>

      <div className="space-y-4">
        {defaultActivities.map((activity) => {
          const colorClasses = getColorClasses(activity.color);
          return (
            <div key={activity.id} className="flex items-start space-x-4 p-3 hover:bg-gray-50 rounded-lg transition-colors">
              <div className={`p-2 rounded-full ${colorClasses}`}>
                <i className={`${activity.icon} text-sm`}></i>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                <div className="flex items-center mt-2 text-xs text-gray-500">
                  <span>{activity.user}</span>
                  <span className="mx-2">•</span>
                  <span>{formatTimeAgo(activity.timestamp)}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {defaultActivities.length === 0 && (
        <div className="text-center py-8">
          <i className="fas fa-clock text-4xl text-gray-300 mb-4"></i>
          <p className="text-gray-600">No recent activity</p>
        </div>
      )}
    </div>
  );
};

RecentActivity.propTypes = {
  activities: PropTypes.array
};

export default RecentActivity;
