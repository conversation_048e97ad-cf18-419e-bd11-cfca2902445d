import React from 'react';
import PropTypes from 'prop-types';

// Import tab components
import ExecutiveDashboard from '../Tabs/ExecutiveDashboard/ExecutiveDashboard';
import IdeaCreation from '../Tabs/IdeaCreation/IdeaCreation';
import IdeaApproval from '../Tabs/IdeaApproval/IdeaApproval';
import Pulseboard from '../Tabs/Pulseboard/Pulseboard';
import CreateBusinessCase from '../Tabs/CreateBusinessCase/CreateBusinessCase';
import BusinessCasesList from '../Tabs/BusinessCasesList/BusinessCasesList';
import MasterBusinessCases from '../Tabs/MasterBusinessCases/MasterBusinessCases';
import ProjectsEpics from '../Tabs/ProjectsEpics/ProjectsEpics';
import Programs from '../Tabs/Programs/Programs';
import RelationshipsDashboard from '../Tabs/RelationshipsDashboard/RelationshipsDashboard';
import BulkOperations from '../Tabs/BulkOperations/BulkOperations';
import BusinessArchitecture from '../Tabs/BusinessArchitecture/BusinessArchitecture';
import DataLoadingStatus from '../Debug/DataLoadingStatus';

const TabContent = ({ activeTab, tabs }) => {
  const renderTabContent = () => {
    switch (activeTab) {
      case 'executive-dashboard':
        return <ExecutiveDashboard />;
      case 'idea-creation':
        return <IdeaCreation />;
      case 'idea-approval':
        return <IdeaApproval />;
      case 'pulseboard':
        return <Pulseboard />;
      case 'create-bc':
        return <CreateBusinessCase />;
      case 'list-bc':
        return <BusinessCasesList />;
      case 'master-bc':
        return <MasterBusinessCases />;
      case 'projects-epics':
        return <ProjectsEpics />;
      case 'programs':
        return <Programs />;
      case 'relationships':
        return <RelationshipsDashboard />;
      case 'bulk-operations':
        return <BulkOperations />;
      case 'business-architecture':
        return <BusinessArchitecture />;
      case 'debug-status':
        return <DataLoadingStatus />;
      default:
        return (
          <div className="text-center py-12">
            <i className="fas fa-exclamation-triangle text-yellow-500 text-4xl mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Tab Not Found</h3>
            <p className="text-gray-600">The requested tab "{activeTab}" could not be found.</p>
          </div>
        );
    }
  };

  return (
    <div className="tab-content-container">
      {renderTabContent()}
    </div>
  );
};

TabContent.propTypes = {
  activeTab: PropTypes.string.isRequired,
  tabs: PropTypes.array.isRequired,
};

export default TabContent;
