import mongoose, { Document } from 'mongoose';
export interface IBusinessCase extends Document {
    name: string;
    description?: string;
    tags?: string[];
    businessUnit?: string;
    timeframe: {
        startYear: number;
        endYear: number;
    };
    financialData: {
        capex: Array<{
            year: number;
            amount: number;
            description?: string;
        }>;
        opex: Array<{
            year: number;
            amount: number;
            description?: string;
        }>;
        revenue?: Array<{
            year: number;
            amount: number;
            description?: string;
        }>;
        totalCapex: number;
        totalOpex: number;
    };
    calculatedMetrics?: {
        irr?: number;
        npv?: number;
        paybackPeriod?: number;
        grossMargin?: number;
        commercialMargin?: number;
    };
    status: 'draft' | 'active' | 'completed' | 'archived';
    createdBy: string;
    lastModifiedBy: string;
    createdAt: Date;
    updatedAt: Date;
}
export declare const BusinessCase: mongoose.Model<IBusinessCase, {}, {}, {}, mongoose.Document<unknown, {}, IBusinessCase> & IBusinessCase & {
    _id: mongoose.Types.ObjectId;
}, any>;
//# sourceMappingURL=BusinessCase.d.ts.map