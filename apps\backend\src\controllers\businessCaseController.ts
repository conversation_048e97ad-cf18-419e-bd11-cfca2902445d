import { Request, Response } from 'express';
import { BusinessCase, IBusinessCase } from '../models/BusinessCase';
import { FinancialCalculationService } from '../services/financialCalculations';
import { ExcelExportService } from '../services/excelExport';

interface AuthRequest extends Request {
  user?: {
    id: string;
    email: string;
    name: string;
    role: string;
  };
}

// @desc    Get all business cases
// @route   GET /api/business-cases
// @access  Private
export const getBusinessCases = async (req: AuthRequest, res: Response) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      status,
      startYear,
      endYear,
      createdBy
    } = req.query;

    const query: any = {};

    // Search by name, description, or tags
    if (search) {
      query.$text = { $search: search as string };
    }

    // Filter by status
    if (status) {
      query.status = status;
    }

    // Filter by timeframe
    if (startYear || endYear) {
      query['timeframe.startYear'] = {};
      if (startYear) query['timeframe.startYear'].$gte = parseInt(startYear as string);
      if (endYear) query['timeframe.endYear'] = { $lte: parseInt(endYear as string) };
    }

    // Filter by creator
    if (createdBy) {
      query.createdBy = createdBy;
    }

    const options = {
      page: parseInt(page as string),
      limit: parseInt(limit as string),
      sort: { updatedAt: -1 },
      populate: []
    };

    const businessCases = await BusinessCase.find(query)
      .sort(options.sort)
      .limit(options.limit * options.page)
      .skip((options.page - 1) * options.limit)
      .exec();

    const total = await BusinessCase.countDocuments(query);

    res.json({
      success: true,
      data: {
        businessCases,
        pagination: {
          page: options.page,
          limit: options.limit,
          total,
          pages: Math.ceil(total / options.limit)
        }
      }
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// @desc    Get single business case
// @route   GET /api/business-cases/:id
// @access  Private
export const getBusinessCase = async (req: Request, res: Response) => {
  try {
    const businessCase = await BusinessCase.findById(req.params.id);

    if (!businessCase) {
      return res.status(404).json({
        success: false,
        error: 'Business case not found'
      });
    }

    res.json({
      success: true,
      data: businessCase
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// @desc    Create business case
// @route   POST /api/business-cases
// @access  Private
export const createBusinessCase = async (req: AuthRequest, res: Response) => {
  try {
    const {
      name,
      description,
      tags,
      businessUnit,
      timeframe,
      financialData
    } = req.body;

    // Calculate financial metrics
    const calculatedMetrics = FinancialCalculationService.calculateBusinessCaseMetrics({
      timeframe,
      financialData
    });

    const businessCase = await BusinessCase.create({
      name,
      description,
      tags,
      businessUnit,
      timeframe,
      financialData,
      calculatedMetrics,
      createdBy: req.user?.email || 'unknown',
      lastModifiedBy: req.user?.email || 'unknown'
    });

    res.status(201).json({
      success: true,
      data: businessCase
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
};

// @desc    Update business case
// @route   PUT /api/business-cases/:id
// @access  Private
export const updateBusinessCase = async (req: AuthRequest, res: Response) => {
  try {
    const businessCase = await BusinessCase.findById(req.params.id);

    if (!businessCase) {
      return res.status(404).json({
        success: false,
        error: 'Business case not found'
      });
    }

    const {
      name,
      description,
      tags,
      businessUnit,
      timeframe,
      financialData,
      status
    } = req.body;

    // Recalculate financial metrics if financial data changed
    let calculatedMetrics = businessCase.calculatedMetrics;
    if (financialData || timeframe) {
      calculatedMetrics = FinancialCalculationService.calculateBusinessCaseMetrics({
        timeframe: timeframe || businessCase.timeframe,
        financialData: financialData || businessCase.financialData
      });
    }

    const updatedBusinessCase = await BusinessCase.findByIdAndUpdate(
      req.params.id,
      {
        name,
        description,
        tags,
        businessUnit,
        timeframe,
        financialData,
        status,
        calculatedMetrics,
        lastModifiedBy: req.user?.email || 'unknown'
      },
      {
        new: true,
        runValidators: true
      }
    );

    res.json({
      success: true,
      data: updatedBusinessCase
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
};

// @desc    Delete business case
// @route   DELETE /api/business-cases/:id
// @access  Private
export const deleteBusinessCase = async (req: Request, res: Response) => {
  try {
    const businessCase = await BusinessCase.findById(req.params.id);

    if (!businessCase) {
      return res.status(404).json({
        success: false,
        error: 'Business case not found'
      });
    }

    await BusinessCase.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      data: {}
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// @desc    Export business case to Excel
// @route   GET /api/business-cases/:id/export
// @access  Private
export const exportBusinessCase = async (req: Request, res: Response) => {
  try {
    const businessCase = await BusinessCase.findById(req.params.id);

    if (!businessCase) {
      return res.status(404).json({
        success: false,
        error: 'Business case not found'
      });
    }

    const excelBuffer = await ExcelExportService.exportBusinessCase(businessCase);

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${businessCase.name}_financial_analysis.xlsx"`);
    
    res.send(excelBuffer);
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// @desc    Get business case statistics
// @route   GET /api/business-cases/stats
// @access  Private
export const getBusinessCaseStats = async (req: AuthRequest, res: Response) => {
  try {
    const stats = await BusinessCase.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalCapex: { $sum: '$financialData.totalCapex' },
          totalOpex: { $sum: '$financialData.totalOpex' },
          avgIRR: { $avg: '$calculatedMetrics.irr' },
          avgNPV: { $avg: '$calculatedMetrics.npv' }
        }
      }
    ]);

    const totalStats = await BusinessCase.aggregate([
      {
        $group: {
          _id: null,
          totalBusinessCases: { $sum: 1 },
          totalInvestment: { $sum: { $add: ['$financialData.totalCapex', '$financialData.totalOpex'] } },
          avgPaybackPeriod: { $avg: '$calculatedMetrics.paybackPeriod' }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        byStatus: stats,
        overall: totalStats[0] || {}
      }
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};
