/* Excel-like Grid Styles */
.pulseboard-grid {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Toolbar */
.grid-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filter-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.filter-toggle:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.bulk-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
}

.selected-count {
  font-size: 14px;
  font-weight: 500;
  color: #1e40af;
}

.bulk-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.bulk-btn.delete {
  background: #fee2e2;
  color: #dc2626;
}

.bulk-btn.delete:hover {
  background: #fecaca;
}

.bulk-btn.export {
  background: #f0f9ff;
  color: #0369a1;
}

.bulk-btn.export:hover {
  background: #e0f2fe;
}

.grid-stats {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #6b7280;
}

/* Filters */
.grid-filters {
  padding: 16px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.filter-row {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-input,
.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.filter-input {
  min-width: 200px;
}

.filter-select {
  min-width: 120px;
}

.clear-filters {
  padding: 8px 16px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-filters:hover {
  background: #e5e7eb;
}

/* Excel Grid Table */
.grid-container {
  overflow: auto;
  max-height: calc(100vh - 300px);
}

.excel-grid {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 14px;
}

.excel-grid th {
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-top: none;
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  position: sticky;
  top: 0;
  z-index: 10;
}

.excel-grid th.sortable {
  cursor: pointer;
  user-select: none;
}

.excel-grid th.sortable:hover {
  background: #f3f4f6;
}

.excel-grid th i {
  margin-left: 4px;
  color: #6b7280;
}

.excel-grid td {
  border: 1px solid #e5e7eb;
  border-top: none;
  border-left: none;
  padding: 8px;
  vertical-align: middle;
  background: white;
}

.excel-grid td:first-child {
  border-left: 1px solid #e5e7eb;
}

.excel-grid tr:hover td {
  background: #f9fafb;
}

.excel-grid tr.selected td {
  background: #eff6ff;
  border-color: #bfdbfe;
}

/* Cell Types */
.cell-id {
  font-family: monospace;
  font-size: 12px;
  color: #6b7280;
}

.cell-title {
  font-weight: 500;
  color: #1f2937;
}

.cell-type {
  text-transform: capitalize;
  color: #6b7280;
}

.cell-category,
.cell-department,
.cell-business-unit {
  font-size: 12px;
  color: #374151;
  background: #f9fafb;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cell-linked {
  font-size: 12px;
  color: #6b7280;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cell-user {
  font-size: 12px;
  color: #374151;
}

.cell-date {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
}

.cell-value {
  font-weight: 500;
  color: #059669;
}

.cell-effort {
  text-align: right;
  font-family: monospace;
  color: #6b7280;
}

.cell-cost {
  font-family: monospace;
  font-weight: 500;
  color: #059669;
  text-align: right;
}

/* Status Badges */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-new { background: #f3f4f6; color: #374151; }
.status-in-progress { background: #dbeafe; color: #1e40af; }
.status-review { background: #fef3c7; color: #d97706; }
.status-approved { background: #d1fae5; color: #059669; }
.status-rejected { background: #fee2e2; color: #dc2626; }
.status-completed { background: #e0e7ff; color: #5b21b6; }
.status-on-hold { background: #fed7aa; color: #ea580c; }
.status-cancelled { background: #fee2e2; color: #dc2626; }
.status-default { background: #f3f4f6; color: #6b7280; }

/* Priority Badges */
.priority-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.priority-critical { background: #fee2e2; color: #dc2626; }
.priority-high { background: #fed7aa; color: #ea580c; }
.priority-medium { background: #fef3c7; color: #d97706; }
.priority-low { background: #d1fae5; color: #059669; }
.priority-default { background: #f3f4f6; color: #6b7280; }

/* Progress Bar */
.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 80px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 11px;
  color: #6b7280;
  min-width: 30px;
}

/* Action Buttons */
.cell-actions {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn.edit {
  background: #f0f9ff;
  color: #0369a1;
}

.action-btn.edit:hover {
  background: #e0f2fe;
}

.action-btn.delete {
  background: #fee2e2;
  color: #dc2626;
}

.action-btn.delete:hover {
  background: #fecaca;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-content i {
  font-size: 48px;
  color: #d1d5db;
}

.empty-content h3 {
  margin: 0;
  font-size: 18px;
  color: #374151;
}

.empty-content p {
  margin: 0;
  color: #6b7280;
}

/* Loading States */
.grid-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .grid-container {
    overflow-x: auto;
  }
  
  .excel-grid {
    min-width: 1600px;
  }
}

@media (max-width: 768px) {
  .grid-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-input,
  .filter-select {
    min-width: auto;
  }
}

/* Scrollbar Styling */
.grid-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.grid-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.grid-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.grid-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
