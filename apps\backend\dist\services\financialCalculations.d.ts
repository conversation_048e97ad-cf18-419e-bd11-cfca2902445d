import { FinancialResults, CashFlowItem, FinancialModel } from '../../../shared/src/types';
export declare class FinancialCalculationService {
    static calculateNPV(cashFlows: CashFlowItem[], discountRate: number): number;
    static calculateIRR(cashFlows: CashFlowItem[], initialGuess?: number): number;
    private static calculateNPVForRate;
    private static calculateNPVDerivative;
    static calculatePaybackPeriod(cashFlows: CashFlowItem[]): number;
    static calculateYieldIndex(cashFlows: CashFlowItem[], discountRate: number): number;
    static calculateGrossMargin(revenue: number, costOfGoodsSold: number): number;
    static calculateSalesAtMaturity(cashFlows: CashFlowItem[]): number;
    static calculateBreakEvenSales(fixedCosts: number, variableCostPerUnit: number, pricePerUnit: number): number;
    static generateCashFlow(model: FinancialModel, years?: number): CashFlowItem[];
    private static calculateYearlyRevenue;
    private static calculateYearlyCosts;
    static calculateFinancialResults(model: FinancialModel): FinancialResults;
    static calculateBusinessCaseMetrics(businessCaseData: {
        timeframe: {
            startYear: number;
            endYear: number;
        };
        financialData: {
            capex: Array<{
                year: number;
                amount: number;
            }>;
            opex: Array<{
                year: number;
                amount: number;
            }>;
            revenue?: Array<{
                year: number;
                amount: number;
            }>;
        };
    }): {
        irr: number;
        npv: number;
        paybackPeriod: number;
        grossMargin: number;
        commercialMargin: number;
    };
    private static calculateCommercialMargin;
}
//# sourceMappingURL=financialCalculations.d.ts.map