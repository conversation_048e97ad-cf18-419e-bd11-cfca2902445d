import { apiPost, apiGet } from './api';

class AuthService {
  constructor() {
    this.currentUser = null;
    this.authToken = null;
  }

  // Login user
  async login(credentials) {
    try {
      console.log('🔐 Attempting login with:', credentials.email);
      
      const response = await apiPost('/api/auth/login', {
        email: credentials.email,
        password: credentials.password,
      });

      if (response.success) {
        this.currentUser = response.data.user;
        this.authToken = response.data.token;
        
        // Store token in localStorage
        if (this.authToken) {
          localStorage.setItem('authToken', this.authToken);
        }
        
        console.log('✅ Login successful for:', this.currentUser.name);
        
        return {
          success: true,
          data: {
            user: this.currentUser,
            token: this.authToken,
          },
        };
      } else {
        console.error('❌ Login failed:', response.error);
        return {
          success: false,
          error: response.error || 'Login failed',
        };
      }
    } catch (error) {
      console.error('❌ Login error:', error);
      return {
        success: false,
        error: error.message || 'Login failed',
      };
    }
  }

  // Logout user
  logout() {
    this.currentUser = null;
    this.authToken = null;
    localStorage.removeItem('authToken');
    console.log('🚪 User logged out');
  }

  // Validate token
  async validateToken(token) {
    try {
      if (!token) {
        return null;
      }

      // Set token for the request
      const originalToken = this.authToken;
      this.authToken = token;

      const response = await apiGet('/api/auth/verify');
      
      if (response.success) {
        this.currentUser = response.data.user;
        this.authToken = token;
        return this.currentUser;
      } else {
        this.authToken = originalToken;
        return null;
      }
    } catch (error) {
      console.error('Token validation failed:', error);
      this.authToken = null;
      return null;
    }
  }

  // Get current user
  getCurrentUser() {
    return this.currentUser;
  }

  // Get auth token
  getAuthToken() {
    return this.authToken || localStorage.getItem('authToken');
  }

  // Check if user is authenticated
  isAuthenticated() {
    return !!this.currentUser && !!this.getAuthToken();
  }

  // Check if user has specific role
  hasRole(role) {
    return this.currentUser?.role === role;
  }

  // Check if user has any of the specified roles
  hasAnyRole(roles) {
    return roles.includes(this.currentUser?.role);
  }

  // Refresh user data
  async refreshUser() {
    try {
      const response = await apiGet('/api/auth/me');
      if (response.success) {
        this.currentUser = response.data;
        return this.currentUser;
      }
      return null;
    } catch (error) {
      console.error('Failed to refresh user data:', error);
      return null;
    }
  }

  // Change password
  async changePassword(currentPassword, newPassword) {
    try {
      const response = await apiPost('/api/auth/change-password', {
        currentPassword,
        newPassword,
      });

      return response;
    } catch (error) {
      console.error('Password change failed:', error);
      return {
        success: false,
        error: error.message || 'Password change failed',
      };
    }
  }

  // Request password reset
  async requestPasswordReset(email) {
    try {
      const response = await apiPost('/api/auth/forgot-password', { email });
      return response;
    } catch (error) {
      console.error('Password reset request failed:', error);
      return {
        success: false,
        error: error.message || 'Password reset request failed',
      };
    }
  }

  // Reset password with token
  async resetPassword(token, newPassword) {
    try {
      const response = await apiPost('/api/auth/reset-password', {
        token,
        newPassword,
      });
      return response;
    } catch (error) {
      console.error('Password reset failed:', error);
      return {
        success: false,
        error: error.message || 'Password reset failed',
      };
    }
  }

  // Update user profile
  async updateProfile(profileData) {
    try {
      const response = await apiPost('/api/auth/update-profile', profileData);

      if (response.success) {
        this.currentUser = { ...this.currentUser, ...response.data };
      }

      return response;
    } catch (error) {
      console.error('Profile update failed:', error);
      return {
        success: false,
        error: error.message || 'Profile update failed',
      };
    }
  }
}

// Create and export a singleton instance
export const authService = new AuthService();
export default authService;
