.bulk-operations {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Header */
.bulk-operations-header {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
}

.header-info p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.safety-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #0369a1;
}

/* Navigation Tabs */
.bulk-operations-nav {
  margin-bottom: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.nav-tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
}

.nav-tab {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
}

.nav-tab:hover {
  background: #f9fafb;
}

.nav-tab.active {
  background: #3b82f6;
  color: white;
}

.nav-tab i {
  font-size: 20px;
  color: #6b7280;
  min-width: 20px;
}

.nav-tab.active i {
  color: white;
}

.tab-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tab-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.nav-tab.active .tab-name {
  color: white;
}

.tab-description {
  font-size: 14px;
  color: #6b7280;
}

.nav-tab.active .tab-description {
  color: #e5e7eb;
}

/* Content Area */
.bulk-operations-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-height: 600px;
}

/* Utility Classes */
.text-green-500 {
  color: #10b981;
}

.text-blue-500 {
  color: #3b82f6;
}

.text-purple-500 {
  color: #8b5cf6;
}

.text-orange-500 {
  color: #f59e0b;
}

.text-red-500 {
  color: #ef4444;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-900 {
  color: #111827;
}

/* Responsive Design */
@media (max-width: 768px) {
  .bulk-operations {
    padding: 12px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .nav-tabs {
    flex-direction: column;
  }
  
  .nav-tab {
    border-bottom: 1px solid #e5e7eb;
  }
  
  .nav-tab:last-child {
    border-bottom: none;
  }
}
