import React, { useState } from 'react';
import ControlledImportExport from './ControlledImportExport/ControlledImportExport';
import ThirdPartyIntegrations from './ThirdPartyIntegrations/ThirdPartyIntegrations';
import AuditLogs from './AuditLogs/AuditLogs';
import './BulkOperations.css';

const BulkOperations = () => {
  const [activeTab, setActiveTab] = useState('import-export');

  const tabs = [
    {
      id: 'import-export',
      name: 'Controlled Import/Export',
      icon: 'fas fa-exchange-alt',
      description: 'Selective data import/export with validation'
    },
    {
      id: 'integrations',
      name: 'Third-Party Integrations',
      icon: 'fas fa-plug',
      description: 'JIRA, ERP, and other system integrations'
    },
    {
      id: 'audit-logs',
      name: 'Audit & Rollback',
      icon: 'fas fa-history',
      description: 'Operation logs and rollback capabilities'
    }
  ];

  return (
    <div className="bulk-operations">
      {/* Header */}
      <div className="bulk-operations-header">
        <div className="header-content">
          <div className="header-info">
            <h2>Bulk Operations Center</h2>
            <p>Controlled import/export and third-party system integrations</p>
          </div>
          <div className="header-actions">
            <div className="safety-indicator">
              <i className="fas fa-shield-alt text-green-500"></i>
              <span>Safety Controls Active</span>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bulk-operations-nav">
        <div className="nav-tabs">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`nav-tab ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => setActiveTab(tab.id)}
            >
              <i className={tab.icon}></i>
              <div className="tab-content">
                <span className="tab-name">{tab.name}</span>
                <span className="tab-description">{tab.description}</span>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="bulk-operations-content">
        {activeTab === 'import-export' && <ControlledImportExport />}
        {activeTab === 'integrations' && <ThirdPartyIntegrations />}
        {activeTab === 'audit-logs' && <AuditLogs />}
      </div>
    </div>
  );
};

export default BulkOperations;
