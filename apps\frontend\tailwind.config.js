/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./public/index.html"
  ],
  theme: {
    extend: {
      animation: {
        'tour-pulse': 'tour-pulse 2s infinite',
        'fadeInUp': 'fadeInUp 0.3s ease',
        'fadeOutDown': 'fadeOutDown 0.3s ease',
        'slideInRight': 'slideInRight 0.3s ease'
      },
      keyframes: {
        'tour-pulse': {
          '0%': { boxShadow: '0 0 0 0 rgba(59, 130, 246, 0.7)' },
          '70%': { boxShadow: '0 0 0 10px rgba(59, 130, 246, 0)' },
          '100%': { boxShadow: '0 0 0 0 rgba(59, 130, 246, 0)' }
        },
        'fadeInUp': {
          'from': {
            opacity: '0',
            transform: 'translateY(10px)'
          },
          'to': {
            opacity: '1',
            transform: 'translateY(0)'
          }
        },
        'fadeOutDown': {
          'from': {
            opacity: '1',
            transform: 'translateY(0)'
          },
          'to': {
            opacity: '0',
            transform: 'translateY(10px)'
          }
        },
        'slideInRight': {
          'from': {
            opacity: '0',
            transform: 'translateX(100%)'
          },
          'to': {
            opacity: '1',
            transform: 'translateX(0)'
          }
        }
      },
      zIndex: {
        '60': '60',
        '70': '70'
      }
    },
  },
  plugins: [],
}
