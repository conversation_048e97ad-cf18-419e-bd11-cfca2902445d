import { Request, Response } from 'express';
interface AuthRequest extends Request {
    user?: {
        id: string;
        email: string;
        name: string;
        role: string;
    };
}
export declare const getBusinessCases: (req: AuthRequest, res: Response) => Promise<void>;
export declare const getBusinessCase: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const createBusinessCase: (req: AuthRequest, res: Response) => Promise<void>;
export declare const updateBusinessCase: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deleteBusinessCase: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const exportBusinessCase: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getBusinessCaseStats: (req: AuthRequest, res: Response) => Promise<void>;
export {};
//# sourceMappingURL=businessCaseController.d.ts.map