import React from 'react';

const DataTypeSelector = ({ selectedDataType, onDataTypeChange, operation }) => {
  const dataTypes = [
    {
      id: 'opex',
      name: 'OPEX (Operating Expenses)',
      description: 'Operational expenditure data by year',
      icon: 'fas fa-chart-line',
      color: 'text-blue-500',
      fields: ['Year', 'Amount', 'Category', 'Description'],
      supportedFormats: ['Excel', 'CSV', 'JSON']
    },
    {
      id: 'capex',
      name: 'CAPEX (Capital Expenses)',
      description: 'Capital expenditure data by year',
      icon: 'fas fa-building',
      color: 'text-green-500',
      fields: ['Year', 'Amount', 'Asset Type', 'Description'],
      supportedFormats: ['Excel', 'CSV', 'JSON']
    },
    {
      id: 'revenue',
      name: 'Revenue Projections',
      description: 'Revenue forecast data by year',
      icon: 'fas fa-dollar-sign',
      color: 'text-purple-500',
      fields: ['Year', 'Amount', 'Revenue Stream', 'Description'],
      supportedFormats: ['Excel', 'CSV', 'JSON']
    },
    {
      id: 'resource-plan',
      name: 'Resource Plan',
      description: 'Human resource allocation and planning',
      icon: 'fas fa-users',
      color: 'text-orange-500',
      fields: ['Role', 'FTE', 'Start Date', 'End Date', 'Cost'],
      supportedFormats: ['Excel', 'CSV']
    },
    {
      id: 'milestones',
      name: 'Project Milestones',
      description: 'Project milestone and timeline data',
      icon: 'fas fa-flag',
      color: 'text-red-500',
      fields: ['Milestone Name', 'Due Date', 'Status', 'Owner'],
      supportedFormats: ['Excel', 'CSV', 'JSON']
    },
    {
      id: 'financial-metrics',
      name: 'Financial Metrics',
      description: 'Calculated financial metrics (IRR, NPV, etc.)',
      icon: 'fas fa-calculator',
      color: 'text-indigo-500',
      fields: ['IRR', 'NPV', 'Payback Period', 'ROI'],
      supportedFormats: ['Excel', 'JSON'],
      importOnly: operation === 'import' // Only allow import for calculated metrics
    },
    {
      id: 'business-unit-data',
      name: 'Business Unit Data',
      description: 'Business unit information and hierarchy',
      icon: 'fas fa-sitemap',
      color: 'text-teal-500',
      fields: ['BU Name', 'Manager', 'Budget', 'Location'],
      supportedFormats: ['Excel', 'CSV', 'JSON']
    },
    {
      id: 'risk-assessments',
      name: 'Risk Assessments',
      description: 'Risk analysis and mitigation data',
      icon: 'fas fa-exclamation-triangle',
      color: 'text-yellow-500',
      fields: ['Risk Category', 'Probability', 'Impact', 'Mitigation'],
      supportedFormats: ['Excel', 'CSV']
    }
  ];

  // Filter data types based on operation
  const availableDataTypes = dataTypes.filter(dataType => {
    if (operation === 'export') {
      return !dataType.importOnly;
    }
    return true;
  });

  return (
    <div className="data-type-selector">
      <div className="data-type-grid">
        {availableDataTypes.map(dataType => (
          <div
            key={dataType.id}
            className={`data-type-card ${selectedDataType === dataType.id ? 'selected' : ''}`}
            onClick={() => onDataTypeChange(dataType.id)}
          >
            <div className="card-header">
              <i className={`${dataType.icon} ${dataType.color}`}></i>
              <h5>{dataType.name}</h5>
            </div>
            <p className="card-description">{dataType.description}</p>
            
            <div className="card-details">
              <div className="detail-section">
                <span className="detail-label">Fields:</span>
                <div className="field-tags">
                  {dataType.fields.slice(0, 3).map(field => (
                    <span key={field} className="field-tag">{field}</span>
                  ))}
                  {dataType.fields.length > 3 && (
                    <span className="field-tag more">+{dataType.fields.length - 3} more</span>
                  )}
                </div>
              </div>
              
              <div className="detail-section">
                <span className="detail-label">Formats:</span>
                <div className="format-tags">
                  {dataType.supportedFormats.map(format => (
                    <span key={format} className="format-tag">{format}</span>
                  ))}
                </div>
              </div>
            </div>

            {dataType.importOnly && operation === 'import' && (
              <div className="import-only-badge">
                <i className="fas fa-info-circle"></i>
                Import Only
              </div>
            )}
          </div>
        ))}
      </div>

      {selectedDataType && (
        <div className="selected-data-type-info">
          <div className="info-header">
            <i className="fas fa-info-circle text-blue-500"></i>
            <h6>Selected Data Type Information</h6>
          </div>
          {(() => {
            const selected = dataTypes.find(dt => dt.id === selectedDataType);
            return (
              <div className="info-content">
                <p><strong>Description:</strong> {selected.description}</p>
                <p><strong>Required Fields:</strong> {selected.fields.join(', ')}</p>
                <p><strong>Supported Formats:</strong> {selected.supportedFormats.join(', ')}</p>
                {operation === 'import' && (
                  <div className="import-requirements">
                    <p><strong>Import Requirements:</strong></p>
                    <ul>
                      <li>All required fields must be present in the file</li>
                      <li>Data must be in the correct format (numbers for amounts, dates for date fields)</li>
                      <li>File size should not exceed 50MB</li>
                      <li>Maximum 10,000 rows per import</li>
                    </ul>
                  </div>
                )}
                {operation === 'export' && (
                  <div className="export-options">
                    <p><strong>Export Options:</strong></p>
                    <ul>
                      <li>Data will be exported with all available fields</li>
                      <li>Calculated fields will be included where applicable</li>
                      <li>Export format can be selected in the next step</li>
                    </ul>
                  </div>
                )}
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
};

export default DataTypeSelector;
