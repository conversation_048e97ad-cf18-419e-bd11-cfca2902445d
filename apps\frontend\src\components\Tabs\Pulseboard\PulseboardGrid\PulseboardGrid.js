import React, { useState, useMemo } from 'react';
import './PulseboardGrid.css';

const PulseboardGrid = ({
  items,
  projects,
  programs,
  businessCases,
  masterBusinessCases,
  epics,
  onEdit,
  onDelete,
  onBulkAction,
  loading
}) => {
  const [selectedItems, setSelectedItems] = useState(new Set());
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [filterConfig, setFilterConfig] = useState({
    status: '',
    priority: '',
    type: '',
    linkedEntity: '',
    search: ''
  });
  const [showFilters, setShowFilters] = useState(false);

  // Define Excel-like columns with enhanced linking
  const columns = [
    { key: 'select', label: '', width: '40px', sortable: false },
    { key: 'id', label: 'ID', width: '100px', sortable: true },
    { key: 'title', label: 'Title', width: '200px', sortable: true },
    { key: 'type', label: 'Type', width: '120px', sortable: true },
    { key: 'status', label: 'Status', width: '100px', sortable: true },
    { key: 'priority', label: 'Priority', width: '100px', sortable: true },
    { key: 'category', label: 'Category', width: '120px', sortable: true },
    { key: 'department', label: 'Department', width: '120px', sortable: true },
    { key: 'businessUnit', label: 'Business Unit', width: '120px', sortable: true },
    { key: 'linkedProject', label: 'Linked Project', width: '150px', sortable: true },
    { key: 'linkedEpic', label: 'Linked Epic', width: '150px', sortable: true },
    { key: 'linkedProgram', label: 'Linked Program', width: '150px', sortable: true },
    { key: 'linkedBusinessCase', label: 'Linked BC', width: '150px', sortable: true },
    { key: 'linkedMasterBC', label: 'Linked Master BC', width: '150px', sortable: true },
    { key: 'requestor', label: 'Requestor', width: '120px', sortable: true },
    { key: 'assignee', label: 'Assignee', width: '120px', sortable: true },
    { key: 'dueDate', label: 'Due Date', width: '120px', sortable: true },
    { key: 'progress', label: 'Progress', width: '100px', sortable: true },
    { key: 'businessValue', label: 'Business Value', width: '120px', sortable: true },
    { key: 'effort', label: 'Effort (Hours)', width: '100px', sortable: true },
    { key: 'estimatedCost', label: 'Est. Cost', width: '100px', sortable: true },
    { key: 'createdAt', label: 'Created', width: '120px', sortable: true },
    { key: 'updatedAt', label: 'Updated', width: '120px', sortable: true },
    { key: 'actions', label: 'Actions', width: '100px', sortable: false }
  ];

  // Helper functions to get linked entity names
  const getProjectName = (projectId) => {
    const project = projects.find(p => p.id === projectId);
    return project ? project.name : projectId;
  };

  const getEpicName = (epicId) => {
    const epic = epics.find(e => e.id === epicId);
    return epic ? epic.name : epicId;
  };

  const getProgramName = (programId) => {
    const program = programs.find(p => p.id === programId);
    return program ? program.name : programId;
  };

  const getBusinessCaseName = (bcId) => {
    const bc = businessCases.find(b => b.id === bcId);
    return bc ? bc.name : bcId;
  };

  const getMasterBusinessCaseName = (masterBcId) => {
    const masterBc = masterBusinessCases.find(mbc => mbc.id === masterBcId);
    return masterBc ? masterBc.name : masterBcId;
  };

  // Sorting logic
  const sortedItems = useMemo(() => {
    if (!sortConfig.key) return items;

    return [...items].sort((a, b) => {
      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];

      // Handle special cases for linked entities
      if (sortConfig.key === 'linkedProject') {
        aValue = getProjectName(a.linkedProjectId);
        bValue = getProjectName(b.linkedProjectId);
      } else if (sortConfig.key === 'linkedEpic') {
        aValue = getEpicName(a.linkedEpicId);
        bValue = getEpicName(b.linkedEpicId);
      } else if (sortConfig.key === 'linkedProgram') {
        aValue = getProgramName(a.linkedProgramId);
        bValue = getProgramName(b.linkedProgramId);
      } else if (sortConfig.key === 'linkedBusinessCase') {
        aValue = getBusinessCaseName(a.linkedBusinessCaseId);
        bValue = getBusinessCaseName(b.linkedBusinessCaseId);
      } else if (sortConfig.key === 'linkedMasterBC') {
        aValue = getMasterBusinessCaseName(a.linkedMasterBusinessCaseId);
        bValue = getMasterBusinessCaseName(b.linkedMasterBusinessCaseId);
      }

      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });
  }, [items, sortConfig, projects, programs, businessCases]);

  // Filtering logic
  const filteredItems = useMemo(() => {
    return sortedItems.filter(item => {
      if (filterConfig.status && item.status !== filterConfig.status) return false;
      if (filterConfig.priority && item.priority !== filterConfig.priority) return false;
      if (filterConfig.type && item.type !== filterConfig.type) return false;
      if (filterConfig.search) {
        const searchLower = filterConfig.search.toLowerCase();
        const searchableFields = [item.title, item.description, item.requestor, item.assignee];
        if (!searchableFields.some(field => field?.toLowerCase().includes(searchLower))) {
          return false;
        }
      }
      return true;
    });
  }, [sortedItems, filterConfig]);

  const handleSort = (key) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedItems(new Set(filteredItems.map(item => item.id)));
    } else {
      setSelectedItems(new Set());
    }
  };

  const handleSelectItem = (itemId, checked) => {
    const newSelected = new Set(selectedItems);
    if (checked) {
      newSelected.add(itemId);
    } else {
      newSelected.delete(itemId);
    }
    setSelectedItems(newSelected);
  };

  const handleBulkAction = (action) => {
    if (selectedItems.size === 0) {
      alert('Please select items first');
      return;
    }
    onBulkAction(action, Array.from(selectedItems));
    setSelectedItems(new Set());
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      'new': 'status-new',
      'in-progress': 'status-in-progress',
      'review': 'status-review',
      'approved': 'status-approved',
      'rejected': 'status-rejected',
      'completed': 'status-completed'
    };
    return (
      <span className={`status-badge ${statusClasses[status] || 'status-default'}`}>
        {status}
      </span>
    );
  };

  const getPriorityBadge = (priority) => {
    const priorityClasses = {
      'critical': 'priority-critical',
      'high': 'priority-high',
      'medium': 'priority-medium',
      'low': 'priority-low'
    };
    return (
      <span className={`priority-badge ${priorityClasses[priority] || 'priority-default'}`}>
        {priority}
      </span>
    );
  };

  const getProgressBar = (progress) => {
    const percentage = Math.min(Math.max(progress || 0, 0), 100);
    return (
      <div className="progress-container">
        <div className="progress-bar">
          <div 
            className="progress-fill" 
            style={{ width: `${percentage}%` }}
          ></div>
        </div>
        <span className="progress-text">{percentage}%</span>
      </div>
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="grid-loading">
        <div className="loading-spinner"></div>
        <p>Loading grid data...</p>
      </div>
    );
  }

  return (
    <div className="pulseboard-grid">
      {/* Toolbar */}
      <div className="grid-toolbar">
        <div className="toolbar-left">
          <button 
            className="filter-toggle"
            onClick={() => setShowFilters(!showFilters)}
          >
            <i className="fas fa-filter"></i>
            Filters
          </button>
          
          {selectedItems.size > 0 && (
            <div className="bulk-actions">
              <span className="selected-count">{selectedItems.size} selected</span>
              <button 
                className="bulk-btn delete"
                onClick={() => handleBulkAction('delete')}
              >
                <i className="fas fa-trash"></i>
                Delete
              </button>
              <button 
                className="bulk-btn export"
                onClick={() => handleBulkAction('export')}
              >
                <i className="fas fa-download"></i>
                Export
              </button>
            </div>
          )}
        </div>
        
        <div className="toolbar-right">
          <div className="grid-stats">
            <span>Total: {filteredItems.length}</span>
            {filteredItems.length !== items.length && (
              <span>Filtered: {filteredItems.length} of {items.length}</span>
            )}
          </div>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="grid-filters">
          <div className="filter-row">
            <input
              type="text"
              placeholder="Search..."
              value={filterConfig.search}
              onChange={(e) => setFilterConfig(prev => ({ ...prev, search: e.target.value }))}
              className="filter-input"
            />
            
            <select
              value={filterConfig.status}
              onChange={(e) => setFilterConfig(prev => ({ ...prev, status: e.target.value }))}
              className="filter-select"
            >
              <option value="">All Status</option>
              <option value="new">New</option>
              <option value="in-progress">In Progress</option>
              <option value="review">Review</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="completed">Completed</option>
            </select>
            
            <select
              value={filterConfig.priority}
              onChange={(e) => setFilterConfig(prev => ({ ...prev, priority: e.target.value }))}
              className="filter-select"
            >
              <option value="">All Priority</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
            
            <select
              value={filterConfig.type}
              onChange={(e) => setFilterConfig(prev => ({ ...prev, type: e.target.value }))}
              className="filter-select"
            >
              <option value="">All Types</option>
              <option value="feature">Feature</option>
              <option value="enhancement">Enhancement</option>
              <option value="bug">Bug</option>
              <option value="change">Change</option>
              <option value="support">Support</option>
            </select>
            
            <button 
              className="clear-filters"
              onClick={() => setFilterConfig({ status: '', priority: '', type: '', linkedEntity: '', search: '' })}
            >
              Clear
            </button>
          </div>
        </div>
      )}

      {/* Grid Table */}
      <div className="grid-container">
        <table className="excel-grid">
          <thead>
            <tr>
              {columns.map(column => (
                <th 
                  key={column.key} 
                  style={{ width: column.width }}
                  className={column.sortable ? 'sortable' : ''}
                  onClick={column.sortable ? () => handleSort(column.key) : undefined}
                >
                  {column.key === 'select' ? (
                    <input
                      type="checkbox"
                      checked={selectedItems.size === filteredItems.length && filteredItems.length > 0}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                    />
                  ) : (
                    <>
                      {column.label}
                      {column.sortable && sortConfig.key === column.key && (
                        <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'}`}></i>
                      )}
                    </>
                  )}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {filteredItems.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="empty-state">
                  <div className="empty-content">
                    <i className="fas fa-table"></i>
                    <h3>No items found</h3>
                    <p>Create your first pulseboard item to get started</p>
                  </div>
                </td>
              </tr>
            ) : (
              filteredItems.map(item => (
                <tr key={item.id} className={selectedItems.has(item.id) ? 'selected' : ''}>
                  <td>
                    <input
                      type="checkbox"
                      checked={selectedItems.has(item.id)}
                      onChange={(e) => handleSelectItem(item.id, e.target.checked)}
                    />
                  </td>
                  <td className="cell-id">{item.id}</td>
                  <td className="cell-title">{item.title}</td>
                  <td className="cell-type">{item.type}</td>
                  <td className="cell-status">{getStatusBadge(item.status)}</td>
                  <td className="cell-priority">{getPriorityBadge(item.priority)}</td>
                  <td className="cell-category">{item.category || '-'}</td>
                  <td className="cell-department">{item.department || '-'}</td>
                  <td className="cell-business-unit">{item.businessUnit || '-'}</td>
                  <td className="cell-linked">{item.linkedProjectId ? getProjectName(item.linkedProjectId) : '-'}</td>
                  <td className="cell-linked">{item.linkedEpicId ? getEpicName(item.linkedEpicId) : '-'}</td>
                  <td className="cell-linked">{item.linkedProgramId ? getProgramName(item.linkedProgramId) : '-'}</td>
                  <td className="cell-linked">{item.linkedBusinessCaseId ? getBusinessCaseName(item.linkedBusinessCaseId) : '-'}</td>
                  <td className="cell-linked">{item.linkedMasterBusinessCaseId ? getMasterBusinessCaseName(item.linkedMasterBusinessCaseId) : '-'}</td>
                  <td className="cell-user">{item.requestor}</td>
                  <td className="cell-user">{item.assignee}</td>
                  <td className="cell-date">{formatDate(item.dueDate)}</td>
                  <td className="cell-progress">{getProgressBar(item.progress)}</td>
                  <td className="cell-value">{item.businessValue}</td>
                  <td className="cell-effort">{item.effort || '-'}</td>
                  <td className="cell-cost">{item.estimatedCost ? `$${item.estimatedCost}` : '-'}</td>
                  <td className="cell-date">{formatDate(item.createdAt)}</td>
                  <td className="cell-date">{formatDate(item.updatedAt)}</td>
                  <td className="cell-actions">
                    <button 
                      className="action-btn edit"
                      onClick={() => onEdit(item)}
                      title="Edit"
                    >
                      <i className="fas fa-edit"></i>
                    </button>
                    <button 
                      className="action-btn delete"
                      onClick={() => onDelete(item.id)}
                      title="Delete"
                    >
                      <i className="fas fa-trash"></i>
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PulseboardGrid;
