// Sample relationship data for the Relationships Dashboard
export const sampleRelationshipData = {
  nodes: [
    // Ideas
    {
      id: 'idea_sample_1',
      label: 'AI-Powered Customer Support',
      type: 'idea',
      data: {
        businessUnit: 'Customer Service',
        status: 'approved',
        priority: 'high',
        estimatedCost: 150000,
        submittedBy: '<EMAIL>',
        promotedToBusinessCase: 'bc_sample_3'
      }
    },
    {
      id: 'idea_sample_5',
      label: 'Predictive Maintenance System',
      type: 'idea',
      data: {
        businessUnit: 'Manufacturing',
        status: 'approved',
        priority: 'high',
        estimatedCost: 300000,
        submittedBy: '<EMAIL>',
        promotedToBusinessCase: 'bc_sample_4'
      }
    },
    {
      id: 'idea_sample_2',
      label: 'Green Energy Initiative',
      type: 'idea',
      data: {
        businessUnit: 'Operations',
        status: 'approved',
        priority: 'medium',
        estimatedCost: 500000,
        submittedBy: '<EMAIL>'
      }
    },

    // Business Cases
    {
      id: 'bc_sample_1',
      label: 'Digital Customer Experience Platform',
      type: 'businessCase',
      data: {
        businessUnit: 'Technology',
        status: 'approved',
        totalCapex: 2500000,
        totalOpex: 800000,
        irr: 32.5,
        npv: 4200000,
        paybackPeriod: 2.1
      }
    },
    {
      id: 'bc_sample_2',
      label: 'Supply Chain Optimization Initiative',
      type: 'businessCase',
      data: {
        businessUnit: 'Operations',
        status: 'approved',
        totalCapex: 1800000,
        totalOpex: 600000,
        irr: 28.3,
        npv: 2800000,
        paybackPeriod: 1.8
      }
    },
    {
      id: 'bc_sample_3',
      label: 'Customer Service Automation',
      type: 'businessCase',
      data: {
        businessUnit: 'Customer Service',
        status: 'approved',
        totalCapex: 1200000,
        totalOpex: 400000,
        irr: 29.8,
        npv: 2100000,
        paybackPeriod: 1.9
      }
    },
    {
      id: 'bc_sample_4',
      label: 'Manufacturing Process Digitization',
      type: 'businessCase',
      data: {
        businessUnit: 'Manufacturing',
        status: 'approved',
        totalCapex: 3200000,
        totalOpex: 950000,
        irr: 26.7,
        npv: 5800000,
        paybackPeriod: 2.3
      }
    },
    {
      id: 'bc_sample_5',
      label: 'Data Analytics Platform',
      type: 'businessCase',
      data: {
        businessUnit: 'Technology',
        status: 'approved',
        totalCapex: 1500000,
        totalOpex: 500000,
        irr: 31.2,
        npv: 2900000,
        paybackPeriod: 1.7
      }
    },
    {
      id: 'bc_sample_6',
      label: 'Mobile Workforce Solution',
      type: 'businessCase',
      data: {
        businessUnit: 'Operations',
        status: 'approved',
        totalCapex: 800000,
        totalOpex: 300000,
        irr: 27.4,
        npv: 1650000,
        paybackPeriod: 2.0
      }
    },

    // Master Business Cases
    {
      id: 'mbc-1',
      label: 'Digital Transformation Initiative',
      type: 'masterBusinessCase',
      data: {
        businessUnit: 'Technology',
        status: 'active',
        totalCapex: 4000000,
        totalOpex: 1300000,
        totalNpv: 7100000,
        avgIrr: 31.85,
        linkedBusinessCasesCount: 2
      }
    },
    {
      id: 'mbc-2',
      label: 'Operational Excellence Program',
      type: 'masterBusinessCase',
      data: {
        businessUnit: 'Operations',
        status: 'active',
        totalCapex: 2600000,
        totalOpex: 900000,
        totalNpv: 4450000,
        avgIrr: 27.85,
        linkedBusinessCasesCount: 2
      }
    },
    {
      id: 'mbc-3',
      label: 'Customer Experience Excellence',
      type: 'masterBusinessCase',
      data: {
        businessUnit: 'Customer Service',
        status: 'active',
        totalCapex: 1200000,
        totalOpex: 400000,
        totalNpv: 2100000,
        avgIrr: 29.8,
        linkedBusinessCasesCount: 1
      }
    },
    {
      id: 'mbc-4',
      label: 'Manufacturing Innovation Program',
      type: 'masterBusinessCase',
      data: {
        businessUnit: 'Manufacturing',
        status: 'active',
        totalCapex: 3200000,
        totalOpex: 950000,
        totalNpv: 5800000,
        avgIrr: 26.7,
        linkedBusinessCasesCount: 1
      }
    },

    // Programs
    {
      id: 'prog-1',
      label: 'Enterprise Digital Platform',
      type: 'program',
      data: {
        businessUnit: 'Technology',
        status: 'active',
        owner: '<EMAIL>',
        progress: 65,
        linkedProjectsCount: 2,
        linkedEpicsCount: 1
      }
    },
    {
      id: 'prog-2',
      label: 'Operational Excellence Initiative',
      type: 'program',
      data: {
        businessUnit: 'Operations',
        status: 'active',
        owner: '<EMAIL>',
        progress: 55,
        linkedProjectsCount: 1,
        linkedEpicsCount: 0
      }
    },
    {
      id: 'prog-3',
      label: 'Customer Experience Excellence',
      type: 'program',
      data: {
        businessUnit: 'Customer Service',
        status: 'active',
        owner: '<EMAIL>',
        progress: 40,
        linkedProjectsCount: 0,
        linkedEpicsCount: 1
      }
    },
    {
      id: 'prog-4',
      label: 'Manufacturing Innovation',
      type: 'program',
      data: {
        businessUnit: 'Manufacturing',
        status: 'planning',
        owner: '<EMAIL>',
        progress: 15,
        linkedProjectsCount: 0,
        linkedEpicsCount: 0
      }
    },

    // Projects
    {
      id: 'proj_sample_1',
      label: 'Customer Portal Enhancement',
      type: 'project',
      data: {
        businessUnit: 'Technology',
        status: 'active',
        owner: '<EMAIL>',
        progress: 75,
        linkedBusinessCasesCount: 1,
        milestonesCount: 4
      }
    },
    {
      id: 'proj_sample_2',
      label: 'Supply Chain Analytics Dashboard',
      type: 'project',
      data: {
        businessUnit: 'Operations',
        status: 'active',
        owner: '<EMAIL>',
        progress: 60,
        linkedBusinessCasesCount: 1,
        milestonesCount: 4
      }
    },

    // Epics
    {
      id: 'epic_sample_1',
      label: 'Digital Transformation Epic',
      type: 'epic',
      data: {
        businessUnit: 'Technology',
        status: 'active',
        owner: '<EMAIL>',
        progress: 45,
        linkedBusinessCasesCount: 2,
        milestonesCount: 4
      }
    },
    {
      id: 'epic_sample_2',
      label: 'Customer Experience Excellence',
      type: 'epic',
      data: {
        businessUnit: 'Customer Service',
        status: 'active',
        owner: '<EMAIL>',
        progress: 30,
        linkedBusinessCasesCount: 1,
        milestonesCount: 4
      }
    }
  ],

  edges: [
    // Ideas to Business Cases
    {
      source: 'idea_sample_1',
      target: 'bc_sample_3',
      type: 'leads_to',
      label: 'promoted to',
      isBidirectional: false
    },
    {
      source: 'idea_sample_5',
      target: 'bc_sample_4',
      type: 'leads_to',
      label: 'promoted to',
      isBidirectional: false
    },

    // Business Cases to Master Business Cases
    {
      source: 'bc_sample_1',
      target: 'mbc-1',
      type: 'includes',
      label: 'included in',
      isBidirectional: false
    },
    {
      source: 'bc_sample_5',
      target: 'mbc-1',
      type: 'includes',
      label: 'included in',
      isBidirectional: false
    },
    {
      source: 'bc_sample_2',
      target: 'mbc-2',
      type: 'includes',
      label: 'included in',
      isBidirectional: false
    },
    {
      source: 'bc_sample_6',
      target: 'mbc-2',
      type: 'includes',
      label: 'included in',
      isBidirectional: false
    },
    {
      source: 'bc_sample_3',
      target: 'mbc-3',
      type: 'includes',
      label: 'included in',
      isBidirectional: false
    },
    {
      source: 'bc_sample_4',
      target: 'mbc-4',
      type: 'includes',
      label: 'included in',
      isBidirectional: false
    },

    // Master Business Cases to Programs
    {
      source: 'mbc-1',
      target: 'prog-1',
      type: 'connects_to',
      label: 'drives',
      isBidirectional: false
    },
    {
      source: 'mbc-2',
      target: 'prog-2',
      type: 'connects_to',
      label: 'drives',
      isBidirectional: false
    },
    {
      source: 'mbc-3',
      target: 'prog-3',
      type: 'connects_to',
      label: 'drives',
      isBidirectional: false
    },
    {
      source: 'mbc-4',
      target: 'prog-4',
      type: 'connects_to',
      label: 'drives',
      isBidirectional: false
    },

    // Programs to Projects/Epics (bidirectional)
    {
      source: 'prog-1',
      target: 'proj_sample_1',
      type: 'implements',
      label: 'implements via',
      isBidirectional: true
    },
    {
      source: 'prog-1',
      target: 'epic_sample_1',
      type: 'implements',
      label: 'implements via',
      isBidirectional: true
    },
    {
      source: 'prog-2',
      target: 'proj_sample_2',
      type: 'implements',
      label: 'implements via',
      isBidirectional: true
    },
    {
      source: 'prog-3',
      target: 'epic_sample_2',
      type: 'implements',
      label: 'implements via',
      isBidirectional: true
    }
  ],

  summary: {
    totalEntities: 22,
    totalConnections: 16,
    entityCounts: {
      ideas: 3,
      businessCases: 6,
      masterBusinessCases: 4,
      programs: 4,
      projects: 2,
      epics: 2
    }
  }
};
