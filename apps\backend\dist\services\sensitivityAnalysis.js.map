{"version": 3, "file": "sensitivityAnalysis.js", "sourceRoot": "", "sources": ["../../src/services/sensitivityAnalysis.ts"], "names": [], "mappings": ";;;AAAA,qDAQmC;AACnC,mEAAsE;AAEtE,MAAa,0BAA0B;IAIrC,MAAM,CAAC,0BAA0B,CAC/B,SAAyB,EACzB,SAAgC;QAEhC,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAC/D,MAAM,OAAO,GAAG,IAAI,CAAC,2BAA2B,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAEvE,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,IAAI,EAAE,0BAA0B,SAAS,CAAC,IAAI,EAAE;YAChD,SAAS;YACT,SAAS;YACT,OAAO;YACP,SAAS,EAAE,SAAS,CAAC,SAAS;YAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAKO,MAAM,CAAC,iBAAiB,CAC9B,SAAyB,EACzB,SAAgC;QAEhC,MAAM,SAAS,GAAe,EAAE,CAAC;QAGjC,MAAM,aAAa,GAA2B,EAAE,CAAC;QACjD,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,mDAA2B,CAAC,yBAAyB,CACvE,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,aAAa,CAAC,CACrD,CAAC;QAEF,SAAS,CAAC,IAAI,CAAC;YACb,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,oBAAY,CAAC,SAAS;YAC5B,SAAS,EAAE,aAAa;YACxB,OAAO,EAAE,WAAW;SACrB,CAAC,CAAC;QAGH,MAAM,aAAa,GAA2B,EAAE,CAAC;QACjD,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,mDAA2B,CAAC,yBAAyB,CACvE,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,aAAa,CAAC,CACrD,CAAC;QAEF,SAAS,CAAC,IAAI,CAAC;YACb,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,oBAAY,CAAC,SAAS;YAC5B,SAAS,EAAE,aAAa;YACxB,OAAO,EAAE,WAAW;SACrB,CAAC,CAAC;QAGH,MAAM,cAAc,GAA2B,EAAE,CAAC;QAClD,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,mDAA2B,CAAC,yBAAyB,CACxE,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,cAAc,CAAC,CACtD,CAAC;QAEF,SAAS,CAAC,IAAI,CAAC;YACb,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,oBAAY,CAAC,UAAU;YAC7B,SAAS,EAAE,cAAc;YACzB,OAAO,EAAE,YAAY;SACtB,CAAC,CAAC;QAGH,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAE3B,MAAM,aAAa,GAAG,EAAE,GAAG,aAAa,EAAE,CAAC;YAC3C,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC;YAE/C,MAAM,WAAW,GAAG,mDAA2B,CAAC,yBAAyB,CACvE,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,aAAa,CAAC,CACrD,CAAC;YAEF,SAAS,CAAC,IAAI,CAAC;gBACb,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;gBACrB,IAAI,EAAE,GAAG,QAAQ,CAAC,IAAI,SAAS;gBAC/B,IAAI,EAAE,oBAAY,CAAC,MAAM;gBACzB,SAAS,EAAE,aAAa;gBACxB,OAAO,EAAE,WAAW;aACrB,CAAC,CAAC;YAGH,MAAM,YAAY,GAAG,EAAE,GAAG,aAAa,EAAE,CAAC;YAC1C,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC;YAE9C,MAAM,UAAU,GAAG,mDAA2B,CAAC,yBAAyB,CACtE,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,YAAY,CAAC,CACpD,CAAC;YAEF,SAAS,CAAC,IAAI,CAAC;gBACb,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;gBACrB,IAAI,EAAE,GAAG,QAAQ,CAAC,IAAI,QAAQ;gBAC9B,IAAI,EAAE,oBAAY,CAAC,MAAM;gBACzB,SAAS,EAAE,YAAY;gBACvB,OAAO,EAAE,UAAU;aACpB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAKO,MAAM,CAAC,2BAA2B,CACxC,SAAqB,EACrB,SAAgC;QAEhC,MAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,oBAAY,CAAC,SAAS,CAAC,CAAC;QAC5E,IAAI,CAAC,YAAY;YAAE,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAEnE,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,MAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CACtC,CAAC,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC,IAAI,KAAK,oBAAY,CAAC,MAAM,CACvE,CAAC;YACF,MAAM,WAAW,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CACrC,CAAC,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,oBAAY,CAAC,MAAM,CACtE,CAAC;YAEF,IAAI,YAAY,IAAI,WAAW,EAAE,CAAC;gBAChC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;gBAC7E,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;gBACjG,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;gBAElF,OAAO,CAAC,IAAI,CAAC;oBACX,UAAU,EAAE,QAAQ,CAAC,EAAE;oBACvB,YAAY,EAAE,QAAQ,CAAC,IAAI;oBAC3B,MAAM;oBACN,WAAW;oBACX,UAAU;iBACX,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;IACzE,CAAC;IAKO,MAAM,CAAC,eAAe,CAC5B,YAAsB,EACtB,YAAsB,EACtB,WAAqB;QAErB,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC;QACzC,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC;QACzC,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC;QAGvC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC;QAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;QAE7C,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IACzC,CAAC;IAKO,MAAM,CAAC,oBAAoB,CACjC,QAA6B,EAC7B,YAAsB,EACtB,YAAsB,EACtB,WAAqB;QAErB,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC;QACzC,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC;QACzC,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC;QAEvC,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;QAC7D,MAAM,SAAS,GAAG,OAAO,GAAG,MAAM,CAAC;QAGnC,OAAO,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC9C,CAAC;IAKO,MAAM,CAAC,mBAAmB,CAChC,QAA6B,EAC7B,YAAsB,EACtB,YAAsB;QAEtB,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC;QACzC,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC;QAEzC,IAAI,OAAO,KAAK,CAAC,IAAI,QAAQ,CAAC,SAAS,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAExD,MAAM,gBAAgB,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC;QACvD,MAAM,qBAAqB,GAAG,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC;QAE5F,OAAO,gBAAgB,GAAG,qBAAqB,CAAC;IAClD,CAAC;IAKO,MAAM,CAAC,qBAAqB,CAClC,SAAyB,EACzB,SAAiC;QAEjC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;QAEpD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,EAAE;YAExD,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YACnD,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;YACpD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAKO,MAAM,CAAC,oBAAoB,CACjC,KAAqB,EACrB,QAA6B,EAC7B,KAAa;QAEb,QAAQ,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAC1B,KAAK,wBAAgB,CAAC,IAAI;gBACxB,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;oBACrD,KAAK,CAAC,UAAU,CAAC,YAAY,GAAG,KAAK,CAAC;gBACxC,CAAC;qBAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;oBACvD,KAAK,CAAC,UAAU,CAAC,OAAO,GAAG,KAAK,CAAC;gBACnC,CAAC;gBACD,MAAM;YAER,KAAK,wBAAgB,CAAC,IAAI;gBAExB,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACtC,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC;gBACH,MAAM;YAER,KAAK,wBAAgB,CAAC,KAAK;gBAEzB,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oBAC5C,MAAM,CAAC,KAAK,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;gBAC/C,CAAC,CAAC,CAAC;gBACH,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBAC1C,KAAK,CAAC,SAAS,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;gBAClD,CAAC,CAAC,CAAC;gBACH,MAAM;YAER,KAAK,wBAAgB,CAAC,MAAM;gBAE1B,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oBAC5C,MAAM,CAAC,MAAM,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;gBAChD,CAAC,CAAC,CAAC;gBACH,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBAC1C,KAAK,CAAC,MAAM,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;gBAC/C,CAAC,CAAC,CAAC;gBACH,MAAM;QACV,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,gBAAgB,CAAC,QAA6B;QAE3D,QAAQ,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAC1B,KAAK,wBAAgB,CAAC,IAAI;gBACxB,OAAO,QAAQ,CAAC,QAAQ,CAAC;YAC3B,KAAK,wBAAgB,CAAC,KAAK,CAAC;YAC5B,KAAK,wBAAgB,CAAC,MAAM;gBAC1B,OAAO,QAAQ,CAAC,QAAQ,CAAC;YAC3B,KAAK,wBAAgB,CAAC,IAAI;gBAExB,OAAO,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC7F;gBACE,OAAO,QAAQ,CAAC,QAAQ,CAAC;QAC7B,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,iBAAiB,CAAC,QAA6B;QAE5D,QAAQ,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAC1B,KAAK,wBAAgB,CAAC,IAAI;gBACxB,OAAO,QAAQ,CAAC,QAAQ,CAAC;YAC3B,KAAK,wBAAgB,CAAC,KAAK,CAAC;YAC5B,KAAK,wBAAgB,CAAC,MAAM;gBAC1B,OAAO,QAAQ,CAAC,QAAQ,CAAC;YAC3B,KAAK,wBAAgB,CAAC,IAAI;gBACxB,OAAO,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC7F;gBACE,OAAO,QAAQ,CAAC,QAAQ,CAAC;QAC7B,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,gBAAgB,CAAC,EAAU;QAGxC,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,MAAM,CAAC,UAAU;QACvB,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjD,CAAC;CACF;AAnVD,gEAmVC"}