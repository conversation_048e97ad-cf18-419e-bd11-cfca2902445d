import { apiGet } from './api';

class DashboardService {
  // Get complete dashboard data
  async getDashboardData() {
    return await apiGet('/api/dashboard');
  }

  // Get KPI data
  async getKPIs() {
    return await apiGet('/api/dashboard/kpis');
  }

  // Get chart data
  async getChartData() {
    return await apiGet('/api/dashboard/charts');
  }

  // Get recent activity
  async getRecentActivity() {
    return await apiGet('/api/dashboard/activity');
  }

  // Get insights
  async getInsights() {
    return await apiGet('/api/dashboard/insights');
  }

  // Get business unit summary
  async getBusinessUnitSummary() {
    return await apiGet('/api/dashboard/business-units');
  }

  // Get financial summary
  async getFinancialSummary() {
    return await apiGet('/api/dashboard/financial-summary');
  }

  // Get comprehensive relationship data
  async getRelationshipData() {
    return await apiGet('/api/relationships');
  }

  // Get project portfolio status
  async getPortfolioStatus() {
    return await apiGet('/api/dashboard/portfolio-status');
  }
}

export const dashboardService = new DashboardService();
export default dashboardService;
