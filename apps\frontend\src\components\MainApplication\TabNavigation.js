import React, { useState } from 'react';
import PropTypes from 'prop-types';

const TabNavigation = ({ tabs, activeTab, onTabChange }) => {
  const [showAllTabs, setShowAllTabs] = useState(false);

  // Show first 5 tabs by default, rest on toggle
  const visibleTabs = showAllTabs ? tabs : tabs.slice(0, 5);
  const hiddenTabsCount = tabs.length - 5;

  return (
    <div className="border-b border-gray-200">
      <nav className="flex flex-wrap -mb-px">
        {visibleTabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`group inline-flex items-center py-4 px-6 border-b-2 font-medium text-sm transition-colors ${
              activeTab === tab.id
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50'
            }`}
            title={tab.description}
          >
            <i className={`${tab.icon} mr-2 text-lg`}></i>
            <span className="hidden sm:inline">{tab.name}</span>
            <span className="sm:hidden">{tab.name.split(' ')[0]}</span>
          </button>
        ))}

        {/* Show More/Less Toggle */}
        {tabs.length > 5 && (
          <button
            onClick={() => setShowAllTabs(!showAllTabs)}
            className="group inline-flex items-center py-4 px-6 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50 font-medium text-sm transition-colors"
          >
            <i className={`fas ${showAllTabs ? 'fa-chevron-up' : 'fa-chevron-down'} mr-2`}></i>
            <span className="hidden sm:inline">
              {showAllTabs ? 'Show Less' : `${hiddenTabsCount} More`}
            </span>
            <span className="sm:hidden">
              {showAllTabs ? 'Less' : `+${hiddenTabsCount}`}
            </span>
          </button>
        )}
      </nav>

      {/* Active Tab Info */}
      <div className="bg-gray-50 px-6 py-2 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <i className={`${tabs.find(t => t.id === activeTab)?.icon} text-blue-600 mr-2`}></i>
            <span className="text-sm font-medium text-gray-900">
              {tabs.find(t => t.id === activeTab)?.name}
            </span>
            <span className="text-sm text-gray-500 ml-2">
              - {tabs.find(t => t.id === activeTab)?.description}
            </span>
          </div>
          
          {/* Tab Actions */}
          <div className="flex items-center space-x-2">
            <button
              className="text-gray-400 hover:text-gray-600 p-1"
              title="Refresh Tab"
              onClick={() => window.location.reload()}
            >
              <i className="fas fa-sync-alt text-sm"></i>
            </button>
            <button
              className="text-gray-400 hover:text-gray-600 p-1"
              title="Help"
            >
              <i className="fas fa-question-circle text-sm"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

TabNavigation.propTypes = {
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      icon: PropTypes.string.isRequired,
      description: PropTypes.string.isRequired,
    })
  ).isRequired,
  activeTab: PropTypes.string.isRequired,
  onTabChange: PropTypes.func.isRequired,
};

export default TabNavigation;
