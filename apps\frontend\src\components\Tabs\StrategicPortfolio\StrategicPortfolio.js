import React, { useState, useEffect } from 'react';
import { useData } from '../../../context/DataContext';
import PortfolioOverview from './PortfolioOverview/PortfolioOverview';
import ProfitabilityMatrix from './ProfitabilityMatrix/ProfitabilityMatrix';
import PortfolioComparison from './PortfolioComparison/PortfolioComparison';
import RiskReturnAnalysis from './RiskReturnAnalysis/RiskReturnAnalysis';
import { sampleMasterBCs, samplePrograms } from '../../../data/samplePortfolioData';
import './StrategicPortfolio.css';

const StrategicPortfolio = () => {
  const { 
    masterBusinessCases, 
    programs, 
    businessCases,
    fetchMasterBusinessCases, 
    fetchPrograms,
    fetchBusinessCases,
    loading 
  } = useData();

  const [activeView, setActiveView] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);
  const [portfolioData, setPortfolioData] = useState({
    masterBCs: [],
    programs: [],
    businessCases: [],
    profitabilityMetrics: {},
    riskMetrics: {}
  });

  useEffect(() => {
    // Load all data when component mounts
    const loadData = async () => {
      try {
        await Promise.all([
          fetchMasterBusinessCases(),
          fetchPrograms(),
          fetchBusinessCases()
        ]);
      } catch (error) {
        console.log('API not available, using sample data');
        // If API fails, we'll use sample data in the next useEffect
      }
    };
    loadData();
  }, [fetchMasterBusinessCases, fetchPrograms, fetchBusinessCases]);

  useEffect(() => {
    // Process data for portfolio analysis
    const dataToProcess = {
      masterBCs: masterBusinessCases.length > 0 ? masterBusinessCases : sampleMasterBCs,
      programs: programs.length > 0 ? programs : samplePrograms,
      businessCases: businessCases.length > 0 ? businessCases : []
    };

    console.log('🔍 StrategicPortfolio - Data to process:', {
      masterBCsCount: dataToProcess.masterBCs.length,
      programsCount: dataToProcess.programs.length,
      businessCasesCount: dataToProcess.businessCases.length,
      masterBCs: dataToProcess.masterBCs,
      programs: dataToProcess.programs
    });

    processPortfolioData(dataToProcess);
  }, [masterBusinessCases, programs, businessCases]);

  const processPortfolioData = (data) => {
    // Calculate profitability metrics for Master BCs
    const masterBCMetrics = data.masterBCs.map(mbc => ({
      ...mbc,
      profitability: calculateProfitability(mbc.aggregatedMetrics),
      riskScore: calculateRiskScore(mbc),
      roi: calculateROI(mbc.aggregatedMetrics),
      category: mbc.category || 'Uncategorized'
    }));

    // Calculate profitability metrics for Programs
    const programMetrics = data.programs.map(program => {
      const linkedMasterBC = data.masterBCs.find(mbc => mbc.id === program.linkedMasterBC);
      return {
        ...program,
        profitability: linkedMasterBC ? calculateProfitability(linkedMasterBC.aggregatedMetrics) : 0,
        riskScore: calculateProgramRiskScore(program),
        roi: linkedMasterBC ? calculateROI(linkedMasterBC.aggregatedMetrics) : 0,
        linkedMasterBCMetrics: linkedMasterBC?.aggregatedMetrics || {}
      };
    });

    // Calculate overall portfolio metrics
    const profitabilityMetrics = {
      totalInvestment: masterBCMetrics.reduce((sum, mbc) => sum + (mbc.aggregatedMetrics?.totalInvestment || 0), 0),
      totalNPV: masterBCMetrics.reduce((sum, mbc) => sum + (mbc.aggregatedMetrics?.totalNPV || 0), 0),
      avgIRR: masterBCMetrics.reduce((sum, mbc) => sum + (mbc.aggregatedMetrics?.avgIRR || 0), 0) / masterBCMetrics.length || 0,
      profitableCount: masterBCMetrics.filter(mbc => mbc.profitability > 0).length,
      totalCount: masterBCMetrics.length
    };

    const newPortfolioData = {
      masterBCs: masterBCMetrics,
      programs: programMetrics,
      businessCases: data.businessCases,
      profitabilityMetrics,
      riskMetrics: calculateRiskMetrics(masterBCMetrics, programMetrics)
    };

    console.log('📊 StrategicPortfolio - Setting portfolio data:', newPortfolioData);
    setPortfolioData(newPortfolioData);
  };

  const calculateProfitability = (metrics) => {
    if (!metrics || !metrics.totalNPV || !metrics.totalInvestment) return 0;
    return (metrics.totalNPV / metrics.totalInvestment) * 100;
  };

  const calculateROI = (metrics) => {
    if (!metrics || !metrics.totalNPV || !metrics.totalInvestment) return 0;
    return ((metrics.totalNPV / metrics.totalInvestment) * 100).toFixed(1);
  };

  const calculateRiskScore = (mbc) => {
    // Simple risk scoring based on investment size, IRR, and payback period
    const investment = mbc.aggregatedMetrics?.totalInvestment || 0;
    const irr = mbc.aggregatedMetrics?.avgIRR || 0;
    const payback = mbc.aggregatedMetrics?.avgPaybackPeriod || 0;
    
    let riskScore = 0;
    
    // Investment size risk (higher investment = higher risk)
    if (investment > 5000000) riskScore += 3;
    else if (investment > 2000000) riskScore += 2;
    else riskScore += 1;
    
    // IRR risk (lower IRR = higher risk)
    if (irr < 10) riskScore += 3;
    else if (irr < 15) riskScore += 2;
    else riskScore += 1;
    
    // Payback period risk (longer payback = higher risk)
    if (payback > 4) riskScore += 3;
    else if (payback > 2) riskScore += 2;
    else riskScore += 1;
    
    return Math.min(riskScore, 10); // Cap at 10
  };

  const calculateProgramRiskScore = (program) => {
    // Simple program risk scoring
    let riskScore = 5; // Base score
    
    if (program.priority === 'high') riskScore -= 1;
    if (program.status === 'active') riskScore -= 1;
    if (program.linkedMasterBC) riskScore -= 1;
    
    return Math.max(riskScore, 1); // Minimum score of 1
  };

  const calculateRiskMetrics = (masterBCs, programs) => {
    return {
      highRiskCount: masterBCs.filter(mbc => mbc.riskScore >= 7).length,
      mediumRiskCount: masterBCs.filter(mbc => mbc.riskScore >= 4 && mbc.riskScore < 7).length,
      lowRiskCount: masterBCs.filter(mbc => mbc.riskScore < 4).length,
      avgRiskScore: masterBCs.reduce((sum, mbc) => sum + mbc.riskScore, 0) / masterBCs.length || 0
    };
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      fetchMasterBusinessCases(),
      fetchPrograms(),
      fetchBusinessCases()
    ]);
    setRefreshing(false);
  };

  const views = [
    { id: 'overview', name: 'Portfolio Overview', icon: 'fas fa-chart-pie' },
    { id: 'profitability', name: 'Profitability Matrix', icon: 'fas fa-chart-bar' },
    { id: 'comparison', name: 'Portfolio Comparison', icon: 'fas fa-balance-scale' },
    { id: 'risk-return', name: 'Risk-Return Analysis', icon: 'fas fa-chart-scatter' }
  ];

  if (loading.masterBusinessCases || loading.programs) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <p className="text-gray-600">Loading portfolio data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="strategic-portfolio">
      {/* Header */}
      <div className="portfolio-header">
        <div className="header-content">
          <h2 className="portfolio-title">Strategic Portfolio Dashboard</h2>
          <p className="portfolio-subtitle">
            Analyze profitability and performance of Master Business Cases and Programs
          </p>
        </div>
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="refresh-btn"
        >
          <i className={`fas fa-sync-alt ${refreshing ? 'fa-spin' : ''}`}></i>
          Refresh
        </button>
      </div>

      {/* View Navigation */}
      <div className="view-navigation">
        {views.map(view => (
          <button
            key={view.id}
            onClick={() => setActiveView(view.id)}
            className={`view-btn ${activeView === view.id ? 'active' : ''}`}
          >
            <i className={view.icon}></i>
            {view.name}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="portfolio-content">
        {activeView === 'overview' && (
          <PortfolioOverview 
            portfolioData={portfolioData}
            onViewChange={setActiveView}
          />
        )}
        
        {activeView === 'profitability' && (
          <ProfitabilityMatrix 
            masterBCs={portfolioData.masterBCs}
            programs={portfolioData.programs}
          />
        )}
        
        {activeView === 'comparison' && (
          <PortfolioComparison 
            masterBCs={portfolioData.masterBCs}
            programs={portfolioData.programs}
          />
        )}
        
        {activeView === 'risk-return' && (
          <RiskReturnAnalysis 
            masterBCs={portfolioData.masterBCs}
            programs={portfolioData.programs}
          />
        )}
      </div>
    </div>
  );
};

export default StrategicPortfolio;
