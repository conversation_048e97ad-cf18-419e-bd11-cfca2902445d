import React, { useState, useEffect, useMemo } from 'react';
import { useData } from '../../../context/DataContext';
import RelationshipTree from './RelationshipTree/RelationshipTree';
import RelationshipMatrix from './RelationshipMatrix/RelationshipMatrix';
import RelationshipGraph from './RelationshipGraph/RelationshipGraph';
import RelationshipStats from './RelationshipStats/RelationshipStats';
import { dashboardService } from '../../../services/dashboardService';
import { sampleRelationshipData } from '../../../data/sampleRelationshipData';
import './RelationshipsDashboard.css';

const RelationshipsDashboard = () => {
  const [relationshipData, setRelationshipData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeView, setActiveView] = useState('graph');
  const [filters, setFilters] = useState({
    search: '',
    entityType: '',
    connectionType: ''
  });

  // Load relationship data from new API endpoint
  const loadRelationshipData = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔄 Loading relationship data - using enhanced sample data for demo');

      // Force use of sample data for demo purposes
      setRelationshipData(sampleRelationshipData);
      console.log(`📊 Loaded ${sampleRelationshipData.summary.totalEntities} entities with ${sampleRelationshipData.summary.totalConnections} connections`);

    } catch (err) {
      console.error('❌ Error loading relationship data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadRelationshipData();
  }, []);

  // Process and filter relationship data
  const processedData = useMemo(() => {
    if (!relationshipData) {
      return {
        nodes: [],
        edges: [],
        hierarchy: {},
        stats: {
          totalEntities: 0,
          totalConnections: 0,
          orphanedEntities: 0,
          fullyConnectedChains: 0
        }
      };
    }

    // Use the data directly from the API (it's already properly structured)
    const processedData = {
      nodes: relationshipData.nodes || [],
      edges: relationshipData.edges || [],
      hierarchy: {},
      stats: {
        totalEntities: relationshipData.summary?.totalEntities || 0,
        totalConnections: relationshipData.summary?.totalConnections || 0,
        orphanedEntities: 0,
        fullyConnectedChains: 0
      }
    };

    // Calculate additional stats
    const connectedNodeIds = new Set();
    relationshipData.edges?.forEach(edge => {
      connectedNodeIds.add(edge.source);
      connectedNodeIds.add(edge.target);
    });

    processedData.stats.orphanedEntities = processedData.nodes.length - connectedNodeIds.size;

    return processedData;
  }, [relationshipData]);

  // Filter data based on current filters
  const filteredData = useMemo(() => {
    if (!processedData || (!filters.search && !filters.entityType && !filters.connectionType)) {
      return processedData;
    }

    const filtered = {
      ...processedData,
      nodes: processedData.nodes.filter(node => {
        const matchesSearch = !filters.search ||
          node.label.toLowerCase().includes(filters.search.toLowerCase());
        const matchesType = !filters.entityType || node.type === filters.entityType;
        return matchesSearch && matchesType;
      }),
      edges: processedData.edges.filter(edge => {
        const matchesConnectionType = !filters.connectionType || edge.type === filters.connectionType;
        return matchesConnectionType;
      })
    };

    return filtered;
  }, [processedData, filters]);

  const handleRefresh = () => {
    loadRelationshipData();
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Loading state
  if (loading) {
    return (
      <div className="relationships-dashboard">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading comprehensive relationship data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="relationships-dashboard">
        <div className="error-container">
          <h3>Error Loading Relationship Data</h3>
          <p>{error}</p>
          <button onClick={handleRefresh} className="btn btn-primary">
            <i className="fas fa-retry"></i>
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="relationships-dashboard">
      {/* Header */}
      <div className="dashboard-header">
        <div className="header-info">
          <h2>Relationships Dashboard</h2>
          <p>Visualize entity relationships and hierarchies</p>
        </div>
        <button className="btn btn-primary" onClick={handleRefresh}>
          <i className="fas fa-refresh"></i>
          Refresh View
        </button>
      </div>

      {/* Statistics Overview */}
      <RelationshipStats data={processedData} />

      {/* View Controls */}
      <div className="view-controls">
        <div className="view-tabs">
          <button
            className={`tab-button ${activeView === 'graph' ? 'active' : ''}`}
            onClick={() => setActiveView('graph')}
          >
            <i className="fas fa-project-diagram"></i>
            Graph View
          </button>
          <button
            className={`tab-button ${activeView === 'tree' ? 'active' : ''}`}
            onClick={() => setActiveView('tree')}
          >
            <i className="fas fa-sitemap"></i>
            Tree View
          </button>
          <button
            className={`tab-button ${activeView === 'matrix' ? 'active' : ''}`}
            onClick={() => setActiveView('matrix')}
          >
            <i className="fas fa-table"></i>
            Matrix View
          </button>
        </div>

        {/* Filters */}
        <div className="filters">
          <input
            type="text"
            placeholder="Search entities..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className="filter-input"
          />
          <select
            value={filters.entityType}
            onChange={(e) => handleFilterChange('entityType', e.target.value)}
            className="filter-select"
          >
            <option value="">All Entity Types</option>
            <option value="idea">Ideas</option>
            <option value="businessCase">Business Cases</option>
            <option value="masterBusinessCase">Master Business Cases</option>
            <option value="program">Programs</option>
            <option value="project">Projects</option>
            <option value="epic">Epics</option>
          </select>
        </div>
      </div>

      {/* Main Content */}
      <div className="dashboard-content">
        {activeView === 'graph' && (
          <RelationshipGraph data={filteredData} />
        )}
        {activeView === 'tree' && (
          <RelationshipTree data={filteredData} />
        )}
        {activeView === 'matrix' && (
          <RelationshipMatrix data={filteredData} />
        )}
      </div>
    </div>
  );
};

export default RelationshipsDashboard;
