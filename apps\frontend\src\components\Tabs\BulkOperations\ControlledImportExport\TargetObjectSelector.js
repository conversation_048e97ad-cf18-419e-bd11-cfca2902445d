import React from 'react';

const TargetObjectSelector = ({ selectedTargetObject, onTargetObjectChange, dataType, operation }) => {
  // Define which data types are compatible with which target objects
  const getCompatibleTargetObjects = (dataType) => {
    const allTargetObjects = [
      {
        id: 'business-case',
        name: 'Business Cases',
        description: 'Individual business case entities',
        icon: 'fas fa-briefcase',
        color: 'text-blue-500',
        compatibleDataTypes: ['opex', 'capex', 'revenue', 'financial-metrics', 'risk-assessments'],
        examples: ['AI Customer Support Implementation', 'Digital Transformation Program']
      },
      {
        id: 'master-business-case',
        name: 'Master Business Cases',
        description: 'Master business case portfolios',
        icon: 'fas fa-folder-open',
        color: 'text-purple-500',
        compatibleDataTypes: ['financial-metrics', 'business-unit-data'],
        examples: ['Technology Innovation Program', 'Customer Service Excellence']
      },
      {
        id: 'program',
        name: 'Programs',
        description: 'Program-level entities',
        icon: 'fas fa-sitemap',
        color: 'text-green-500',
        compatibleDataTypes: ['resource-plan', 'milestones', 'business-unit-data', 'financial-metrics'],
        examples: ['Digital Innovation Program', 'Operations Excellence Program']
      },
      {
        id: 'project',
        name: 'Projects',
        description: 'Individual project entities',
        icon: 'fas fa-project-diagram',
        color: 'text-orange-500',
        compatibleDataTypes: ['resource-plan', 'milestones', 'opex', 'capex'],
        examples: ['AI Customer Support Implementation', 'Green Energy Transition']
      },
      {
        id: 'epic',
        name: 'Epics',
        description: 'Epic-level entities',
        icon: 'fas fa-layer-group',
        color: 'text-red-500',
        compatibleDataTypes: ['milestones', 'resource-plan'],
        examples: ['Digital Transformation Epic', 'Customer Service Excellence Epic']
      },
      {
        id: 'idea',
        name: 'Ideas',
        description: 'Idea entities in the pipeline',
        icon: 'fas fa-lightbulb',
        color: 'text-yellow-500',
        compatibleDataTypes: ['business-unit-data', 'risk-assessments'],
        examples: ['AI-Powered Analytics Platform', 'Sustainable Supply Chain Initiative']
      }
    ];

    return allTargetObjects.filter(obj => 
      obj.compatibleDataTypes.includes(dataType)
    );
  };

  const compatibleObjects = getCompatibleTargetObjects(dataType);

  if (compatibleObjects.length === 0) {
    return (
      <div className="target-object-selector">
        <div className="no-compatible-objects">
          <i className="fas fa-exclamation-circle text-yellow-500"></i>
          <p>No compatible target objects found for the selected data type.</p>
          <p>Please select a different data type.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="target-object-selector">
      <div className="target-object-grid">
        {compatibleObjects.map(targetObject => (
          <div
            key={targetObject.id}
            className={`target-object-card ${selectedTargetObject === targetObject.id ? 'selected' : ''}`}
            onClick={() => onTargetObjectChange(targetObject.id)}
          >
            <div className="card-header">
              <i className={`${targetObject.icon} ${targetObject.color}`}></i>
              <h5>{targetObject.name}</h5>
            </div>
            <p className="card-description">{targetObject.description}</p>
            
            <div className="card-examples">
              <span className="examples-label">Examples:</span>
              <ul className="examples-list">
                {targetObject.examples.map(example => (
                  <li key={example}>{example}</li>
                ))}
              </ul>
            </div>

            <div className="compatibility-badge">
              <i className="fas fa-check-circle text-green-500"></i>
              Compatible with {dataType.replace('-', ' ')}
            </div>
          </div>
        ))}
      </div>

      {selectedTargetObject && (
        <div className="selected-target-info">
          <div className="info-header">
            <i className="fas fa-info-circle text-blue-500"></i>
            <h6>Target Object Information</h6>
          </div>
          {(() => {
            const selected = compatibleObjects.find(obj => obj.id === selectedTargetObject);
            return (
              <div className="info-content">
                <p><strong>Target:</strong> {selected.name}</p>
                <p><strong>Description:</strong> {selected.description}</p>
                
                {operation === 'import' && (
                  <div className="import-behavior">
                    <p><strong>Import Behavior:</strong></p>
                    <ul>
                      <li>Data will be {getImportBehavior(dataType, selectedTargetObject)}</li>
                      <li>Existing data will be preserved unless explicitly overwritten</li>
                      <li>Validation will ensure data integrity before import</li>
                      <li>Rollback option will be available after import</li>
                    </ul>
                  </div>
                )}
                
                {operation === 'export' && (
                  <div className="export-behavior">
                    <p><strong>Export Behavior:</strong></p>
                    <ul>
                      <li>All {selected.name.toLowerCase()} matching the scope will be included</li>
                      <li>Only {dataType.replace('-', ' ')} data will be exported</li>
                      <li>Related metadata will be included for context</li>
                      <li>Export will include calculated fields where applicable</li>
                    </ul>
                  </div>
                )}
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
};

// Helper function to describe import behavior
const getImportBehavior = (dataType, targetObject) => {
  const behaviors = {
    'opex': {
      'business-case': 'added to or updated in the selected business cases',
      'project': 'associated with the selected projects'
    },
    'capex': {
      'business-case': 'added to or updated in the selected business cases',
      'project': 'associated with the selected projects'
    },
    'revenue': {
      'business-case': 'added to or updated in the selected business cases'
    },
    'resource-plan': {
      'program': 'associated with the selected programs',
      'project': 'associated with the selected projects',
      'epic': 'associated with the selected epics'
    },
    'milestones': {
      'program': 'added to the selected programs',
      'project': 'added to the selected projects',
      'epic': 'added to the selected epics'
    },
    'financial-metrics': {
      'business-case': 'updated in the selected business cases',
      'master-business-case': 'updated in the selected master business cases',
      'program': 'updated in the selected programs'
    },
    'business-unit-data': {
      'master-business-case': 'associated with the selected master business cases',
      'program': 'associated with the selected programs',
      'idea': 'associated with the selected ideas'
    },
    'risk-assessments': {
      'business-case': 'added to the selected business cases',
      'idea': 'associated with the selected ideas'
    }
  };

  return behaviors[dataType]?.[targetObject] || 'processed according to the data type requirements';
};

export default TargetObjectSelector;
