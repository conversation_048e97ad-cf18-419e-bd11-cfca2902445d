import React, { useState } from 'react';

const ErpIntegration = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    { id: 'overview', name: 'Overview', icon: 'fas fa-chart-pie' },
    { id: 'financial', name: 'Financial Data', icon: 'fas fa-dollar-sign' },
    { id: 'resources', name: 'Resource Allocation', icon: 'fas fa-users' },
    { id: 'cost-tracking', name: 'Cost Tracking', icon: 'fas fa-chart-line' },
    { id: 'budget-sync', name: 'Budget Sync', icon: 'fas fa-sync-alt' }
  ];

  const renderOverview = () => (
    <div className="erp-overview">
      <div className="connection-status">
        <div className="status-header">
          <i className="fas fa-chart-bar text-green-500"></i>
          <div>
            <h5>ERP Connection Status</h5>
            <p>Ready to configure ERP integration</p>
          </div>
          <div className="status-indicator available">
            <i className="fas fa-circle"></i>
            <span>Available</span>
          </div>
        </div>
      </div>

      <div className="erp-features">
        <h6>ERP Integration Features</h6>
        <div className="features-grid">
          <div className="feature-card">
            <i className="fas fa-dollar-sign text-green-500"></i>
            <h7>Financial Data Exchange</h7>
            <p>Sync CAPEX, OPEX, and revenue data with ERP systems</p>
          </div>
          <div className="feature-card">
            <i className="fas fa-users text-blue-500"></i>
            <h7>Resource Allocation</h7>
            <p>Import/export resource planning and allocation data</p>
          </div>
          <div className="feature-card">
            <i className="fas fa-chart-line text-purple-500"></i>
            <h7>Cost Tracking</h7>
            <p>Real-time cost tracking and budget monitoring</p>
          </div>
          <div className="feature-card">
            <i className="fas fa-sync-alt text-orange-500"></i>
            <h7>Budget Synchronization</h7>
            <p>Bi-directional budget data synchronization</p>
          </div>
        </div>
      </div>

      <div className="setup-instructions">
        <h6>Setup Instructions</h6>
        <div className="instruction-steps">
          <div className="step">
            <span className="step-number">1</span>
            <div className="step-content">
              <h7>Configure ERP Connection</h7>
              <p>Set up connection parameters for your ERP system</p>
            </div>
          </div>
          <div className="step">
            <span className="step-number">2</span>
            <div className="step-content">
              <h7>Map Data Fields</h7>
              <p>Configure field mappings between systems</p>
            </div>
          </div>
          <div className="step">
            <span className="step-number">3</span>
            <div className="step-content">
              <h7>Test Integration</h7>
              <p>Perform test data exchange to verify setup</p>
            </div>
          </div>
          <div className="step">
            <span className="step-number">4</span>
            <div className="step-content">
              <h7>Enable Sync</h7>
              <p>Activate automatic data synchronization</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderFinancial = () => (
    <div className="erp-financial">
      <div className="section-header">
        <h6>Financial Data Integration</h6>
        <p>Configure financial data exchange with ERP systems</p>
      </div>

      <div className="data-types">
        <div className="data-type-card">
          <div className="card-header">
            <i className="fas fa-building text-blue-500"></i>
            <h7>CAPEX Data</h7>
          </div>
          <p>Capital expenditure data synchronization</p>
          <div className="card-actions">
            <button className="btn btn-outline">Configure</button>
          </div>
        </div>

        <div className="data-type-card">
          <div className="card-header">
            <i className="fas fa-chart-line text-green-500"></i>
            <h7>OPEX Data</h7>
          </div>
          <p>Operating expenditure data synchronization</p>
          <div className="card-actions">
            <button className="btn btn-outline">Configure</button>
          </div>
        </div>

        <div className="data-type-card">
          <div className="card-header">
            <i className="fas fa-dollar-sign text-purple-500"></i>
            <h7>Revenue Data</h7>
          </div>
          <p>Revenue and income data synchronization</p>
          <div className="card-actions">
            <button className="btn btn-outline">Configure</button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="erp-integration">
      {/* Tab Navigation */}
      <div className="integration-tabs">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <i className={tab.icon}></i>
            <span>{tab.name}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'financial' && renderFinancial()}
        {activeTab === 'resources' && (
          <div className="placeholder-content">
            <i className="fas fa-users text-blue-500"></i>
            <h6>Resource Allocation Integration</h6>
            <p>Configure resource allocation data exchange with ERP systems</p>
            <button className="btn btn-primary">Configure Integration</button>
          </div>
        )}
        {activeTab === 'cost-tracking' && (
          <div className="placeholder-content">
            <i className="fas fa-chart-line text-purple-500"></i>
            <h6>Cost Tracking Integration</h6>
            <p>Set up real-time cost tracking with ERP systems</p>
            <button className="btn btn-primary">Configure Integration</button>
          </div>
        )}
        {activeTab === 'budget-sync' && (
          <div className="placeholder-content">
            <i className="fas fa-sync-alt text-orange-500"></i>
            <h6>Budget Synchronization</h6>
            <p>Configure bi-directional budget data synchronization</p>
            <button className="btn btn-primary">Configure Integration</button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ErpIntegration;
