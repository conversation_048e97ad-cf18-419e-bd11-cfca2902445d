/**
 * Database Configuration
 * Manages database settings for hybrid JSON/MongoDB approach
 */

const path = require('path');

class DatabaseConfig {
    constructor() {
        this.environment = process.env.NODE_ENV || 'development';
        this.config = this.getConfig();
    }

    getConfig() {
        const baseConfig = {
            // Data directory for JSON files
            dataDir: path.join(__dirname, 'data'),
            
            // Audit logging settings
            auditLog: {
                enabled: true,
                maxEntries: 1000, // For JSON mode
                retention: '30d'   // For MongoDB mode
            },
            
            // Performance settings
            performance: {
                cacheEnabled: true,
                cacheTimeout: 300000, // 5 minutes
                batchSize: 100
            }
        };

        // Environment-specific configurations
        const configs = {
            development: {
                ...baseConfig,
                mode: 'json',
                mongodb: {
                    url: 'mongodb://localhost:27017',
                    dbName: 'master_bc_dev',
                    options: {
                        useUnifiedTopology: true,
                        serverSelectionTimeoutMS: 5000,
                        connectTimeoutMS: 10000,
                        maxPoolSize: 10
                    }
                }
            },

            test: {
                ...baseConfig,
                mode: 'json',
                dataDir: path.join(__dirname, 'test-data'),
                mongodb: {
                    url: 'mongodb://localhost:27017',
                    dbName: 'master_bc_test',
                    options: {
                        useUnifiedTopology: true,
                        serverSelectionTimeoutMS: 5000,
                        connectTimeoutMS: 10000,
                        maxPoolSize: 5
                    }
                }
            },

            staging: {
                ...baseConfig,
                mode: process.env.DB_MODE || 'mongodb',
                mongodb: {
                    url: process.env.MONGO_URL || 'mongodb://localhost:27017',
                    dbName: process.env.MONGO_DB_NAME || 'master_bc_staging',
                    options: {
                        useUnifiedTopology: true,
                        serverSelectionTimeoutMS: 10000,
                        connectTimeoutMS: 20000,
                        maxPoolSize: 20,
                        retryWrites: true,
                        w: 'majority'
                    }
                }
            },

            production: {
                ...baseConfig,
                mode: process.env.DB_MODE || 'mongodb',
                mongodb: {
                    url: process.env.MONGO_URL || 'mongodb://localhost:27017',
                    dbName: process.env.MONGO_DB_NAME || 'master_business_case',
                    options: {
                        useUnifiedTopology: true,
                        serverSelectionTimeoutMS: 30000,
                        connectTimeoutMS: 30000,
                        socketTimeoutMS: 45000,
                        maxPoolSize: 50,
                        minPoolSize: 5,
                        retryWrites: true,
                        w: 'majority',
                        readPreference: 'primary',
                        ssl: process.env.MONGO_SSL === 'true',
                        authSource: process.env.MONGO_AUTH_SOURCE || 'admin'
                    }
                },
                performance: {
                    cacheEnabled: true,
                    cacheTimeout: 600000, // 10 minutes
                    batchSize: 500
                }
            }
        };

        return configs[this.environment] || configs.development;
    }

    /**
     * Get database mode based on environment and configuration
     */
    getDatabaseMode() {
        // Allow override via environment variable
        if (process.env.DB_MODE) {
            return process.env.DB_MODE;
        }

        // Default modes by environment
        const defaultModes = {
            development: 'json',
            test: 'json',
            staging: 'mongodb',
            production: 'mongodb'
        };

        return defaultModes[this.environment] || 'json';
    }

    /**
     * Get MongoDB connection configuration
     */
    getMongoConfig() {
        return this.config.mongodb;
    }

    /**
     * Get JSON storage configuration
     */
    getJSONConfig() {
        return {
            dataDir: this.config.dataDir,
            auditLog: this.config.auditLog
        };
    }

    /**
     * Validate configuration
     */
    validate() {
        const errors = [];

        // Validate MongoDB configuration if in MongoDB mode
        if (this.config.mode === 'mongodb') {
            if (!this.config.mongodb.url) {
                errors.push('MongoDB URL is required for MongoDB mode');
            }
            if (!this.config.mongodb.dbName) {
                errors.push('MongoDB database name is required for MongoDB mode');
            }
        }

        // Validate JSON configuration if in JSON mode
        if (this.config.mode === 'json') {
            if (!this.config.dataDir) {
                errors.push('Data directory is required for JSON mode');
            }
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * Get complete configuration
     */
    getFullConfig() {
        return {
            ...this.config,
            mode: this.getDatabaseMode(),
            environment: this.environment,
            validation: this.validate()
        };
    }

    /**
     * Print configuration summary
     */
    printSummary() {
        const mode = this.getDatabaseMode();
        console.log('\n🔧 Database Configuration Summary');
        console.log('=====================================');
        console.log(`Environment: ${this.environment}`);
        console.log(`Database Mode: ${mode.toUpperCase()}`);
        
        if (mode === 'mongodb') {
            console.log(`MongoDB URL: ${this.config.mongodb.url}`);
            console.log(`Database Name: ${this.config.mongodb.dbName}`);
            console.log(`Connection Pool: ${this.config.mongodb.options.maxPoolSize} max connections`);
        } else {
            console.log(`Data Directory: ${this.config.dataDir}`);
            console.log(`Audit Log: ${this.config.auditLog.enabled ? 'Enabled' : 'Disabled'}`);
        }
        
        console.log(`Cache: ${this.config.performance.cacheEnabled ? 'Enabled' : 'Disabled'}`);
        console.log('=====================================\n');
    }
}

// Export singleton instance
module.exports = new DatabaseConfig();
