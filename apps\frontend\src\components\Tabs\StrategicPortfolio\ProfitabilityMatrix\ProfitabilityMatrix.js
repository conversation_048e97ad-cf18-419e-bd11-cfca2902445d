import React, { useState } from 'react';
import './ProfitabilityMatrix.css';

const ProfitabilityMatrix = ({ masterBCs, programs }) => {
  const [sortBy, setSortBy] = useState('npv');
  const [filterBy, setFilterBy] = useState('all');
  const [viewType, setViewType] = useState('masterbc');

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount || 0);
  };

  const formatPercentage = (value) => {
    return `${(value || 0).toFixed(1)}%`;
  };

  const getProfitabilityStatus = (npv, investment) => {
    if (!npv || !investment) return 'unknown';
    const profitability = (npv / investment) * 100;
    if (profitability > 20) return 'excellent';
    if (profitability > 10) return 'good';
    if (profitability > 0) return 'marginal';
    return 'loss';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'excellent': return '#10b981';
      case 'good': return '#3b82f6';
      case 'marginal': return '#f59e0b';
      case 'loss': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'excellent': return 'Excellent (>20%)';
      case 'good': return 'Good (10-20%)';
      case 'marginal': return 'Marginal (0-10%)';
      case 'loss': return 'Loss (<0%)';
      default: return 'Unknown';
    }
  };

  // Filter and sort Master BCs
  const filteredMasterBCs = masterBCs
    .filter(mbc => {
      if (filterBy === 'all') return true;
      const status = getProfitabilityStatus(
        mbc.aggregatedMetrics?.totalNPV,
        mbc.aggregatedMetrics?.totalInvestment
      );
      return status === filterBy;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'npv':
          return (b.aggregatedMetrics?.totalNPV || 0) - (a.aggregatedMetrics?.totalNPV || 0);
        case 'investment':
          return (b.aggregatedMetrics?.totalInvestment || 0) - (a.aggregatedMetrics?.totalInvestment || 0);
        case 'irr':
          return (b.aggregatedMetrics?.avgIRR || 0) - (a.aggregatedMetrics?.avgIRR || 0);
        case 'name':
          return a.name.localeCompare(b.name);
        default:
          return 0;
      }
    });

  // Filter and sort Programs
  const filteredPrograms = programs
    .filter(program => program.linkedMasterBC)
    .sort((a, b) => {
      switch (sortBy) {
        case 'npv':
          return (b.linkedMasterBCMetrics?.totalNPV || 0) - (a.linkedMasterBCMetrics?.totalNPV || 0);
        case 'investment':
          return (b.linkedMasterBCMetrics?.totalInvestment || 0) - (a.linkedMasterBCMetrics?.totalInvestment || 0);
        case 'irr':
          return (b.linkedMasterBCMetrics?.avgIRR || 0) - (a.linkedMasterBCMetrics?.avgIRR || 0);
        case 'name':
          return a.name.localeCompare(b.name);
        default:
          return 0;
      }
    });

  const currentData = viewType === 'masterbc' ? filteredMasterBCs : filteredPrograms;

  return (
    <div className="profitability-matrix">
      {/* Header */}
      <div className="matrix-header">
        <div className="header-content">
          <h2 className="matrix-title">Profitability Matrix</h2>
          <p className="matrix-subtitle">
            Analyze financial performance and profitability across your portfolio
          </p>
        </div>
      </div>

      {/* Controls */}
      <div className="matrix-controls">
        <div className="view-toggle">
          <button
            className={`toggle-btn ${viewType === 'masterbc' ? 'active' : ''}`}
            onClick={() => setViewType('masterbc')}
          >
            <i className="fas fa-sitemap"></i>
            Master Business Cases ({masterBCs.length})
          </button>
          <button
            className={`toggle-btn ${viewType === 'programs' ? 'active' : ''}`}
            onClick={() => setViewType('programs')}
          >
            <i className="fas fa-project-diagram"></i>
            Programs ({filteredPrograms.length})
          </button>
        </div>

        <div className="filter-controls">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="control-select"
          >
            <option value="npv">Sort by NPV</option>
            <option value="investment">Sort by Investment</option>
            <option value="irr">Sort by IRR</option>
            <option value="name">Sort by Name</option>
          </select>

          {viewType === 'masterbc' && (
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value)}
              className="control-select"
            >
              <option value="all">All Status</option>
              <option value="excellent">Excellent (&gt;20%)</option>
              <option value="good">Good (10-20%)</option>
              <option value="marginal">Marginal (0-10%)</option>
              <option value="loss">Loss (&lt;0%)</option>
            </select>
          )}
        </div>
      </div>

      {/* Profitability Legend */}
      <div className="profitability-legend">
        <h4>Profitability Status Legend</h4>
        <div className="legend-items">
          {['excellent', 'good', 'marginal', 'loss'].map(status => (
            <div key={status} className="legend-item">
              <div 
                className="legend-color" 
                style={{ backgroundColor: getStatusColor(status) }}
              ></div>
              <span>{getStatusLabel(status)}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Matrix Table */}
      <div className="matrix-table-container">
        <table className="matrix-table">
          <thead>
            <tr>
              <th>Name</th>
              <th>Category</th>
              <th>Investment</th>
              <th>NPV</th>
              <th>IRR</th>
              <th>Profitability</th>
              <th>Status</th>
              {viewType === 'programs' && <th>Linked Master BC</th>}
            </tr>
          </thead>
          <tbody>
            {currentData.map((item) => {
              const metrics = viewType === 'masterbc' 
                ? item.aggregatedMetrics 
                : item.linkedMasterBCMetrics;
              
              const status = getProfitabilityStatus(
                metrics?.totalNPV,
                metrics?.totalInvestment
              );
              
              const profitability = metrics?.totalNPV && metrics?.totalInvestment
                ? (metrics.totalNPV / metrics.totalInvestment) * 100
                : 0;

              return (
                <tr key={item.id} className={`status-${status}`}>
                  <td className="name-cell">
                    <div className="name-content">
                      <div className="item-name">{item.name}</div>
                      <div className="item-description">{item.description}</div>
                    </div>
                  </td>
                  <td className="category-cell">
                    <span className="category-badge">
                      {item.category || 'Uncategorized'}
                    </span>
                  </td>
                  <td className="investment-cell">
                    {formatCurrency(metrics?.totalInvestment)}
                  </td>
                  <td className="npv-cell">
                    <span className={`npv-value ${(metrics?.totalNPV || 0) >= 0 ? 'positive' : 'negative'}`}>
                      {formatCurrency(metrics?.totalNPV)}
                    </span>
                  </td>
                  <td className="irr-cell">
                    {formatPercentage(metrics?.avgIRR)}
                  </td>
                  <td className="profitability-cell">
                    <span className={`profitability-value ${profitability >= 0 ? 'positive' : 'negative'}`}>
                      {formatPercentage(profitability)}
                    </span>
                  </td>
                  <td className="status-cell">
                    <div className="status-indicator">
                      <div 
                        className="status-dot" 
                        style={{ backgroundColor: getStatusColor(status) }}
                      ></div>
                      <span className="status-text">{getStatusLabel(status).split(' ')[0]}</span>
                    </div>
                  </td>
                  {viewType === 'programs' && (
                    <td className="linked-bc-cell">
                      {item.linkedMasterBC ? (
                        <span className="linked-bc-name">
                          {masterBCs.find(mbc => mbc.id === item.linkedMasterBC)?.name || 'Unknown'}
                        </span>
                      ) : (
                        <span className="no-link">Not Linked</span>
                      )}
                    </td>
                  )}
                </tr>
              );
            })}
          </tbody>
        </table>

        {currentData.length === 0 && (
          <div className="empty-state">
            <i className="fas fa-chart-bar"></i>
            <h3>No Data Available</h3>
            <p>
              {viewType === 'masterbc' 
                ? 'No Master Business Cases match the current filter criteria.'
                : 'No Programs with linked Master Business Cases found.'
              }
            </p>
          </div>
        )}
      </div>

      {/* Summary Stats */}
      {currentData.length > 0 && (
        <div className="matrix-summary">
          <h4>Portfolio Summary</h4>
          <div className="summary-stats">
            <div className="summary-stat">
              <span className="stat-label">Total Items:</span>
              <span className="stat-value">{currentData.length}</span>
            </div>
            <div className="summary-stat">
              <span className="stat-label">Total Investment:</span>
              <span className="stat-value">
                {formatCurrency(
                  currentData.reduce((sum, item) => {
                    const metrics = viewType === 'masterbc' 
                      ? item.aggregatedMetrics 
                      : item.linkedMasterBCMetrics;
                    return sum + (metrics?.totalInvestment || 0);
                  }, 0)
                )}
              </span>
            </div>
            <div className="summary-stat">
              <span className="stat-label">Total NPV:</span>
              <span className="stat-value">
                {formatCurrency(
                  currentData.reduce((sum, item) => {
                    const metrics = viewType === 'masterbc' 
                      ? item.aggregatedMetrics 
                      : item.linkedMasterBCMetrics;
                    return sum + (metrics?.totalNPV || 0);
                  }, 0)
                )}
              </span>
            </div>
            <div className="summary-stat">
              <span className="stat-label">Average IRR:</span>
              <span className="stat-value">
                {formatPercentage(
                  currentData.reduce((sum, item) => {
                    const metrics = viewType === 'masterbc' 
                      ? item.aggregatedMetrics 
                      : item.linkedMasterBCMetrics;
                    return sum + (metrics?.avgIRR || 0);
                  }, 0) / currentData.length
                )}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfitabilityMatrix;
