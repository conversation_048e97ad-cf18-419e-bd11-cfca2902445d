/* Pulseboard Stats Styles */
.pulseboard-stats {
  padding: 24px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Header */
.stats-header {
  text-align: center;
  margin-bottom: 32px;
}

.stats-header h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
}

.stats-header p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.linking-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

/* Metric Cards */
.metric-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #3b82f6;
  transition: all 0.2s;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.metric-card.blue { border-left-color: #3b82f6; }
.metric-card.green { border-left-color: #10b981; }
.metric-card.purple { border-left-color: #8b5cf6; }
.metric-card.orange { border-left-color: #f59e0b; }
.metric-card.teal { border-left-color: #14b8a6; }
.metric-card.indigo { border-left-color: #6366f1; }
.metric-card.yellow { border-left-color: #eab308; }

.metric-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: #f3f4f6;
  border-radius: 8px;
  color: #6b7280;
  font-size: 20px;
}

.metric-card.blue .metric-icon { background: #eff6ff; color: #3b82f6; }
.metric-card.green .metric-icon { background: #ecfdf5; color: #10b981; }
.metric-card.purple .metric-icon { background: #f3e8ff; color: #8b5cf6; }
.metric-card.orange .metric-icon { background: #fffbeb; color: #f59e0b; }
.metric-card.teal .metric-icon { background: #f0fdfa; color: #14b8a6; }
.metric-card.indigo .metric-icon { background: #eef2ff; color: #6366f1; }
.metric-card.yellow .metric-icon { background: #fefce8; color: #eab308; }

.metric-info h3 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.metric-subtitle {
  font-size: 12px;
  color: #9ca3af;
}

/* Linking Statistics */
.linking-stats {
  margin-bottom: 32px;
}

.linking-stats h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

/* Charts Grid */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.chart-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Distribution Charts */
.distribution-chart h4 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.chart-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.chart-item {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 12px;
  align-items: center;
}

.chart-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-indicator {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.chart-indicator.gray { background: #6b7280; }
.chart-indicator.blue { background: #3b82f6; }
.chart-indicator.yellow { background: #eab308; }
.chart-indicator.green { background: #10b981; }
.chart-indicator.red { background: #ef4444; }
.chart-indicator.purple { background: #8b5cf6; }
.chart-indicator.orange { background: #f59e0b; }
.chart-indicator.indigo { background: #6366f1; }
.chart-indicator.teal { background: #14b8a6; }
.chart-indicator.default { background: #d1d5db; }

.label-text {
  font-size: 14px;
  color: #374151;
  text-transform: capitalize;
}

.chart-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  min-width: 30px;
  text-align: right;
}

.chart-bar {
  grid-column: 1 / -1;
  height: 6px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
  margin-top: 4px;
}

.chart-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.chart-fill.gray { background: #6b7280; }
.chart-fill.blue { background: #3b82f6; }
.chart-fill.yellow { background: #eab308; }
.chart-fill.green { background: #10b981; }
.chart-fill.red { background: #ef4444; }
.chart-fill.purple { background: #8b5cf6; }
.chart-fill.orange { background: #f59e0b; }
.chart-fill.indigo { background: #6366f1; }
.chart-fill.teal { background: #14b8a6; }
.chart-fill.default { background: #d1d5db; }

/* Insights Section */
.insights-section {
  margin-bottom: 32px;
}

.insights-section h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.insight-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  border: 1px solid #e5e7eb;
}

.insight-card h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.insight-card p {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  text-transform: capitalize;
}

/* Export Section */
.export-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.export-section h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.export-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.export-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.export-btn.excel {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.export-btn.excel:hover {
  background: #a7f3d0;
}

.export-btn.pdf {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.export-btn.pdf:hover {
  background: #fecaca;
}

.export-btn.dashboard {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #bfdbfe;
}

.export-btn.dashboard:hover {
  background: #bfdbfe;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pulseboard-stats {
    padding: 16px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .linking-grid {
    grid-template-columns: 1fr;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .insights-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .export-buttons {
    flex-direction: column;
  }
  
  .export-btn {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .insights-grid {
    grid-template-columns: 1fr;
  }
  
  .metric-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .chart-item {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .chart-value {
    text-align: left;
  }
}
