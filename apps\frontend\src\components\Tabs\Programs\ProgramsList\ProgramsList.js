import React, { useState, useMemo } from 'react';
import './ProgramsList.css';

const ProgramsList = ({ 
  programs, 
  masterBusinessCases,
  onCreateNew, 
  onEdit, 
  onViewDetail, 
  onLinkMasterBC,
  onUnlinkMasterBC
}) => {
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    priority: '',
    linkedMasterBC: ''
  });
  const [sortConfig, setSortConfig] = useState({ key: 'name', direction: 'asc' });
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Filter and sort data
  const filteredAndSortedData = useMemo(() => {
    let filtered = programs.filter(program => {
      const matchesSearch = !filters.search || 
        program.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        program.description.toLowerCase().includes(filters.search.toLowerCase());
      
      const matchesStatus = !filters.status || program.status === filters.status;
      const matchesPriority = !filters.priority || program.priority === filters.priority;
      const matchesLinkedMasterBC = !filters.linkedMasterBC || 
        (filters.linkedMasterBC === 'linked' && program.linkedMasterBC) ||
        (filters.linkedMasterBC === 'unlinked' && !program.linkedMasterBC);

      return matchesSearch && matchesStatus && matchesPriority && matchesLinkedMasterBC;
    });

    // Sort data
    if (sortConfig.key) {
      filtered.sort((a, b) => {
        let aValue = a[sortConfig.key];
        let bValue = b[sortConfig.key];

        if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return filtered;
  }, [programs, filters, sortConfig]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedData = filteredAndSortedData.slice(startIndex, startIndex + itemsPerPage);

  const handleSort = (key) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount || 0);
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      active: 'status-badge status-active',
      planning: 'status-badge status-planning',
      'on hold': 'status-badge status-on-hold',
      completed: 'status-badge status-completed',
      cancelled: 'status-badge status-cancelled'
    };
    return statusClasses[status?.toLowerCase()] || 'status-badge status-planning';
  };

  const getPriorityBadge = (priority) => {
    const priorityClasses = {
      high: 'priority-badge priority-high',
      medium: 'priority-badge priority-medium',
      low: 'priority-badge priority-low'
    };
    return priorityClasses[priority?.toLowerCase()] || 'priority-badge priority-medium';
  };

  const getMasterBCName = (linkedMasterBC) => {
    if (!linkedMasterBC) return null;
    const masterBC = masterBusinessCases.find(mbc => mbc.id === linkedMasterBC);
    return masterBC ? masterBC.name : 'Unknown Master BC';
  };

  // Get unique values for filter dropdowns
  const uniqueStatuses = [...new Set(programs.map(p => p.status))];
  const uniquePriorities = [...new Set(programs.map(p => p.priority))];

  return (
    <div className="programs-list">
      {/* Header with Create Button */}
      <div className="list-header">
        <div className="header-info">
          <h3>Programs ({filteredAndSortedData.length})</h3>
          <p>Program management with Master Business Case integration</p>
        </div>
        <button className="btn btn-primary" onClick={onCreateNew}>
          <i className="fas fa-plus"></i>
          Create Program
        </button>
      </div>

      {/* Filters */}
      <div className="filters-section">
        <div className="filters-row">
          <div className="filter-group">
            <label>Search</label>
            <input
              type="text"
              placeholder="Search by name or description..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="filter-input"
            />
          </div>
          
          <div className="filter-group">
            <label>Status</label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="filter-select"
            >
              <option value="">All Statuses</option>
              {uniqueStatuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>Priority</label>
            <select
              value={filters.priority}
              onChange={(e) => handleFilterChange('priority', e.target.value)}
              className="filter-select"
            >
              <option value="">All Priorities</option>
              {uniquePriorities.map(priority => (
                <option key={priority} value={priority}>{priority}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>Master BC Link</label>
            <select
              value={filters.linkedMasterBC}
              onChange={(e) => handleFilterChange('linkedMasterBC', e.target.value)}
              className="filter-select"
            >
              <option value="">All Programs</option>
              <option value="linked">Linked to Master BC</option>
              <option value="unlinked">Not Linked</option>
            </select>
          </div>
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="summary-stats">
        <div className="stat-card">
          <div className="stat-value">{filteredAndSortedData.length}</div>
          <div className="stat-label">Total Programs</div>
        </div>
        <div className="stat-card">
          <div className="stat-value">
            {filteredAndSortedData.filter(p => p.linkedMasterBC).length}
          </div>
          <div className="stat-label">Linked to Master BC</div>
        </div>
        <div className="stat-card">
          <div className="stat-value">
            {formatCurrency(filteredAndSortedData.reduce((sum, p) => sum + (p.budget || 0), 0))}
          </div>
          <div className="stat-label">Total Budget</div>
        </div>
        <div className="stat-card">
          <div className="stat-value">
            {filteredAndSortedData.filter(p => p.status === 'active').length}
          </div>
          <div className="stat-label">Active Programs</div>
        </div>
      </div>

      {/* Data Table */}
      <div className="table-container">
        <table className="programs-table">
          <thead>
            <tr>
              <th onClick={() => handleSort('name')} className="sortable">
                Name {sortConfig.key === 'name' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
              </th>
              <th onClick={() => handleSort('owner')} className="sortable">
                Owner {sortConfig.key === 'owner' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
              </th>
              <th>
                Master BC
              </th>
              <th>
                Projects/Epics
              </th>
              <th>
                Business Cases
              </th>
              <th onClick={() => handleSort('aggregatedMetrics.totalInvestment')} className="sortable">
                Investment {sortConfig.key === 'aggregatedMetrics.totalInvestment' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
              </th>
              <th onClick={() => handleSort('aggregatedMetrics.totalNPV')} className="sortable">
                NPV {sortConfig.key === 'aggregatedMetrics.totalNPV' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
              </th>
              <th onClick={() => handleSort('aggregatedMetrics.avgIRR')} className="sortable">
                Avg IRR {sortConfig.key === 'aggregatedMetrics.avgIRR' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
              </th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {paginatedData.map(program => (
              <tr key={program.id}>
                <td>
                  <div className="name-cell">
                    <strong>{program.name}</strong>
                    <div className="description-preview">{program.description}</div>
                  </div>
                </td>
                <td>{program.owner}</td>
                <td>
                  {program.connectedMasterBC ? (
                    <div className="connected-master-bc">
                      <div className="master-bc-name">
                        <i className="fas fa-briefcase text-purple-500"></i>
                        <span className="ml-1">{program.connectedMasterBC.name}</span>
                      </div>
                      <div className="master-bc-unit text-sm text-gray-500">
                        {program.connectedMasterBC.businessUnit}
                      </div>
                    </div>
                  ) : (
                    <span className="text-gray-400">No Master BC linked</span>
                  )}
                </td>
                <td>
                  {program.connectedProjects && program.connectedProjects.length > 0 ? (
                    <div className="connected-projects">
                      {program.connectedProjects.slice(0, 2).map(proj => (
                        <div key={proj.id} className="project-item">
                          <i className={`fas ${proj.type === 'epic' ? 'fa-layer-group' : 'fa-project-diagram'} text-green-500`}></i>
                          <span className="ml-1">{proj.name}</span>
                          <span className="text-xs text-gray-500">({proj.type})</span>
                        </div>
                      ))}
                      {program.connectedProjects.length > 2 && (
                        <div className="text-xs text-gray-500">
                          +{program.connectedProjects.length - 2} more
                        </div>
                      )}
                    </div>
                  ) : (
                    <span className="text-gray-400">No projects linked</span>
                  )}
                </td>
                <td>
                  {program.connectedBusinessCases && program.connectedBusinessCases.length > 0 ? (
                    <div className="connected-business-cases">
                      {program.connectedBusinessCases.slice(0, 2).map(bc => (
                        <div key={bc.id} className="bc-item">
                          <i className="fas fa-briefcase text-blue-500"></i>
                          <span className="ml-1">{bc.name}</span>
                        </div>
                      ))}
                      {program.connectedBusinessCases.length > 2 && (
                        <div className="text-xs text-gray-500">
                          +{program.connectedBusinessCases.length - 2} more
                        </div>
                      )}
                    </div>
                  ) : (
                    <span className="text-gray-400">No BCs linked</span>
                  )}
                </td>
                <td className="text-right">
                  {formatCurrency(program.aggregatedMetrics?.totalInvestment || 0)}
                </td>
                <td className="text-right">
                  {formatCurrency(program.aggregatedMetrics?.totalNPV || 0)}
                </td>
                <td className="text-right">
                  {program.aggregatedMetrics?.avgIRR ? `${program.aggregatedMetrics.avgIRR.toFixed(1)}%` : '-'}
                </td>
                <td>
                  <div className="action-buttons">
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={() => onViewDetail(program)}
                      title="View Details"
                    >
                      <i className="fas fa-eye"></i>
                    </button>
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={() => onEdit(program)}
                      title="Edit"
                    >
                      <i className="fas fa-edit"></i>
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="pagination-section">
        <div className="pagination-info">
          Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredAndSortedData.length)} of {filteredAndSortedData.length} entries
        </div>
        
        <div className="pagination-controls">
          <select
            value={itemsPerPage}
            onChange={(e) => {
              setItemsPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
            className="items-per-page"
          >
            <option value={10}>10 per page</option>
            <option value={25}>25 per page</option>
            <option value={50}>50 per page</option>
            <option value={100}>100 per page</option>
          </select>

          <div className="page-buttons">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="btn btn-sm btn-outline"
            >
              Previous
            </button>
            
            <span className="page-info">
              Page {currentPage} of {totalPages}
            </span>
            
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="btn btn-sm btn-outline"
            >
              Next
            </button>
          </div>
        </div>
      </div>

      {/* Empty State */}
      {filteredAndSortedData.length === 0 && (
        <div className="empty-state">
          <i className="fas fa-layer-group"></i>
          <h3>No Programs Found</h3>
          <p>
            {programs.length === 0 
              ? "Get started by creating your first Program"
              : "Try adjusting your filters to see more results"
            }
          </p>
          {programs.length === 0 && (
            <button className="btn btn-primary" onClick={onCreateNew}>
              Create First Program
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default ProgramsList;
