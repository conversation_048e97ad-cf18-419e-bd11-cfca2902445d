import React, { useState } from 'react';
import './ProcessFlow.css';

const ProcessFlow = () => {
  const [activeStage, setActiveStage] = useState(null);
  const [selectedProcess, setSelectedProcess] = useState('request-tracking');
  const [processProgress, setProcessProgress] = useState({});
  const [showProgressTracker, setShowProgressTracker] = useState(false);

  // Define the comprehensive process flow stages
  const processStages = {
    'request-tracking': {
      name: 'Request Tracking & Validation Process',
      description: 'Comprehensive request lifecycle management from initiation to execution',
      stages: [
        {
          id: 'funnel-step1',
          name: 'FUNNEL',
          title: 'Request Initiation',
          description: 'Initial request creation and basic validation',
          color: '#e8f5e9',
          steps: [
            {
              id: 'step1',
              title: 'Create the EPIC in PPM as capability portfolio folder',
              description: 'Establish the foundational structure in Project Portfolio Management',
              inputs: ['Business need identification', 'Strategic alignment confirmation'],
              outputs: ['EPIC folder created', 'Initial capability framework', 'Portfolio structure established'],
              checklist: [
                'Verify business need is clearly defined',
                'Confirm strategic alignment with organizational goals',
                'Validate capability requirements',
                'Ensure proper folder structure in PPM'
              ],
              owner: 'Capability team multiple selection',
              stakeholders: ['Product Architects', 'Portfolio team', 'Business team'],
              timeline: 'As per business need',
              deliverables: ['EPIC folder in PPM', 'Capability framework document']
            }
          ]
        },
        {
          id: 'backlog-step2',
          name: 'BACKLOG',
          title: 'Requirements Analysis',
          description: 'Detailed analysis and backlog creation',
          color: '#fff3e0',
          steps: [
            {
              id: 'step2',
              title: 'Confirm the additions to the backlog - Fast technique',
              description: 'Rapid validation and backlog prioritization',
              inputs: ['EPIC structure', 'Business requirements', 'Stakeholder input'],
              outputs: ['Validated backlog items', 'Priority matrix', 'Resource estimates'],
              checklist: [
                'Validate all Epic Owners',
                'Create a capability PPM team',
                'Confirm Epic Owners',
                'Establish development/deployment timeline'
              ],
              owner: 'Product Owner & Technical leaders',
              stakeholders: ['Architects', 'Technical leaders', 'Business stakeholders'],
              timeline: 'Sprint reviews, Scrum master & Product Owners',
              deliverables: ['Prioritized backlog', 'Resource allocation plan']
            }
          ]
        },
        {
          id: 'execution-step3',
          name: 'EXECUTION',
          title: 'Implementation & Delivery',
          description: 'Execution phase with monitoring and delivery',
          color: '#e3f2fd',
          steps: [
            {
              id: 'step3-1',
              title: 'Refactor the backlog - Fast technique',
              description: 'Optimize and refine backlog for execution',
              inputs: ['Validated backlog', 'Technical analysis', 'Resource availability'],
              outputs: ['Refined backlog', 'Sprint planning', 'Technical specifications'],
              checklist: [
                'Review and refactor backlog items',
                'Ensure technical feasibility',
                'Validate resource allocation',
                'Confirm delivery timelines'
              ],
              owner: 'Product Owner & Technical leaders',
              stakeholders: ['Development team', 'Architects', 'QA team'],
              timeline: 'Sprint planning cycles',
              deliverables: ['Refined backlog', 'Sprint plans', 'Technical specs']
            },
            {
              id: 'step3-2',
              title: 'Move into Production backlog',
              description: 'Transition to production-ready state',
              inputs: ['Refined backlog', 'Development completion', 'Testing results'],
              outputs: ['Production-ready features', 'Deployment plan', 'Go-live strategy'],
              checklist: [
                'Complete development and testing',
                'Validate production readiness',
                'Ensure deployment procedures',
                'Confirm rollback strategies'
              ],
              owner: 'Product Owner & Technical leaders',
              stakeholders: ['DevOps team', 'Production support', 'Business users'],
              timeline: 'Production deployment cycles',
              deliverables: ['Production deployment', 'Go-live documentation']
            },
            {
              id: 'step3-3',
              title: 'Start the execution',
              description: 'Begin production execution and monitoring',
              inputs: ['Production deployment', 'Monitoring setup', 'Support procedures'],
              outputs: ['Live system', 'Performance metrics', 'User feedback'],
              checklist: [
                'Confirm system is operational',
                'Validate performance metrics',
                'Ensure monitoring is active',
                'Collect initial user feedback'
              ],
              owner: 'Implementation team',
              stakeholders: ['End users', 'Support team', 'Business stakeholders'],
              timeline: 'Ongoing operational cycles',
              deliverables: ['Operational system', 'Performance reports', 'User feedback']
            }
          ]
        }
      ]
    }
  };

  const handleStageClick = (stageId) => {
    setActiveStage(activeStage === stageId ? null : stageId);
  };

  const handleStepProgress = (stepId, completed) => {
    setProcessProgress(prev => ({
      ...prev,
      [stepId]: completed
    }));
  };

  const getStageProgress = (stage) => {
    const totalSteps = stage.steps.length;
    const completedSteps = stage.steps.filter(step => processProgress[step.id]).length;
    return { completed: completedSteps, total: totalSteps, percentage: (completedSteps / totalSteps) * 100 };
  };

  const renderProgressTracker = () => {
    const currentProcess = processStages[selectedProcess];
    
    return (
      <div className="progress-tracker">
        <div className="tracker-header">
          <h4>Process Progress Tracker</h4>
          <button 
            className="close-tracker"
            onClick={() => setShowProgressTracker(false)}
          >
            <i className="fas fa-times"></i>
          </button>
        </div>
        
        <div className="progress-overview">
          {currentProcess.stages.map(stage => {
            const progress = getStageProgress(stage);
            return (
              <div key={stage.id} className="stage-progress">
                <div className="stage-progress-header">
                  <span className="stage-name">{stage.name}</span>
                  <span className="progress-text">{progress.completed}/{progress.total}</span>
                </div>
                <div className="progress-bar">
                  <div 
                    className="progress-fill"
                    style={{ width: `${progress.percentage}%` }}
                  ></div>
                </div>
              </div>
            );
          })}
        </div>
        
        <div className="tracker-actions">
          <button className="action-btn secondary">
            <i className="fas fa-save"></i>
            Save Progress
          </button>
          <button className="action-btn secondary">
            <i className="fas fa-download"></i>
            Export Report
          </button>
        </div>
      </div>
    );
  };

  const renderProcessSelector = () => (
    <div className="process-selector">
      <h3>Select Process Flow</h3>
      <div className="process-options">
        {Object.entries(processStages).map(([key, process]) => (
          <button
            key={key}
            className={`process-option ${selectedProcess === key ? 'active' : ''}`}
            onClick={() => setSelectedProcess(key)}
          >
            <div className="option-content">
              <h4>{process.name}</h4>
              <p>{process.description}</p>
            </div>
          </button>
        ))}
      </div>
    </div>
  );

  const renderStageDetails = (stage) => (
    <div className="stage-details">
      {stage.steps.map(step => (
        <div key={step.id} className="step-detail">
          <div className="step-header">
            <h4>{step.title}</h4>
            <p>{step.description}</p>
          </div>
          
          <div className="step-content">
            <div className="step-section">
              <h5>📥 Inputs</h5>
              <ul>
                {step.inputs.map((input, index) => (
                  <li key={index}>{input}</li>
                ))}
              </ul>
            </div>
            
            <div className="step-section">
              <h5>📤 Outputs</h5>
              <ul>
                {step.outputs.map((output, index) => (
                  <li key={index}>{output}</li>
                ))}
              </ul>
            </div>
            
            <div className="step-section">
              <h5>✅ Checklist</h5>
              <ul className="checklist">
                {step.checklist.map((item, index) => (
                  <li key={index}>
                    <input 
                      type="checkbox" 
                      id={`check-${step.id}-${index}`}
                      onChange={(e) => {
                        // Update progress when checklist items are completed
                        const allCheckboxes = document.querySelectorAll(`input[id^="check-${step.id}"]`);
                        const checkedBoxes = document.querySelectorAll(`input[id^="check-${step.id}"]:checked`);
                        const isStepComplete = checkedBoxes.length === allCheckboxes.length;
                        handleStepProgress(step.id, isStepComplete);
                      }}
                    />
                    <label htmlFor={`check-${step.id}-${index}`}>{item}</label>
                  </li>
                ))}
              </ul>
              <div className="step-progress-indicator">
                <span className={`step-status ${processProgress[step.id] ? 'completed' : 'pending'}`}>
                  <i className={`fas ${processProgress[step.id] ? 'fa-check-circle' : 'fa-clock'}`}></i>
                  {processProgress[step.id] ? 'Completed' : 'Pending'}
                </span>
              </div>
            </div>
            
            <div className="step-metadata">
              <div className="metadata-grid">
                <div className="metadata-item">
                  <span className="metadata-label">👤 Owner:</span>
                  <span className="metadata-value">{step.owner}</span>
                </div>
                <div className="metadata-item">
                  <span className="metadata-label">👥 Stakeholders:</span>
                  <span className="metadata-value">{step.stakeholders.join(', ')}</span>
                </div>
                <div className="metadata-item">
                  <span className="metadata-label">⏱️ Timeline:</span>
                  <span className="metadata-value">{step.timeline}</span>
                </div>
                <div className="metadata-item">
                  <span className="metadata-label">📋 Deliverables:</span>
                  <span className="metadata-value">{step.deliverables.join(', ')}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const currentProcess = processStages[selectedProcess];

  return (
    <div className="process-flow">
      <div className="process-flow-header">
        <h2>Pulseboard Process Flow</h2>
        <p>Comprehensive request tracking and validation processes</p>
      </div>

      {renderProcessSelector()}

      <div className="process-visualization">
        <h3>{currentProcess.name}</h3>
        <p>{currentProcess.description}</p>
        
        <div className="process-stages">
          {currentProcess.stages.map(stage => (
            <div key={stage.id} className="process-stage">
              <div 
                className={`stage-header ${activeStage === stage.id ? 'active' : ''}`}
                style={{ backgroundColor: stage.color }}
                onClick={() => handleStageClick(stage.id)}
              >
                <div className="stage-info">
                  <h4>{stage.name}</h4>
                  <h5>{stage.title}</h5>
                  <p>{stage.description}</p>
                </div>
                <div className="stage-toggle">
                  <i className={`fas ${activeStage === stage.id ? 'fa-chevron-up' : 'fa-chevron-down'}`}></i>
                </div>
              </div>
              
              {activeStage === stage.id && renderStageDetails(stage)}
            </div>
          ))}
        </div>
      </div>

      <div className="process-actions">
        <button className="action-btn primary">
          <i className="fas fa-play"></i>
          Start Process
        </button>
        <button 
          className="action-btn secondary"
          onClick={() => setShowProgressTracker(!showProgressTracker)}
        >
          <i className="fas fa-chart-line"></i>
          {showProgressTracker ? 'Hide' : 'Show'} Progress
        </button>
        <button className="action-btn secondary">
          <i className="fas fa-download"></i>
          Export Process
        </button>
        <button className="action-btn secondary">
          <i className="fas fa-edit"></i>
          Customize Process
        </button>
      </div>

      {showProgressTracker && renderProgressTracker()}
    </div>
  );
};

export default ProcessFlow;
