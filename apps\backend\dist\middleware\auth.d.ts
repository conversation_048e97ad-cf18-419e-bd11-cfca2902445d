import { Request, Response, NextFunction } from 'express';
import { IUser } from '../models/User';
import { UserRole } from '../types';
interface AuthRequest extends Request {
    user?: IUser;
}
export declare const authMiddleware: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void | Response<any, Record<string, any>>>;
export declare const requireRole: (roles: UserRole[]) => (req: AuthRequest, res: Response, next: NextFunction) => void | Response<any, Record<string, any>>;
export {};
//# sourceMappingURL=auth.d.ts.map