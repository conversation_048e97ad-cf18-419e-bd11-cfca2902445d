.profitability-matrix {
  padding: 24px;
  background: #f8fafc;
}

.matrix-header {
  margin-bottom: 24px;
}

.header-content {
  text-align: center;
}

.matrix-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.matrix-subtitle {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
}

/* Controls */
.matrix-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.view-toggle {
  display: flex;
  gap: 4px;
  background: #f1f5f9;
  padding: 4px;
  border-radius: 8px;
}

.toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: transparent;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
}

.toggle-btn.active {
  background: #3b82f6;
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.toggle-btn:hover:not(.active) {
  background: #e2e8f0;
  color: #334155;
}

.filter-controls {
  display: flex;
  gap: 12px;
}

.control-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 0.9rem;
  cursor: pointer;
  transition: border-color 0.2s;
}

.control-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Legend */
.profitability-legend {
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.profitability-legend h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
}

.legend-items {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.legend-item span {
  font-size: 0.9rem;
  color: #374151;
  font-weight: 500;
}

/* Table */
.matrix-table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.matrix-table {
  width: 100%;
  border-collapse: collapse;
}

.matrix-table th {
  background: #f8fafc;
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
  border-bottom: 1px solid #e2e8f0;
}

.matrix-table td {
  padding: 16px 12px;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: top;
}

.matrix-table tr:hover {
  background: #f8fafc;
}

/* Status-based row styling */
.matrix-table tr.status-excellent {
  border-left: 4px solid #10b981;
}

.matrix-table tr.status-good {
  border-left: 4px solid #3b82f6;
}

.matrix-table tr.status-marginal {
  border-left: 4px solid #f59e0b;
}

.matrix-table tr.status-loss {
  border-left: 4px solid #ef4444;
}

/* Cell-specific styling */
.name-cell {
  min-width: 250px;
}

.name-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.item-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.95rem;
}

.item-description {
  font-size: 0.8rem;
  color: #64748b;
  line-height: 1.3;
}

.category-cell {
  min-width: 120px;
}

.category-badge {
  display: inline-block;
  padding: 4px 8px;
  background: #e0e7ff;
  color: #3730a3;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.investment-cell,
.npv-cell {
  text-align: right;
  font-weight: 600;
  min-width: 120px;
}

.npv-value.positive {
  color: #10b981;
}

.npv-value.negative {
  color: #ef4444;
}

.irr-cell {
  text-align: right;
  font-weight: 600;
  color: #3b82f6;
  min-width: 80px;
}

.profitability-cell {
  text-align: right;
  min-width: 100px;
}

.profitability-value {
  font-weight: 600;
  font-size: 0.95rem;
}

.profitability-value.positive {
  color: #10b981;
}

.profitability-value.negative {
  color: #ef4444;
}

.status-cell {
  min-width: 120px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-text {
  font-size: 0.9rem;
  font-weight: 500;
  color: #374151;
}

.linked-bc-cell {
  min-width: 200px;
}

.linked-bc-name {
  font-size: 0.9rem;
  color: #3b82f6;
  font-weight: 500;
}

.no-link {
  font-size: 0.9rem;
  color: #9ca3af;
  font-style: italic;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 16px;
  color: #d1d5db;
}

.empty-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-state p {
  margin: 0;
  font-size: 0.95rem;
}

/* Summary */
.matrix-summary {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.matrix-summary h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.summary-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.stat-label {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

.stat-value {
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .matrix-controls {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .view-toggle {
    justify-content: center;
  }
  
  .filter-controls {
    justify-content: center;
  }
  
  .legend-items {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .profitability-matrix {
    padding: 16px;
  }
  
  .matrix-table-container {
    overflow-x: auto;
  }
  
  .matrix-table {
    min-width: 800px;
  }
  
  .summary-stats {
    grid-template-columns: 1fr;
  }
  
  .toggle-btn {
    flex: 1;
    justify-content: center;
  }
}
