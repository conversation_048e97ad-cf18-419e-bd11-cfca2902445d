import React, { useState } from 'react';

const ImportExportActions = ({
  operation,
  selectedDataType,
  selectedTargetObject,
  selectedScope,
  selectedFile,
  previewData,
  validationResults
}) => {
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionResults, setExecutionResults] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [exportFormat, setExportFormat] = useState('excel');

  // Check if operation can be executed
  const canExecute = () => {
    if (operation === 'import') {
      return validationResults && (!validationResults.errors || validationResults.errors.length === 0);
    } else {
      return previewData && previewData.selectedRecords > 0;
    }
  };

  // Get execution summary
  const getExecutionSummary = () => {
    if (operation === 'import') {
      return {
        action: 'Import Data',
        description: `Import ${selectedDataType.replace('-', ' ')} data into ${previewData.validRows} ${selectedTargetObject.replace('-', ' ')}(s)`,
        impact: [
          `${previewData.validRows} records will be imported`,
          `${previewData.invalidRows} invalid records will be skipped`,
          'Existing data will be preserved unless explicitly overwritten',
          'Operation can be rolled back within 24 hours'
        ],
        risks: [
          previewData.invalidRows > 0 ? 'Some data will be skipped due to validation errors' : null,
          'Data integrity checks will be performed',
          'Backup will be created before import'
        ].filter(Boolean)
      };
    } else {
      return {
        action: 'Export Data',
        description: `Export ${selectedDataType.replace('-', ' ')} data from ${previewData.selectedRecords} ${selectedTargetObject.replace('-', ' ')}(s)`,
        impact: [
          `${previewData.selectedRecords} records will be exported`,
          `File size: approximately ${previewData.estimatedFileSize}`,
          'Export will include all selected data fields',
          'No changes will be made to existing data'
        ],
        risks: [
          'Exported data may contain sensitive information',
          'Ensure proper handling of exported files',
          'Export may take several minutes for large datasets'
        ]
      };
    }
  };

  // Handle execution
  const handleExecute = async () => {
    setIsExecuting(true);
    setShowConfirmation(false);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 3000));

      if (operation === 'import') {
        const mockResults = {
          success: true,
          imported: previewData.validRows,
          skipped: previewData.invalidRows,
          errors: validationResults?.errors || [],
          operationId: 'op_' + Date.now(),
          timestamp: new Date().toISOString()
        };
        setExecutionResults(mockResults);
      } else {
        const mockResults = {
          success: true,
          exported: previewData.selectedRecords,
          fileUrl: '/downloads/export_' + Date.now() + '.' + exportFormat,
          fileSize: previewData.estimatedFileSize,
          operationId: 'op_' + Date.now(),
          timestamp: new Date().toISOString()
        };
        setExecutionResults(mockResults);
      }
    } catch (error) {
      setExecutionResults({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    } finally {
      setIsExecuting(false);
    }
  };

  const summary = getExecutionSummary();

  return (
    <div className="import-export-actions">
      {/* Operation Summary */}
      <div className="operation-summary">
        <div className="summary-header">
          <i className={`fas ${operation === 'import' ? 'fa-upload' : 'fa-download'} text-blue-500`}></i>
          <h6>{summary.action} Summary</h6>
        </div>
        
        <div className="summary-content">
          <p className="summary-description">{summary.description}</p>
          
          <div className="summary-section">
            <h7>Impact:</h7>
            <ul>
              {summary.impact.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </div>
          
          <div className="summary-section">
            <h7>Important Notes:</h7>
            <ul>
              {summary.risks.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Export Format Selection (Export only) */}
      {operation === 'export' && (
        <div className="export-format-selection">
          <h6>Select Export Format</h6>
          <div className="format-options">
            <label className="format-option">
              <input
                type="radio"
                name="exportFormat"
                value="excel"
                checked={exportFormat === 'excel'}
                onChange={(e) => setExportFormat(e.target.value)}
              />
              <div className="format-info">
                <i className="fas fa-file-excel text-green-500"></i>
                <div>
                  <span className="format-name">Excel (.xlsx)</span>
                  <span className="format-description">Recommended for data analysis</span>
                </div>
              </div>
            </label>
            
            <label className="format-option">
              <input
                type="radio"
                name="exportFormat"
                value="csv"
                checked={exportFormat === 'csv'}
                onChange={(e) => setExportFormat(e.target.value)}
              />
              <div className="format-info">
                <i className="fas fa-file-csv text-blue-500"></i>
                <div>
                  <span className="format-name">CSV (.csv)</span>
                  <span className="format-description">Universal compatibility</span>
                </div>
              </div>
            </label>
            
            <label className="format-option">
              <input
                type="radio"
                name="exportFormat"
                value="json"
                checked={exportFormat === 'json'}
                onChange={(e) => setExportFormat(e.target.value)}
              />
              <div className="format-info">
                <i className="fas fa-file-code text-purple-500"></i>
                <div>
                  <span className="format-name">JSON (.json)</span>
                  <span className="format-description">API integration friendly</span>
                </div>
              </div>
            </label>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="action-buttons">
        <button
          className={`execute-btn ${operation === 'import' ? 'import' : 'export'}`}
          onClick={() => setShowConfirmation(true)}
          disabled={!canExecute() || isExecuting}
        >
          <i className={`fas ${operation === 'import' ? 'fa-upload' : 'fa-download'}`}></i>
          {operation === 'import' ? 'Execute Import' : 'Execute Export'}
        </button>
        
        <div className="safety-note">
          <i className="fas fa-shield-alt text-green-500"></i>
          <span>Safety controls active - operation can be rolled back</span>
        </div>
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="confirmation-modal">
          <div className="modal-overlay" onClick={() => setShowConfirmation(false)}></div>
          <div className="modal-content">
            <div className="modal-header">
              <h6>Confirm {summary.action}</h6>
              <button className="close-btn" onClick={() => setShowConfirmation(false)}>
                <i className="fas fa-times"></i>
              </button>
            </div>
            
            <div className="modal-body">
              <div className="confirmation-warning">
                <i className="fas fa-exclamation-triangle text-orange-500"></i>
                <p>Please confirm that you want to proceed with this operation.</p>
              </div>
              
              <div className="confirmation-details">
                <p><strong>Operation:</strong> {summary.description}</p>
                <p><strong>Scope:</strong> {selectedScope.type} scope</p>
                {operation === 'export' && (
                  <p><strong>Format:</strong> {exportFormat.toUpperCase()}</p>
                )}
              </div>
              
              <div className="confirmation-checklist">
                <label className="checklist-item">
                  <input type="checkbox" required />
                  <span>I understand the impact of this operation</span>
                </label>
                <label className="checklist-item">
                  <input type="checkbox" required />
                  <span>I have reviewed the preview and validation results</span>
                </label>
                <label className="checklist-item">
                  <input type="checkbox" required />
                  <span>I confirm this operation should proceed</span>
                </label>
              </div>
            </div>
            
            <div className="modal-footer">
              <button className="cancel-btn" onClick={() => setShowConfirmation(false)}>
                Cancel
              </button>
              <button className="confirm-btn" onClick={handleExecute}>
                <i className={`fas ${operation === 'import' ? 'fa-upload' : 'fa-download'}`}></i>
                Confirm {operation === 'import' ? 'Import' : 'Export'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Execution Progress */}
      {isExecuting && (
        <div className="execution-progress">
          <div className="progress-header">
            <i className="fas fa-spinner fa-spin text-blue-500"></i>
            <h6>Executing {summary.action}...</h6>
          </div>
          <div className="progress-bar">
            <div className="progress-fill"></div>
          </div>
          <p>Please wait while the operation completes. Do not close this window.</p>
        </div>
      )}

      {/* Execution Results */}
      {executionResults && (
        <div className={`execution-results ${executionResults.success ? 'success' : 'error'}`}>
          <div className="results-header">
            <i className={`fas ${executionResults.success ? 'fa-check-circle text-green-500' : 'fa-times-circle text-red-500'}`}></i>
            <h6>{executionResults.success ? 'Operation Completed Successfully' : 'Operation Failed'}</h6>
          </div>
          
          <div className="results-content">
            {executionResults.success ? (
              <div className="success-details">
                {operation === 'import' ? (
                  <>
                    <p><strong>Imported:</strong> {executionResults.imported} records</p>
                    <p><strong>Skipped:</strong> {executionResults.skipped} records</p>
                  </>
                ) : (
                  <>
                    <p><strong>Exported:</strong> {executionResults.exported} records</p>
                    <p><strong>File Size:</strong> {executionResults.fileSize}</p>
                    <a href={executionResults.fileUrl} className="download-link">
                      <i className="fas fa-download"></i>
                      Download Export File
                    </a>
                  </>
                )}
                <p><strong>Operation ID:</strong> {executionResults.operationId}</p>
                <p><strong>Completed:</strong> {new Date(executionResults.timestamp).toLocaleString()}</p>
              </div>
            ) : (
              <div className="error-details">
                <p><strong>Error:</strong> {executionResults.error}</p>
                <p>Please try again or contact support if the issue persists.</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ImportExportActions;
