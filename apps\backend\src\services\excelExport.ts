import * as XLSX from 'xlsx';
import { IBusinessCase } from '../models/BusinessCase';

export class ExcelExportService {
  static async exportBusinessCase(businessCase: IBusinessCase): Promise<Buffer> {
    const workbook = XLSX.utils.book_new();

    // Create Summary Sheet
    const summaryData = this.createSummarySheet(businessCase);
    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');

    // Create Financial Data Sheet
    const financialData = this.createFinancialDataSheet(businessCase);
    const financialSheet = XLSX.utils.aoa_to_sheet(financialData);
    XLSX.utils.book_append_sheet(workbook, financialSheet, 'Financial Data');

    // Create Cash Flow Analysis Sheet
    const cashFlowData = this.createCashFlowAnalysisSheet(businessCase);
    const cashFlowSheet = XLSX.utils.aoa_to_sheet(cashFlowData);
    XLSX.utils.book_append_sheet(workbook, cashFlowSheet, 'Cash Flow Analysis');

    // Create Metrics Calculation Sheet
    const metricsData = this.createMetricsCalculationSheet(businessCase);
    const metricsSheet = XLSX.utils.aoa_to_sheet(metricsData);
    XLSX.utils.book_append_sheet(workbook, metricsSheet, 'Financial Metrics');

    // Convert to buffer
    const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    return excelBuffer;
  }

  private static createSummarySheet(businessCase: IBusinessCase): any[][] {
    return [
      ['Business Case Financial Analysis'],
      [''],
      ['Business Case Name:', businessCase.name],
      ['Description:', businessCase.description || 'N/A'],
      ['Business Unit:', businessCase.businessUnit || 'N/A'],
      ['Status:', businessCase.status],
      ['Created By:', businessCase.createdBy],
      ['Created Date:', businessCase.createdAt.toLocaleDateString()],
      ['Last Modified:', businessCase.updatedAt.toLocaleDateString()],
      [''],
      ['Timeframe'],
      ['Start Year:', businessCase.timeframe.startYear],
      ['End Year:', businessCase.timeframe.endYear],
      ['Duration (Years):', businessCase.timeframe.endYear - businessCase.timeframe.startYear + 1],
      [''],
      ['Financial Summary'],
      ['Total CAPEX:', businessCase.financialData.totalCapex],
      ['Total OPEX:', businessCase.financialData.totalOpex],
      ['Total Investment:', businessCase.financialData.totalCapex + businessCase.financialData.totalOpex],
      [''],
      ['Calculated Metrics'],
      ['IRR (%):', businessCase.calculatedMetrics?.irr || 'N/A'],
      ['NPV ($):', businessCase.calculatedMetrics?.npv || 'N/A'],
      ['Payback Period (Years):', businessCase.calculatedMetrics?.paybackPeriod || 'N/A'],
      ['Gross Margin (%):', businessCase.calculatedMetrics?.grossMargin || 'N/A'],
      ['Commercial Margin (%):', businessCase.calculatedMetrics?.commercialMargin || 'N/A'],
      [''],
      ['Tags:', businessCase.tags?.join(', ') || 'None']
    ];
  }

  private static createFinancialDataSheet(businessCase: IBusinessCase): any[][] {
    const data: any[][] = [
      ['Financial Data Breakdown'],
      [''],
      ['CAPEX by Year'],
      ['Year', 'Amount ($)', 'Description']
    ];

    // Add CAPEX data
    businessCase.financialData.capex.forEach(item => {
      data.push([item.year, item.amount, item.description || '']);
    });

    data.push(['', '', '']);
    data.push(['Total CAPEX:', businessCase.financialData.totalCapex, '']);
    data.push(['', '', '']);
    data.push(['OPEX by Year']);
    data.push(['Year', 'Amount ($)', 'Description']);

    // Add OPEX data
    businessCase.financialData.opex.forEach(item => {
      data.push([item.year, item.amount, item.description || '']);
    });

    data.push(['', '', '']);
    data.push(['Total OPEX:', businessCase.financialData.totalOpex, '']);

    // Add Revenue data if available
    if (businessCase.financialData.revenue && businessCase.financialData.revenue.length > 0) {
      data.push(['', '', '']);
      data.push(['Revenue by Year']);
      data.push(['Year', 'Amount ($)', 'Description']);

      businessCase.financialData.revenue.forEach(item => {
        data.push([item.year, item.amount, item.description || '']);
      });

      const totalRevenue = businessCase.financialData.revenue.reduce((sum, item) => sum + item.amount, 0);
      data.push(['', '', '']);
      data.push(['Total Revenue:', totalRevenue, '']);
    }

    return data;
  }

  private static createCashFlowAnalysisSheet(businessCase: IBusinessCase): any[][] {
    const startYear = businessCase.timeframe.startYear;
    const endYear = businessCase.timeframe.endYear;
    const years = [];
    
    for (let year = startYear; year <= endYear; year++) {
      years.push(year);
    }

    const data: any[][] = [
      ['Cash Flow Analysis'],
      [''],
      ['Year', ...years],
      ['']
    ];

    // CAPEX row
    const capexRow = ['CAPEX'];
    years.forEach(year => {
      const capexItem = businessCase.financialData.capex.find(item => item.year === year);
      capexRow.push(capexItem ? -capexItem.amount : 0);
    });
    data.push(capexRow);

    // OPEX row
    const opexRow = ['OPEX'];
    years.forEach(year => {
      const opexItem = businessCase.financialData.opex.find(item => item.year === year);
      opexRow.push(opexItem ? -opexItem.amount : 0);
    });
    data.push(opexRow);

    // Revenue row (if available)
    if (businessCase.financialData.revenue && businessCase.financialData.revenue.length > 0) {
      const revenueRow = ['Revenue'];
      years.forEach(year => {
        const revenueItem = businessCase.financialData.revenue!.find(item => item.year === year);
        revenueRow.push(revenueItem ? revenueItem.amount : 0);
      });
      data.push(revenueRow);
    }

    // Net Cash Flow row with formulas
    const netCashFlowRow = ['Net Cash Flow'];
    years.forEach((year, index) => {
      const col = this.getExcelColumn(index + 2); // +2 because first column is labels
      if (businessCase.financialData.revenue && businessCase.financialData.revenue.length > 0) {
        // If revenue exists: Revenue - CAPEX - OPEX
        netCashFlowRow.push(`=C${data.length + 1}+D${data.length + 1}+E${data.length + 1}`);
      } else {
        // If no revenue: -CAPEX - OPEX
        netCashFlowRow.push(`=C${data.length + 1}+D${data.length + 1}`);
      }
    });
    data.push(netCashFlowRow);

    // Cumulative Cash Flow row with formulas
    const cumulativeCashFlowRow = ['Cumulative Cash Flow'];
    years.forEach((year, index) => {
      const col = this.getExcelColumn(index + 2);
      if (index === 0) {
        cumulativeCashFlowRow.push(`=${col}${data.length}`);
      } else {
        const prevCol = this.getExcelColumn(index + 1);
        cumulativeCashFlowRow.push(`=${prevCol}${data.length + 1}+${col}${data.length}`);
      }
    });
    data.push(cumulativeCashFlowRow);

    return data;
  }

  private static createMetricsCalculationSheet(businessCase: IBusinessCase): any[][] {
    const startYear = businessCase.timeframe.startYear;
    const endYear = businessCase.timeframe.endYear;
    const years = [];
    
    for (let year = startYear; year <= endYear; year++) {
      years.push(year);
    }

    const data: any[][] = [
      ['Financial Metrics Calculations'],
      [''],
      ['Discount Rate (%):', 10], // Default discount rate
      [''],
      ['Cash Flow Data for Calculations'],
      ['Year', ...years],
      ['']
    ];

    // Net Cash Flow row for calculations
    const netCashFlowRow = ['Net Cash Flow'];
    years.forEach(year => {
      const capexItem = businessCase.financialData.capex.find(item => item.year === year);
      const opexItem = businessCase.financialData.opex.find(item => item.year === year);
      const revenueItem = businessCase.financialData.revenue?.find(item => item.year === year);
      
      const capex = capexItem ? capexItem.amount : 0;
      const opex = opexItem ? opexItem.amount : 0;
      const revenue = revenueItem ? revenueItem.amount : 0;
      
      netCashFlowRow.push(revenue - capex - opex);
    });
    data.push(netCashFlowRow);

    data.push(['']);
    data.push(['Financial Metrics']);
    data.push(['']);

    // IRR Calculation
    const cashFlowRange = `B${data.length - 3}:${this.getExcelColumn(years.length + 1)}${data.length - 3}`;
    data.push(['IRR (%):', `=IRR(${cashFlowRange})*100`]);

    // NPV Calculation
    const discountRate = 'B3';
    data.push(['NPV ($):', `=NPV(${discountRate}/100,${cashFlowRange})`]);

    // Payback Period Calculation (simplified)
    data.push(['Payback Period (Years):', this.createPaybackFormula(years.length)]);

    // Gross Margin Calculation (if revenue exists)
    if (businessCase.financialData.revenue && businessCase.financialData.revenue.length > 0) {
      const totalRevenue = businessCase.financialData.revenue.reduce((sum, item) => sum + item.amount, 0);
      const totalCosts = businessCase.financialData.totalCapex + businessCase.financialData.totalOpex;
      data.push(['Gross Margin (%):', `=(${totalRevenue}-${totalCosts})/${totalRevenue}*100`]);
    } else {
      data.push(['Gross Margin (%):', 'N/A - No revenue data']);
    }

    // Commercial Margin (custom business logic)
    data.push(['Commercial Margin (%):', 'Custom calculation based on business logic']);

    data.push(['']);
    data.push(['Notes:']);
    data.push(['- IRR: Internal Rate of Return']);
    data.push(['- NPV: Net Present Value']);
    data.push(['- Payback Period: Time to recover initial investment']);
    data.push(['- Gross Margin: (Revenue - Total Costs) / Revenue']);
    data.push(['- Commercial Margin: Custom calculation based on specific business rules']);

    return data;
  }

  private static getExcelColumn(index: number): string {
    let column = '';
    while (index > 0) {
      index--;
      column = String.fromCharCode(65 + (index % 26)) + column;
      index = Math.floor(index / 26);
    }
    return column;
  }

  private static createPaybackFormula(yearCount: number): string {
    // Simplified payback period calculation
    // This would need to be more sophisticated in a real implementation
    return `"Calculated based on cumulative cash flow"`;
  }
}
