import React from 'react';

const IdeaStats = ({ ideas }) => {
  // Calculate statistics
  const totalIdeas = ideas.length;
  
  const statusCounts = ideas.reduce((acc, idea) => {
    acc[idea.status] = (acc[idea.status] || 0) + 1;
    return acc;
  }, {});

  const priorityCounts = ideas.reduce((acc, idea) => {
    acc[idea.priority] = (acc[idea.priority] || 0) + 1;
    return acc;
  }, {});

  const totalEstimatedCost = ideas.reduce((sum, idea) => {
    return sum + (idea.estimatedCost || 0);
  }, 0);

  const totalExpectedBenefit = ideas.reduce((sum, idea) => {
    return sum + (idea.expectedBenefit || 0);
  }, 0);

  const potentialROI = totalEstimatedCost > 0 ? 
    ((totalExpectedBenefit - totalEstimatedCost) / totalEstimatedCost * 100) : 0;

  const recentIdeas = ideas.filter(idea => {
    const submissionDate = new Date(idea.submissionDate);
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    return submissionDate >= thirtyDaysAgo;
  }).length;

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`;
  };

  const getStatusColor = (status) => {
    const colors = {
      'submitted': 'text-blue-600',
      'under-review': 'text-yellow-600',
      'approved': 'text-green-600',
      'rejected': 'text-red-600',
      'on-hold': 'text-gray-600',
      'implemented': 'text-purple-600'
    };
    return colors[status] || 'text-gray-600';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'low': 'text-green-600',
      'medium': 'text-yellow-600',
      'high': 'text-orange-600',
      'critical': 'text-red-600'
    };
    return colors[priority] || 'text-gray-600';
  };

  const statCards = [
    {
      title: 'Total Ideas',
      value: totalIdeas,
      icon: 'fas fa-lightbulb',
      color: 'bg-blue-500',
      textColor: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Recent Ideas',
      value: recentIdeas,
      subtitle: 'Last 30 days',
      icon: 'fas fa-clock',
      color: 'bg-green-500',
      textColor: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'Estimated Investment',
      value: formatCurrency(totalEstimatedCost),
      icon: 'fas fa-dollar-sign',
      color: 'bg-orange-500',
      textColor: 'text-orange-600',
      bgColor: 'bg-orange-50'
    },
    {
      title: 'Expected Benefits',
      value: formatCurrency(totalExpectedBenefit),
      icon: 'fas fa-chart-line',
      color: 'bg-purple-500',
      textColor: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'Potential ROI',
      value: formatPercentage(potentialROI),
      icon: 'fas fa-percentage',
      color: 'bg-indigo-500',
      textColor: 'text-indigo-600',
      bgColor: 'bg-indigo-50'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Main Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {statCards.map((stat, index) => (
          <div key={index} className={`${stat.bgColor} rounded-lg p-4 border border-gray-200`}>
            <div className="flex items-center">
              <div className={`${stat.color} rounded-lg p-3 mr-4`}>
                <i className={`${stat.icon} text-white text-lg`}></i>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className={`text-2xl font-bold ${stat.textColor}`}>{stat.value}</p>
                {stat.subtitle && (
                  <p className="text-xs text-gray-500">{stat.subtitle}</p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Status and Priority Breakdown */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* Status Breakdown */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            <i className="fas fa-chart-pie mr-2 text-blue-600"></i>
            Ideas by Status
          </h3>
          <div className="space-y-3">
            {Object.entries(statusCounts).map(([status, count]) => {
              const percentage = ((count / totalIdeas) * 100).toFixed(1);
              return (
                <div key={status} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-3 ${getStatusColor(status).replace('text-', 'bg-')}`}></div>
                    <span className="text-sm font-medium text-gray-700 capitalize">
                      {status.replace('-', ' ')}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">{count}</span>
                    <span className="text-xs text-gray-500">({percentage}%)</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Priority Breakdown */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            <i className="fas fa-exclamation-triangle mr-2 text-orange-600"></i>
            Ideas by Priority
          </h3>
          <div className="space-y-3">
            {Object.entries(priorityCounts).map(([priority, count]) => {
              const percentage = ((count / totalIdeas) * 100).toFixed(1);
              return (
                <div key={priority} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-3 ${getPriorityColor(priority).replace('text-', 'bg-')}`}></div>
                    <span className="text-sm font-medium text-gray-700 capitalize">
                      {priority}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">{count}</span>
                    <span className="text-xs text-gray-500">({percentage}%)</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Quick Insights */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-4">
          <i className="fas fa-brain mr-2"></i>
          Quick Insights
        </h3>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="bg-white rounded-lg p-4 border border-blue-100">
            <div className="flex items-center mb-2">
              <i className="fas fa-trending-up text-green-600 mr-2"></i>
              <span className="text-sm font-medium text-gray-700">Most Active Status</span>
            </div>
            <p className="text-lg font-semibold text-gray-900 capitalize">
              {Object.entries(statusCounts).reduce((a, b) => statusCounts[a[0]] > statusCounts[b[0]] ? a : b)[0].replace('-', ' ')}
            </p>
          </div>

          <div className="bg-white rounded-lg p-4 border border-blue-100">
            <div className="flex items-center mb-2">
              <i className="fas fa-fire text-red-600 mr-2"></i>
              <span className="text-sm font-medium text-gray-700">Highest Priority</span>
            </div>
            <p className="text-lg font-semibold text-gray-900 capitalize">
              {Object.entries(priorityCounts).reduce((a, b) => priorityCounts[a[0]] > priorityCounts[b[0]] ? a : b)[0]}
            </p>
          </div>

          <div className="bg-white rounded-lg p-4 border border-blue-100">
            <div className="flex items-center mb-2">
              <i className="fas fa-calculator text-purple-600 mr-2"></i>
              <span className="text-sm font-medium text-gray-700">Avg. Investment</span>
            </div>
            <p className="text-lg font-semibold text-gray-900">
              {totalIdeas > 0 ? formatCurrency(totalEstimatedCost / totalIdeas) : '$0'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IdeaStats;
