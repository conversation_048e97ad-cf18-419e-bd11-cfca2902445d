import React, { useState, useEffect } from 'react';

const ScopeSelector = ({ selectedScope, onScopeChange, targetObject, operation }) => {
  const [scopeType, setScopeType] = useState('');
  const [selectedBusinessUnits, setSelectedBusinessUnits] = useState([]);
  const [selectedStatuses, setSelectedStatuses] = useState([]);
  const [selectedEntities, setSelectedEntities] = useState([]);
  const [dateRange, setDateRange] = useState({ start: '', end: '' });

  // Mock data - in real implementation, this would come from API
  const businessUnits = [
    'Technology', 'Customer Service', 'Operations', 'Manufacturing',
    'Supply Chain', 'Human Resources', 'Analytics', 'Infrastructure', 'Security'
  ];

  const statuses = ['active', 'draft', 'approved', 'on-hold', 'completed', 'cancelled'];

  const mockEntities = {
    'business-case': [
      { id: 'bc_1', name: 'AI Customer Support Implementation', businessUnit: 'Customer Service', status: 'approved' },
      { id: 'bc_2', name: 'Blockchain Supply Chain System', businessUnit: 'Supply Chain', status: 'approved' },
      { id: 'bc_3', name: 'Green Energy Transition', businessUnit: 'Operations', status: 'approved' },
      { id: 'bc_5', name: 'Digital Transformation Program', businessUnit: 'Technology', status: 'approved' }
    ],
    'master-business-case': [
      { id: 'mbc_1', name: 'Customer Service Excellence Program', businessUnit: 'Customer Service', status: 'approved' },
      { id: 'mbc_2', name: 'Technology Innovation Program', businessUnit: 'Technology', status: 'approved' },
      { id: 'mbc_3', name: 'Operations Excellence Program', businessUnit: 'Operations', status: 'approved' }
    ],
    'program': [
      { id: 'prog_1', name: 'Digital Innovation Program', businessUnit: 'Technology', status: 'active' },
      { id: 'prog_2', name: 'Customer Excellence Program', businessUnit: 'Customer Service', status: 'active' },
      { id: 'prog_3', name: 'Operations Excellence Program', businessUnit: 'Operations', status: 'active' }
    ],
    'project': [
      { id: 'proj_1', name: 'AI Customer Support Implementation', businessUnit: 'Customer Service', status: 'active' },
      { id: 'proj_2', name: 'Customer Experience Platform', businessUnit: 'Customer Service', status: 'active' },
      { id: 'proj_3', name: 'Green Energy Transition', businessUnit: 'Operations', status: 'active' }
    ],
    'epic': [
      { id: 'epic_1', name: 'Digital Transformation Epic', businessUnit: 'Technology', status: 'active' },
      { id: 'epic_2', name: 'Customer Service Excellence Epic', businessUnit: 'Customer Service', status: 'active' },
      { id: 'epic_3', name: 'Manufacturing Automation Epic', businessUnit: 'Manufacturing', status: 'active' }
    ],
    'idea': [
      { id: 'idea_1', name: 'AI-Powered Analytics Platform', businessUnit: 'Analytics', status: 'draft' },
      { id: 'idea_2', name: 'Sustainable Supply Chain Initiative', businessUnit: 'Supply Chain', status: 'draft' }
    ]
  };

  const scopeTypes = [
    {
      id: 'all',
      name: 'All Entities',
      description: `All ${targetObject.replace('-', ' ')}s in the system`,
      icon: 'fas fa-globe'
    },
    {
      id: 'business-unit',
      name: 'By Business Unit',
      description: 'Filter by specific business units',
      icon: 'fas fa-building'
    },
    {
      id: 'status',
      name: 'By Status',
      description: 'Filter by entity status',
      icon: 'fas fa-flag'
    },
    {
      id: 'specific',
      name: 'Specific Entities',
      description: 'Select individual entities',
      icon: 'fas fa-list'
    },
    {
      id: 'date-range',
      name: 'By Date Range',
      description: 'Filter by creation or update date',
      icon: 'fas fa-calendar'
    }
  ];

  // Update scope when selections change
  useEffect(() => {
    const scope = {
      type: scopeType,
      businessUnits: selectedBusinessUnits,
      statuses: selectedStatuses,
      entities: selectedEntities,
      dateRange: dateRange
    };
    onScopeChange(scope);
  }, [scopeType, selectedBusinessUnits, selectedStatuses, selectedEntities, dateRange, onScopeChange]);

  const handleScopeTypeChange = (type) => {
    setScopeType(type);
    // Reset other selections when scope type changes
    setSelectedBusinessUnits([]);
    setSelectedStatuses([]);
    setSelectedEntities([]);
    setDateRange({ start: '', end: '' });
  };

  const handleBusinessUnitToggle = (bu) => {
    setSelectedBusinessUnits(prev => 
      prev.includes(bu) 
        ? prev.filter(item => item !== bu)
        : [...prev, bu]
    );
  };

  const handleStatusToggle = (status) => {
    setSelectedStatuses(prev => 
      prev.includes(status) 
        ? prev.filter(item => item !== status)
        : [...prev, status]
    );
  };

  const handleEntityToggle = (entityId) => {
    setSelectedEntities(prev => 
      prev.includes(entityId) 
        ? prev.filter(item => item !== entityId)
        : [...prev, entityId]
    );
  };

  const getFilteredEntities = () => {
    let entities = mockEntities[targetObject] || [];
    
    if (selectedBusinessUnits.length > 0) {
      entities = entities.filter(entity => selectedBusinessUnits.includes(entity.businessUnit));
    }
    
    if (selectedStatuses.length > 0) {
      entities = entities.filter(entity => selectedStatuses.includes(entity.status));
    }
    
    return entities;
  };

  const getEstimatedCount = () => {
    switch (scopeType) {
      case 'all':
        return mockEntities[targetObject]?.length || 0;
      case 'business-unit':
        return getFilteredEntities().length;
      case 'status':
        return getFilteredEntities().length;
      case 'specific':
        return selectedEntities.length;
      case 'date-range':
        return getFilteredEntities().length; // Would be calculated based on date range
      default:
        return 0;
    }
  };

  return (
    <div className="scope-selector">
      {/* Scope Type Selection */}
      <div className="scope-type-selection">
        <h6>Select Scope Type</h6>
        <div className="scope-type-grid">
          {scopeTypes.map(type => (
            <div
              key={type.id}
              className={`scope-type-card ${scopeType === type.id ? 'selected' : ''}`}
              onClick={() => handleScopeTypeChange(type.id)}
            >
              <i className={type.icon}></i>
              <div>
                <span className="type-name">{type.name}</span>
                <span className="type-description">{type.description}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Scope Configuration */}
      {scopeType && (
        <div className="scope-configuration">
          {scopeType === 'business-unit' && (
            <div className="filter-section">
              <h6>Select Business Units</h6>
              <div className="checkbox-grid">
                {businessUnits.map(bu => (
                  <label key={bu} className="checkbox-item">
                    <input
                      type="checkbox"
                      checked={selectedBusinessUnits.includes(bu)}
                      onChange={() => handleBusinessUnitToggle(bu)}
                    />
                    <span>{bu}</span>
                  </label>
                ))}
              </div>
            </div>
          )}

          {scopeType === 'status' && (
            <div className="filter-section">
              <h6>Select Statuses</h6>
              <div className="checkbox-grid">
                {statuses.map(status => (
                  <label key={status} className="checkbox-item">
                    <input
                      type="checkbox"
                      checked={selectedStatuses.includes(status)}
                      onChange={() => handleStatusToggle(status)}
                    />
                    <span className="capitalize">{status}</span>
                  </label>
                ))}
              </div>
            </div>
          )}

          {scopeType === 'specific' && (
            <div className="filter-section">
              <h6>Select Specific Entities</h6>
              <div className="entity-list">
                {getFilteredEntities().map(entity => (
                  <label key={entity.id} className="entity-item">
                    <input
                      type="checkbox"
                      checked={selectedEntities.includes(entity.id)}
                      onChange={() => handleEntityToggle(entity.id)}
                    />
                    <div className="entity-info">
                      <span className="entity-name">{entity.name}</span>
                      <span className="entity-meta">{entity.businessUnit} • {entity.status}</span>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          )}

          {scopeType === 'date-range' && (
            <div className="filter-section">
              <h6>Select Date Range</h6>
              <div className="date-range-inputs">
                <div className="date-input">
                  <label>Start Date</label>
                  <input
                    type="date"
                    value={dateRange.start}
                    onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                  />
                </div>
                <div className="date-input">
                  <label>End Date</label>
                  <input
                    type="date"
                    value={dateRange.end}
                    onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Scope Summary */}
          <div className="scope-summary">
            <div className="summary-header">
              <i className="fas fa-info-circle text-blue-500"></i>
              <h6>Scope Summary</h6>
            </div>
            <div className="summary-content">
              <p><strong>Scope Type:</strong> {scopeTypes.find(t => t.id === scopeType)?.name}</p>
              <p><strong>Estimated Count:</strong> {getEstimatedCount()} {targetObject.replace('-', ' ')}(s)</p>
              
              {selectedBusinessUnits.length > 0 && (
                <p><strong>Business Units:</strong> {selectedBusinessUnits.join(', ')}</p>
              )}
              
              {selectedStatuses.length > 0 && (
                <p><strong>Statuses:</strong> {selectedStatuses.join(', ')}</p>
              )}
              
              {dateRange.start && dateRange.end && (
                <p><strong>Date Range:</strong> {dateRange.start} to {dateRange.end}</p>
              )}
              
              <div className="operation-impact">
                <p><strong>{operation === 'import' ? 'Import' : 'Export'} Impact:</strong></p>
                <ul>
                  <li>{getEstimatedCount()} entities will be {operation === 'import' ? 'affected' : 'included'}</li>
                  {operation === 'import' && (
                    <li>Existing data will be preserved unless explicitly overwritten</li>
                  )}
                  <li>Operation can be rolled back if needed</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ScopeSelector;
