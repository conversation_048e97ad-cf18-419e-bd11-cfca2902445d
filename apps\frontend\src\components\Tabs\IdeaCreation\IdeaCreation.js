import React, { useState, useEffect } from 'react';
import { useData } from '../../../context/DataContext';
import { useAuth } from '../../../context/AuthContext';
import IdeaForm from './IdeaForm';
import IdeaList from './IdeaList';
import IdeaEmptyState from './IdeaEmptyState';
import IdeaStats from './IdeaStats';

const IdeaCreation = () => {
  const { ideas, loading, loadIdeas, createIdea, updateIdea, deleteIdea } = useData();
  const { user } = useAuth();
  const [activeView, setActiveView] = useState('list'); // 'list', 'create', 'edit'
  const [selectedIdea, setSelectedIdea] = useState(null);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    businessUnit: '',
    search: ''
  });

  useEffect(() => {
    loadIdeas();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const handleCreateIdea = async (ideaData) => {
    const result = await createIdea({
      ...ideaData,
      submitterName: user?.name || 'Unknown User',
      submitterId: user?.id || 'unknown'
    });

    if (result.success) {
      setActiveView('list');
      return result;
    }
    return result;
  };

  const handleUpdateIdea = async (ideaData) => {
    const result = await updateIdea(selectedIdea.id, ideaData);
    if (result.success) {
      setActiveView('list');
      setSelectedIdea(null);
    }
    return result;
  };

  const handleDeleteIdea = async (ideaId) => {
    const result = await deleteIdea(ideaId);
    if (result.success && selectedIdea?.id === ideaId) {
      setSelectedIdea(null);
      setActiveView('list');
    }
    return result;
  };

  const handleEditIdea = (idea) => {
    setSelectedIdea(idea);
    setActiveView('edit');
  };

  const handleViewChange = (view) => {
    setActiveView(view);
    if (view !== 'edit') {
      setSelectedIdea(null);
    }
  };

  const filteredIdeas = ideas.filter(idea => {
    if (filters.status && idea.status !== filters.status) return false;
    if (filters.priority && idea.priority !== filters.priority) return false;
    if (filters.businessUnit && idea.businessUnitId !== filters.businessUnit) return false;
    if (filters.search && !idea.title.toLowerCase().includes(filters.search.toLowerCase()) &&
        !idea.problemStatement.toLowerCase().includes(filters.search.toLowerCase())) return false;
    return true;
  });

  const showEmptyState = !loading.ideas && ideas.length === 0;
  const showList = !loading.ideas && ideas.length > 0 && activeView === 'list';
  const showForm = activeView === 'create' || activeView === 'edit';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Idea Creation & Management</h2>
          <p className="text-gray-600">Transform innovative ideas into actionable business opportunities</p>
        </div>

        {ideas.length > 0 && (
          <div className="flex space-x-3">
            <button
              onClick={() => handleViewChange('list')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeView === 'list'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <i className="fas fa-list mr-2"></i>
              View Ideas
            </button>
            <button
              onClick={() => handleViewChange('create')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeView === 'create'
                  ? 'bg-green-600 text-white'
                  : 'bg-green-600 text-white hover:bg-green-700'
              }`}
            >
              <i className="fas fa-plus mr-2"></i>
              New Idea
            </button>
          </div>
        )}
      </div>

      {/* Loading State */}
      {loading.ideas && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="loading-spinner mx-auto mb-4"></div>
            <p className="text-gray-600">Loading ideas...</p>
          </div>
        </div>
      )}

      {/* Stats Section - Show when there are ideas */}
      {ideas.length > 0 && activeView === 'list' && (
        <IdeaStats ideas={ideas} />
      )}

      {/* Empty State */}
      {showEmptyState && (
        <IdeaEmptyState onCreateFirst={() => handleViewChange('create')} />
      )}

      {/* Ideas List */}
      {showList && (
        <IdeaList
          ideas={filteredIdeas}
          filters={filters}
          onFiltersChange={setFilters}
          onEditIdea={handleEditIdea}
          onDeleteIdea={handleDeleteIdea}
          onCreateNew={() => handleViewChange('create')}
        />
      )}

      {/* Idea Form */}
      {showForm && (
        <IdeaForm
          idea={selectedIdea}
          onSubmit={activeView === 'edit' ? handleUpdateIdea : handleCreateIdea}
          onCancel={() => handleViewChange('list')}
          isEditing={activeView === 'edit'}
        />
      )}
    </div>
  );
};

export default IdeaCreation;
