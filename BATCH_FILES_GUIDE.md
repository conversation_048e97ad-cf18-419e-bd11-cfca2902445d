# 🚀 **Batch Files Guide - Master Business Case Management**

## **📋 Available Batch Files**

### **🎯 For Most Users (Recommended)**

#### **`START_APPLICATION.bat`** ⭐
- **Main launcher with menu options**
- Choose between frontend-only or full application
- **Double-click to start** - No technical knowledge required
- **Recommended for all users**

#### **`install-dependencies.bat`**
- **Run this FIRST** if it's your first time
- Installs all required packages automatically
- Only needs to be run once (or after updates)

### **🔧 For Advanced Users**

#### **`start-frontend.bat`**
- Starts only the React frontend application
- Fastest startup option
- Uses JSON file storage
- Runs on http://localhost:3000

#### **`start-backend.bat`**
- Starts only the API backend server
- For development or API testing
- Runs on http://localhost:5000

#### **`start-full-application.bat`**
- Starts both frontend and backend
- Opens two command windows
- Full functionality with API integration

---

## **🚀 Quick Start Instructions**

### **First Time Setup:**
1. **Double-click** `install-dependencies.bat`
2. Wait for installation to complete (3-5 minutes)
3. **Double-click** `START_APPLICATION.bat`
4. Choose option 1 (Frontend Only) for most users
5. Browser will open automatically to http://localhost:3000

### **Daily Use:**
1. **Double-click** `START_APPLICATION.bat`
2. Choose your preferred option
3. Start using the application!

---

## **📋 System Requirements**

### **Required:**
- **Windows 10/11**
- **Node.js 16+** (Download from: https://nodejs.org/)
- **4GB RAM minimum** (8GB recommended)
- **Internet connection** (for initial setup)

### **Check if Node.js is Installed:**
1. Press `Win + R`
2. Type `cmd` and press Enter
3. Type `node --version` and press Enter
4. If you see a version number (like v18.x.x), you're ready!
5. If you get an error, install Node.js first

---

## **🛠️ Troubleshooting**

### **Common Issues:**

#### **"Node.js is not installed"**
- **Solution:** Download and install Node.js from https://nodejs.org/
- Choose the LTS (Long Term Support) version
- Restart your computer after installation

#### **"Directory not found"**
- **Solution:** Make sure you're running the batch files from the `spm` project folder
- The folder should contain `apps\frontend` and `apps\backend` directories

#### **"Failed to install dependencies"**
- **Solution:** Check your internet connection
- Run `install-dependencies.bat` again
- Try running as Administrator (right-click → "Run as administrator")

#### **Port already in use**
- **Solution:** Close any existing instances of the application
- Restart your computer if needed
- The batch files will handle port conflicts automatically

#### **Browser doesn't open automatically**
- **Solution:** Manually open your browser and go to http://localhost:3000
- Make sure your default browser is set correctly

---

## **💡 Tips & Best Practices**

### **For Best Performance:**
- **Close other applications** before starting
- **Use Chrome or Edge** for best compatibility
- **Don't close the command windows** while using the application

### **To Stop the Application:**
- **Close the command window(s)**, or
- **Press Ctrl+C** in the command window
- **Close your browser tab**

### **For Regular Use:**
- **Bookmark** http://localhost:3000 in your browser
- **Create desktop shortcuts** to the batch files
- **Keep the project folder** in an easy-to-find location

---

## **🔄 Updates & Maintenance**

### **When to Re-run Installation:**
- After downloading updates to the application
- If you encounter dependency errors
- If it's been a long time since last use

### **Updating Dependencies:**
1. Run `install-dependencies.bat` again
2. This will update all packages to latest versions
3. Restart the application

---

## **📞 Getting Help**

### **If Something Goes Wrong:**
1. **Check the command window** for error messages
2. **Try running** `install-dependencies.bat` again
3. **Restart your computer** and try again
4. **Check that Node.js is properly installed**

### **Success Indicators:**
- ✅ Command window shows "Compiled successfully!"
- ✅ Browser opens to the application
- ✅ You can see the navigation tabs (Ideas, Business Cases, etc.)
- ✅ Sample data is visible in the application

---

## **🎉 You're Ready!**

Once you see the Master Business Case Management interface in your browser, you're ready to start managing business cases, projects, and strategic portfolios!

**Happy Business Case Management! 🚀**
