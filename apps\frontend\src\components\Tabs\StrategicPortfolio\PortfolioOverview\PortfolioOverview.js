import React from 'react';
import './PortfolioOverview.css';

const PortfolioOverview = ({ portfolioData, onViewChange }) => {
  const { masterBCs, programs, profitabilityMetrics, riskMetrics } = portfolioData;

  console.log('📈 PortfolioOverview - Received data:', {
    portfolioData,
    masterBCsCount: masterBCs?.length || 0,
    programsCount: programs?.length || 0,
    profitabilityMetrics,
    riskMetrics
  });

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount || 0);
  };

  const formatPercentage = (value) => {
    return `${(value || 0).toFixed(1)}%`;
  };

  // Get top performing Master BCs
  const topPerformingMasterBCs = masterBCs
    .filter(mbc => mbc.aggregatedMetrics?.totalNPV > 0)
    .sort((a, b) => (b.aggregatedMetrics?.totalNPV || 0) - (a.aggregatedMetrics?.totalNPV || 0))
    .slice(0, 5);

  // Get underperforming Master BCs
  const underperformingMasterBCs = masterBCs
    .filter(mbc => (mbc.aggregatedMetrics?.totalNPV || 0) < 0)
    .sort((a, b) => (a.aggregatedMetrics?.totalNPV || 0) - (b.aggregatedMetrics?.totalNPV || 0))
    .slice(0, 3);

  // Get programs with linked Master BCs
  const linkedPrograms = programs.filter(p => p.linkedMasterBC);
  const unlinkedPrograms = programs.filter(p => !p.linkedMasterBC);

  return (
    <div className="portfolio-overview">
      {/* Key Metrics Cards */}
      <div className="metrics-grid">
        <div className="metric-card primary">
          <div className="metric-icon">
            <i className="fas fa-dollar-sign"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{formatCurrency(profitabilityMetrics.totalInvestment)}</div>
            <div className="metric-label">Total Portfolio Investment</div>
          </div>
        </div>

        <div className="metric-card success">
          <div className="metric-icon">
            <i className="fas fa-chart-line"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{formatCurrency(profitabilityMetrics.totalNPV)}</div>
            <div className="metric-label">Total Portfolio NPV</div>
          </div>
        </div>

        <div className="metric-card info">
          <div className="metric-icon">
            <i className="fas fa-percentage"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{formatPercentage(profitabilityMetrics.avgIRR)}</div>
            <div className="metric-label">Average Portfolio IRR</div>
          </div>
        </div>

        <div className="metric-card warning">
          <div className="metric-icon">
            <i className="fas fa-shield-alt"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{riskMetrics.avgRiskScore?.toFixed(1) || 0}/10</div>
            <div className="metric-label">Average Risk Score</div>
          </div>
        </div>
      </div>

      {/* Portfolio Health Summary */}
      <div className="portfolio-health">
        <h3 className="section-title">Portfolio Health Summary</h3>
        <div className="health-grid">
          <div className="health-card">
            <div className="health-header">
              <h4>Profitability Status</h4>
              <i className="fas fa-chart-pie"></i>
            </div>
            <div className="health-stats">
              <div className="stat-item">
                <span className="stat-value profitable">{profitabilityMetrics.profitableCount}</span>
                <span className="stat-label">Profitable Master BCs</span>
              </div>
              <div className="stat-item">
                <span className="stat-value total">{profitabilityMetrics.totalCount}</span>
                <span className="stat-label">Total Master BCs</span>
              </div>
              <div className="stat-item">
                <span className="stat-value percentage">
                  {formatPercentage((profitabilityMetrics.profitableCount / profitabilityMetrics.totalCount) * 100)}
                </span>
                <span className="stat-label">Success Rate</span>
              </div>
            </div>
          </div>

          <div className="health-card">
            <div className="health-header">
              <h4>Risk Distribution</h4>
              <i className="fas fa-exclamation-triangle"></i>
            </div>
            <div className="health-stats">
              <div className="stat-item">
                <span className="stat-value high-risk">{riskMetrics.highRiskCount}</span>
                <span className="stat-label">High Risk</span>
              </div>
              <div className="stat-item">
                <span className="stat-value medium-risk">{riskMetrics.mediumRiskCount}</span>
                <span className="stat-label">Medium Risk</span>
              </div>
              <div className="stat-item">
                <span className="stat-value low-risk">{riskMetrics.lowRiskCount}</span>
                <span className="stat-label">Low Risk</span>
              </div>
            </div>
          </div>

          <div className="health-card">
            <div className="health-header">
              <h4>Program Alignment</h4>
              <i className="fas fa-sitemap"></i>
            </div>
            <div className="health-stats">
              <div className="stat-item">
                <span className="stat-value linked">{linkedPrograms.length}</span>
                <span className="stat-label">Linked Programs</span>
              </div>
              <div className="stat-item">
                <span className="stat-value unlinked">{unlinkedPrograms.length}</span>
                <span className="stat-label">Unlinked Programs</span>
              </div>
              <div className="stat-item">
                <span className="stat-value percentage">
                  {formatPercentage((linkedPrograms.length / programs.length) * 100)}
                </span>
                <span className="stat-label">Alignment Rate</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Top Performers */}
      <div className="performers-section">
        <div className="performers-grid">
          <div className="performers-card">
            <div className="performers-header">
              <h3>Top Performing Master BCs</h3>
              <button 
                className="view-all-btn"
                onClick={() => onViewChange('profitability')}
              >
                View All
              </button>
            </div>
            <div className="performers-list">
              {topPerformingMasterBCs.length > 0 ? (
                topPerformingMasterBCs.map((mbc, index) => (
                  <div key={mbc.id} className="performer-item">
                    <div className="performer-rank">#{index + 1}</div>
                    <div className="performer-info">
                      <div className="performer-name">{mbc.name}</div>
                      <div className="performer-category">{mbc.category}</div>
                    </div>
                    <div className="performer-metrics">
                      <div className="metric-npv">{formatCurrency(mbc.aggregatedMetrics?.totalNPV)}</div>
                      <div className="metric-irr">{formatPercentage(mbc.aggregatedMetrics?.avgIRR)} IRR</div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="empty-state">
                  <i className="fas fa-chart-line"></i>
                  <p>No profitable Master BCs found</p>
                </div>
              )}
            </div>
          </div>

          <div className="performers-card">
            <div className="performers-header">
              <h3>Attention Required</h3>
              <button 
                className="view-all-btn"
                onClick={() => onViewChange('risk-return')}
              >
                View Analysis
              </button>
            </div>
            <div className="performers-list">
              {underperformingMasterBCs.length > 0 ? (
                underperformingMasterBCs.map((mbc, index) => (
                  <div key={mbc.id} className="performer-item attention">
                    <div className="performer-rank warning">
                      <i className="fas fa-exclamation-triangle"></i>
                    </div>
                    <div className="performer-info">
                      <div className="performer-name">{mbc.name}</div>
                      <div className="performer-category">{mbc.category}</div>
                    </div>
                    <div className="performer-metrics">
                      <div className="metric-npv negative">{formatCurrency(mbc.aggregatedMetrics?.totalNPV)}</div>
                      <div className="metric-irr">{formatPercentage(mbc.aggregatedMetrics?.avgIRR)} IRR</div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="empty-state">
                  <i className="fas fa-check-circle"></i>
                  <p>All Master BCs are performing well</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="quick-actions">
        <h3 className="section-title">Quick Actions</h3>
        <div className="actions-grid">
          <button 
            className="action-btn"
            onClick={() => onViewChange('profitability')}
          >
            <i className="fas fa-chart-bar"></i>
            <span>Analyze Profitability</span>
          </button>
          <button 
            className="action-btn"
            onClick={() => onViewChange('comparison')}
          >
            <i className="fas fa-balance-scale"></i>
            <span>Compare Portfolios</span>
          </button>
          <button 
            className="action-btn"
            onClick={() => onViewChange('risk-return')}
          >
            <i className="fas fa-chart-scatter"></i>
            <span>Risk Analysis</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default PortfolioOverview;
