// Simple Financial Modeling API Server with simulated MongoDB storage
const http = require('http');
const url = require('url');
const fs = require('fs');
const path = require('path');

const PORT = process.env.PORT || 5000;
const DATA_DIR = path.join(__dirname, 'data');

// Ensure data directory exists
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// File-based storage (simulating MongoDB collections)
const COLLECTIONS = {
  users: path.join(DATA_DIR, 'users.json'),
  financialData: path.join(DATA_DIR, 'financialData.json'),
  parameters: path.join(DATA_DIR, 'parameters.json'),
  costStructures: path.join(DATA_DIR, 'costStructures.json'),
  salesStructures: path.join(DATA_DIR, 'salesStructures.json'),
  pricingModels: path.join(DATA_DIR, 'pricingModels.json'),
  sensitivityAnalysis: path.join(DATA_DIR, 'sensitivityAnalysis.json')
};

// Initialize data storage
function initializeStorage() {
  console.log('🔄 Initializing file-based storage (simulating MongoDB)...');
  
  // Initialize users collection
  if (!fs.existsSync(COLLECTIONS.users)) {
    const defaultUsers = [
      {
        id: '1',
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'admin',
        password: 'password123',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Financial Analyst',
        email: '<EMAIL>',
        role: 'financial_analyst',
        password: 'password123',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '3',
        name: 'Executive User',
        email: '<EMAIL>',
        role: 'executive',
        password: 'password123',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '4',
        name: 'Gyanesh',
        email: '<EMAIL>',
        role: 'admin',
        password: 'gyanesh123',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
    fs.writeFileSync(COLLECTIONS.users, JSON.stringify(defaultUsers, null, 2));
    console.log('✅ Users collection initialized');
  }

  // Initialize financial data
  if (!fs.existsSync(COLLECTIONS.financialData)) {
    const defaultFinancialData = {
      metrics: {
        npv: 2400000,
        irr: 18.7,
        paybackPeriod: 3.2,
        yieldIndex: 1.45,
        grossMargin: 42.3,
        breakEvenSales: 1800000
      },
      models: [
        {
          id: '1',
          name: 'Product Launch Q1 2024',
          lastModified: '2 hours ago',
          npv: '$2.4M',
          irr: '18.7%',
          status: 'Active',
          createdBy: 'gyanesh',
          createdAt: new Date().toISOString()
        },
        {
          id: '2',
          name: 'Market Expansion Europe',
          lastModified: '1 day ago',
          npv: '$1.8M',
          irr: '15.2%',
          status: 'Draft',
          createdBy: '<EMAIL>',
          createdAt: new Date().toISOString()
        },
        {
          id: '3',
          name: 'Cost Optimization Initiative',
          lastModified: '3 days ago',
          npv: '$890K',
          irr: '22.1%',
          status: 'Completed',
          createdBy: '<EMAIL>',
          createdAt: new Date().toISOString()
        }
      ]
    };
    fs.writeFileSync(COLLECTIONS.financialData, JSON.stringify(defaultFinancialData, null, 2));
    console.log('✅ Financial data collection initialized');
  }

  // Initialize parameters
  if (!fs.existsSync(COLLECTIONS.parameters)) {
    const defaultParameters = {
      discountRate: 10.0,
      taxRate: 25.0,
      financialRate: 5.0,
      currency: 'USD',
      conversionRates: {
        'EUR': 1.08,
        'GBP': 1.25,
        'JPY': 0.0067
      },
      inflationRates: {
        rd: 3.0,
        production: 2.5,
        sales: 2.0
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    fs.writeFileSync(COLLECTIONS.parameters, JSON.stringify(defaultParameters, null, 2));
    console.log('✅ Parameters collection initialized');
  }

  // Initialize cost structures
  if (!fs.existsSync(COLLECTIONS.costStructures)) {
    const defaultCostStructures = [
      {
        id: '1',
        name: 'Manufacturing Setup',
        capex: [
          { id: '1', name: 'Equipment Purchase', amount: 500000, year: 2024, category: 'Manufacturing' },
          { id: '2', name: 'Facility Setup', amount: 200000, year: 2024, category: 'Infrastructure' }
        ],
        opex: [
          { id: '1', name: 'Labor Costs', amount: 120000, startYear: 2024, endYear: 2030, category: 'Personnel', isRecurring: true },
          { id: '2', name: 'Utilities', amount: 24000, startYear: 2024, endYear: 2030, category: 'Operations', isRecurring: true }
        ],
        tools: [
          { id: '1', name: 'Quality Control Equipment', cost: 50000, quantity: 2, lifespan: 5, category: 'QC' }
        ],
        machinery: [
          { id: '1', name: 'Production Line A', cost: 300000, capacity: 1000, lifespan: 10, maintenanceCost: 15000, category: 'Production' }
        ],
        createdBy: 'gyanesh',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
    fs.writeFileSync(COLLECTIONS.costStructures, JSON.stringify(defaultCostStructures, null, 2));
    console.log('✅ Cost structures collection initialized');
  }

  console.log('✅ All collections initialized successfully');
}

// Helper functions for data operations
function readCollection(collectionName) {
  try {
    const filePath = COLLECTIONS[collectionName];
    if (fs.existsSync(filePath)) {
      const data = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error(`Error reading ${collectionName}:`, error);
    return [];
  }
}

function writeCollection(collectionName, data) {
  try {
    const filePath = COLLECTIONS[collectionName];
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    console.error(`Error writing ${collectionName}:`, error);
    return false;
  }
}

function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Helper function to parse JSON body
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(error);
      }
    });
  });
}

// Helper function to send JSON response
function sendJSON(res, statusCode, data) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  res.end(JSON.stringify(data));
}

// Create HTTP server
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathName = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${pathName}`);

  // Handle CORS preflight
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end();
    return;
  }

  try {
    // Health check
    if (pathName === '/health' && method === 'GET') {
      sendJSON(res, 200, {
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        database: 'File-based storage (MongoDB simulation)',
        collections: Object.keys(COLLECTIONS).length
      });
      return;
    }

    // Login endpoint
    if (pathName === '/api/auth/login' && method === 'POST') {
      const body = await parseBody(req);
      const { email, password } = body;

      console.log('🔐 Login attempt:', { email, password });

      const users = readCollection('users');
      console.log('📋 Available users:', users.map(u => ({ email: u.email, password: u.password, role: u.role })));

      const user = users.find(u => u.email === email && u.password === password);
      console.log('👤 Found user:', user ? `${user.name} (${user.role})` : 'Not found');
      
      if (user) {
        sendJSON(res, 200, {
          success: true,
          data: {
            token: 'file-storage-token-' + user.id,
            user: {
              id: user.id,
              name: user.name,
              email: user.email,
              role: user.role,
              createdAt: user.createdAt,
              updatedAt: user.updatedAt
            }
          }
        });
        console.log(`✅ Login successful for ${user.name} (${user.role})`);
      } else {
        sendJSON(res, 400, {
          success: false,
          error: 'Invalid credentials'
        });
        console.log('❌ Login failed: Invalid credentials');
      }
      return;
    }

    // Verify token endpoint
    if (pathName === '/api/auth/verify' && method === 'GET') {
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const userId = token.replace('file-storage-token-', '');
        
        const users = readCollection('users');
        const user = users.find(u => u.id === userId);
        
        if (user) {
          sendJSON(res, 200, {
            success: true,
            data: {
              id: user.id,
              name: user.name,
              email: user.email,
              role: user.role,
              createdAt: user.createdAt,
              updatedAt: user.updatedAt
            }
          });
        } else {
          sendJSON(res, 401, {
            success: false,
            error: 'Invalid token'
          });
        }
      } else {
        sendJSON(res, 401, {
          success: false,
          error: 'No token provided'
        });
      }
      return;
    }

    // Dashboard data endpoint
    if (pathName === '/api/dashboard' && method === 'GET') {
      const financialData = readCollection('financialData');
      
      sendJSON(res, 200, {
        success: true,
        data: financialData
      });
      return;
    }

    // Financial parameters endpoints
    if (pathName === '/api/parameters' && method === 'GET') {
      const parameters = readCollection('parameters');
      
      sendJSON(res, 200, {
        success: true,
        data: parameters
      });
      return;
    }

    if (pathName === '/api/parameters' && method === 'PUT') {
      const body = await parseBody(req);
      body.updatedAt = new Date().toISOString();
      
      const success = writeCollection('parameters', body);
      
      sendJSON(res, 200, {
        success,
        data: { updated: success }
      });
      return;
    }

    // Users management endpoints
    if (pathName === '/api/users' && method === 'GET') {
      const users = readCollection('users');
      // Remove passwords from response
      const safeUsers = users.map(({ password, ...user }) => user);
      
      sendJSON(res, 200, {
        success: true,
        data: safeUsers
      });
      return;
    }

    if (pathName === '/api/users' && method === 'POST') {
      const body = await parseBody(req);
      const users = readCollection('users');
      
      // Check if user already exists
      const existingUser = users.find(u => u.email === body.email);
      if (existingUser) {
        sendJSON(res, 400, {
          success: false,
          error: 'User already exists'
        });
        return;
      }
      
      const newUser = {
        id: generateId(),
        ...body,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      users.push(newUser);
      const success = writeCollection('users', users);
      
      if (success) {
        // Return user without password
        const { password, ...userWithoutPassword } = newUser;
        sendJSON(res, 201, {
          success: true,
          data: userWithoutPassword
        });
        console.log(`✅ New user created: ${newUser.name} (${newUser.email})`);
      } else {
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to create user'
        });
      }
      return;
    }

    // Cost structures endpoints
    if (pathName === '/api/costs' && method === 'GET') {
      const costStructures = readCollection('costStructures');
      sendJSON(res, 200, {
        success: true,
        data: costStructures
      });
      return;
    }

    if (pathName === '/api/costs' && method === 'POST') {
      const body = await parseBody(req);
      const costStructures = readCollection('costStructures');
      
      const newCostStructure = {
        id: generateId(),
        ...body,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      costStructures.push(newCostStructure);
      const success = writeCollection('costStructures', costStructures);
      
      sendJSON(res, success ? 201 : 500, {
        success,
        data: success ? newCostStructure : null,
        error: success ? null : 'Failed to create cost structure'
      });
      return;
    }

    // Placeholder endpoints with storage ready message
    const storageReadyEndpoints = [
      '/api/sales',
      '/api/pricing',
      '/api/sensitivity',
      '/api/financial',
      '/api/export'
    ];

    if (storageReadyEndpoints.some(endpoint => pathName.startsWith(endpoint))) {
      sendJSON(res, 200, {
        success: true,
        data: [],
        message: `${pathName} endpoint - File storage ready, full CRUD implementation available`
      });
      return;
    }

    // 404 for unknown routes
    sendJSON(res, 404, {
      success: false,
      error: 'Route not found'
    });

  } catch (error) {
    console.error('Server error:', error);
    sendJSON(res, 500, {
      success: false,
      error: 'Internal server error'
    });
  }
});

// Start server
function startServer() {
  initializeStorage();
  
  server.listen(PORT, () => {
    console.log(`🚀 Financial Modeling API Server running on port ${PORT}`);
    console.log(`📊 Dashboard: http://localhost:${PORT}/health`);
    console.log(`🗄️  Database: File-based storage (MongoDB simulation)`);
    console.log(`📁 Data directory: ${DATA_DIR}`);
    console.log(`🔐 Demo credentials:`);
    console.log(`   Admin: <EMAIL> / password123`);
    console.log(`   Analyst: <EMAIL> / password123`);
    console.log(`   Executive: <EMAIL> / password123`);
    console.log(`   Gyanesh (Admin): <EMAIL> / gyanesh123`);
    console.log(`\n📡 API Endpoints with CRUD operations:`);
    console.log(`   POST /api/auth/login`);
    console.log(`   GET  /api/auth/verify`);
    console.log(`   GET  /api/dashboard`);
    console.log(`   GET  /api/parameters`);
    console.log(`   PUT  /api/parameters`);
    console.log(`   GET  /api/users`);
    console.log(`   POST /api/users`);
    console.log(`   GET  /api/costs`);
    console.log(`   POST /api/costs`);
  });
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('\nSIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

// Start the server
startServer();
