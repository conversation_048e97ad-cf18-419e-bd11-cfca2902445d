import React from 'react';

const Navigation = ({ 
  isAuthenticated, 
  currentUser, 
  onLogin, 
  onLogout, 
  onFeedback 
}) => {
  return (
    <nav className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and Title */}
          <div className="flex items-center">
            <i className="fas fa-briefcase text-blue-600 text-2xl mr-3"></i>
            <h1 className="text-xl font-bold text-gray-900">Business Case Management</h1>
          </div>

          {/* Navigation Actions */}
          <div className="flex items-center space-x-4">
            {/* Feedback Button */}
            <button
              onClick={onFeedback}
              className="text-gray-600 hover:text-green-600 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors"
              title="Give Feedback"
            >
              <i className="fas fa-comment text-lg"></i>
            </button>

            {/* User Info and Auth Buttons */}
            {isAuthenticated ? (
              <>
                <span className="text-sm text-gray-500">
                  Welcome, {currentUser?.name || 'User'}
                </span>
                <button
                  onClick={onLogout}
                  className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Logout
                </button>
              </>
            ) : (
              <>
                <span className="text-sm text-gray-500">Not logged in</span>
                <button
                  onClick={onLogin}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Login
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};



export default Navigation;
