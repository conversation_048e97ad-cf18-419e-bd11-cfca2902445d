.relationship-matrix {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Matrix Header */
.matrix-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.matrix-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.matrix-title p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.matrix-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.view-mode-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 6px;
  padding: 2px;
}

.toggle-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  background: transparent;
  color: #6b7280;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.toggle-btn:hover {
  color: #374151;
}

.toggle-btn.active {
  background: #3b82f6;
  color: white;
}

/* Matrix Content */
.matrix-content {
  display: grid;
  grid-template-columns: 1fr 280px;
  gap: 20px;
  flex: 1;
  overflow: hidden;
}

.matrix-wrapper {
  overflow: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
}

.matrix-container {
  display: grid;
  grid-template-rows: auto 1fr;
  min-width: fit-content;
}

/* Column Headers */
.matrix-col-headers {
  display: grid;
  grid-template-columns: 200px repeat(var(--cols, 10), 40px);
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 2;
}

.corner-cell {
  border-right: 1px solid #e5e7eb;
  background: #f3f4f6;
}

.col-header {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  border-right: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.2s;
}

.col-header:hover {
  transform: scale(1.1);
}

/* Matrix Rows */
.matrix-rows {
  display: grid;
  grid-template-rows: repeat(var(--rows, 10), 40px);
}

.matrix-row {
  display: grid;
  grid-template-columns: 200px repeat(var(--cols, 10), 40px);
  border-bottom: 1px solid #f3f4f6;
}

.matrix-row:hover {
  background: #f9fafb;
}

/* Row Headers */
.row-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 12px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  border-right: 1px solid #e5e7eb;
  position: sticky;
  left: 0;
  z-index: 1;
  cursor: pointer;
  transition: all 0.2s;
}

.row-header:hover {
  filter: brightness(1.1);
}

.row-label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

/* Matrix Cells */
.matrix-cell {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #f3f4f6;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
}

.matrix-cell:hover {
  background: #f3f4f6;
  transform: scale(1.1);
}

.matrix-cell.diagonal {
  background: #f3f4f6;
  color: #6b7280;
  cursor: default;
}

.matrix-cell.diagonal:hover {
  transform: none;
}

.matrix-cell.connected {
  background: #d1fae5;
  color: #065f46;
}

.matrix-cell.connected:hover {
  background: #a7f3d0;
}

.matrix-cell.disconnected {
  background: white;
  color: #d1d5db;
}

.matrix-cell.disconnected:hover {
  background: #f9fafb;
}

.matrix-cell.selected {
  background: #3b82f6 !important;
  color: white !important;
  transform: scale(1.2);
  z-index: 3;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.empty-cell {
  font-size: 16px;
  color: #e5e7eb;
}

/* Connection Details Panel */
.connection-details {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.details-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.close-details {
  width: 24px;
  height: 24px;
  border: none;
  background: #f3f4f6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  color: #6b7280;
  font-size: 12px;
}

.close-details:hover {
  background: #e5e7eb;
  color: #374151;
}

.details-content {
  padding: 16px;
  flex: 1;
}

/* Connection Entities */
.connection-entities {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.entity-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.entity-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  flex-shrink: 0;
}

.entity-info {
  flex: 1;
  min-width: 0;
}

.entity-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.entity-type {
  font-size: 12px;
  color: #6b7280;
  font-weight: 400;
}

/* Connection Indicator */
.connection-indicator {
  display: flex;
  justify-content: center;
  margin: 8px 0;
}

.connected-indicator,
.disconnected-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.connected-indicator {
  background: #d1fae5;
  color: #065f46;
}

.disconnected-indicator {
  background: #fee2e2;
  color: #991b1b;
}

/* Connection Info */
.connection-info {
  display: grid;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
}

.info-item label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
}

.info-item span {
  font-size: 12px;
  color: #1f2937;
  font-weight: 500;
}

/* Legend */
.matrix-legend {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
  padding: 16px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.legend-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.legend-items {
  display: grid;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #374151;
}

.legend-icon {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  flex-shrink: 0;
}

.legend-symbol {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  flex-shrink: 0;
}

.legend-symbol.connected {
  background: #d1fae5;
  color: #065f46;
}

.legend-symbol.disconnected {
  background: white;
  border: 1px solid #e5e7eb;
  color: #d1d5db;
  font-size: 14px;
}

.legend-symbol.diagonal {
  background: #f3f4f6;
  color: #6b7280;
}

.count {
  color: #6b7280;
  font-weight: 400;
}

/* Statistics Summary */
.matrix-stats {
  margin-top: 20px;
  padding: 16px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-state i {
  font-size: 48px;
  color: #9ca3af;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #374151;
}

.empty-state p {
  margin: 0;
  color: #6b7280;
}
