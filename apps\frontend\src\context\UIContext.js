import React, { createContext, useContext, useState } from 'react';

const UIContext = createContext();

export const useUI = () => {
  const context = useContext(UIContext);
  if (!context) {
    throw new Error('useUI must be used within a UIProvider');
  }
  return context;
};

export const UIProvider = ({ children }) => {
  // Active tab state
  const [activeTab, setActiveTab] = useState('dashboard');
  
  // Modal states
  const [modals, setModals] = useState({
    login: false,
    feedback: false,
    excelUpload: false,
    tour: false,
    businessCaseForm: false,
    projectForm: false,
    programForm: false
  });

  // Loading states for UI components
  const [uiLoading, setUILoading] = useState({
    charts: false,
    tables: false,
    forms: false
  });

  // Notification state
  const [notifications, setNotifications] = useState([]);

  // Tour state
  const [tourState, setTourState] = useState({
    isActive: false,
    currentStep: 0,
    steps: []
  });

  // Sidebar state (for mobile)
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Theme state
  const [theme, setTheme] = useState('light');

  // Modal management
  const openModal = (modalName) => {
    setModals(prev => ({ ...prev, [modalName]: true }));
  };

  const closeModal = (modalName) => {
    setModals(prev => ({ ...prev, [modalName]: false }));
  };

  const closeAllModals = () => {
    setModals({
      login: false,
      feedback: false,
      excelUpload: false,
      tour: false,
      businessCaseForm: false,
      projectForm: false,
      programForm: false
    });
  };

  // Notification management
  const addNotification = (notification) => {
    const id = Date.now();
    const newNotification = {
      id,
      type: 'info',
      autoClose: true,
      duration: 5000,
      ...notification
    };
    
    setNotifications(prev => [...prev, newNotification]);

    if (newNotification.autoClose) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }

    return id;
  };

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  // Tour management
  const startTour = (steps) => {
    setTourState({
      isActive: true,
      currentStep: 0,
      steps
    });
  };

  const nextTourStep = () => {
    setTourState(prev => ({
      ...prev,
      currentStep: prev.currentStep + 1
    }));
  };

  const prevTourStep = () => {
    setTourState(prev => ({
      ...prev,
      currentStep: Math.max(0, prev.currentStep - 1)
    }));
  };

  const endTour = () => {
    setTourState({
      isActive: false,
      currentStep: 0,
      steps: []
    });
  };

  // Loading management
  const setLoading = (component, isLoading) => {
    setUILoading(prev => ({ ...prev, [component]: isLoading }));
  };

  // Tab management
  const switchTab = (tabName) => {
    setActiveTab(tabName);
  };

  // Theme management
  const toggleTheme = () => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light');
  };

  const value = {
    // State
    activeTab,
    modals,
    uiLoading,
    notifications,
    tourState,
    sidebarOpen,
    theme,

    // Tab management
    switchTab,
    setActiveTab,

    // Modal management
    openModal,
    closeModal,
    closeAllModals,

    // Notification management
    addNotification,
    removeNotification,
    clearAllNotifications,

    // Tour management
    startTour,
    nextTourStep,
    prevTourStep,
    endTour,

    // Loading management
    setLoading,

    // Sidebar management
    setSidebarOpen,

    // Theme management
    toggleTheme,

    // Utility functions
    showSuccess: (message) => addNotification({ type: 'success', message }),
    showError: (message) => addNotification({ type: 'error', message }),
    showWarning: (message) => addNotification({ type: 'warning', message }),
    showInfo: (message) => addNotification({ type: 'info', message })
  };

  return (
    <UIContext.Provider value={value}>
      {children}
    </UIContext.Provider>
  );
};
