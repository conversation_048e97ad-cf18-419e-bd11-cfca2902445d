#!/usr/bin/env node

const http = require('http');

// Configuration
const BACKEND_PORT = process.env.PORT || 5001;
const FRONTEND_PORT = 3000;
const BACKEND_URL = `http://localhost:${BACKEND_PORT}`;
const FRONTEND_URL = `http://localhost:${FRONTEND_PORT}`;

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkPort(url, serviceName) {
  return new Promise((resolve) => {
    const request = http.get(url, (res) => {
      log('green', `✅ ${serviceName} is running on ${url}`);
      resolve(true);
    });

    request.on('error', (err) => {
      log('red', `❌ ${serviceName} is NOT running on ${url}`);
      log('yellow', `   Error: ${err.message}`);
      resolve(false);
    });

    request.setTimeout(5000, () => {
      log('red', `❌ ${serviceName} timeout on ${url}`);
      resolve(false);
    });
  });
}

async function healthCheck() {
  log('blue', '🔍 Performing health check...\n');

  const backendHealthy = await checkPort(`${BACKEND_URL}/health`, 'Backend API');
  const frontendHealthy = await checkPort(FRONTEND_URL, 'Frontend React App');

  console.log('\n' + '='.repeat(50));
  
  if (backendHealthy && frontendHealthy) {
    log('green', '🎉 All services are running correctly!');
    log('blue', `📱 Frontend: ${FRONTEND_URL}`);
    log('blue', `🔧 Backend API: ${BACKEND_URL}`);
  } else {
    log('red', '⚠️  Some services are not running:');
    if (!backendHealthy) {
      log('yellow', '   • Start backend: npm run dev:backend');
    }
    if (!frontendHealthy) {
      log('yellow', '   • Start frontend: npm run dev:frontend');
    }
  }

  console.log('='.repeat(50));
}

// Run health check
healthCheck().catch(console.error);
