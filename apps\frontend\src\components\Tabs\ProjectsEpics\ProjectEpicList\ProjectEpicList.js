import React, { useState } from 'react';
import { projectService } from '../../../../services/projectService';
import './ProjectEpicList.css';

const ProjectEpicList = ({ projects, businessCases, programs, onEdit, onView, onRefresh }) => {
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState({
    search: '',
    type: '',
    status: '',
    program: ''
  });

  // Filter projects based on current filters
  const filteredProjects = projects.filter(project => {
    const matchesSearch = !filter.search || 
      project.name.toLowerCase().includes(filter.search.toLowerCase()) ||
      (project.description && project.description.toLowerCase().includes(filter.search.toLowerCase()));
    
    const matchesType = !filter.type || project.type === filter.type;
    const matchesStatus = !filter.status || project.status === filter.status;
    const matchesProgram = !filter.program || project.associatedProgram === filter.program;

    return matchesSearch && matchesType && matchesStatus && matchesProgram;
  });

  const handleFilterChange = (key, value) => {
    setFilter(prev => ({ ...prev, [key]: value }));
  };

  const handleDelete = async (projectId) => {
    if (!window.confirm('Are you sure you want to delete this project/epic?')) {
      return;
    }

    setLoading(true);
    try {
      await projectService.deleteProject(projectId);
      onRefresh();
    } catch (error) {
      console.error('Failed to delete project:', error);
      alert('Failed to delete project. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      planning: '#f59e0b',
      active: '#10b981',
      'on-hold': '#6b7280',
      completed: '#3b82f6',
      cancelled: '#dc2626'
    };
    return colors[status] || '#6b7280';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      low: '#10b981',
      medium: '#f59e0b',
      high: '#f97316',
      critical: '#dc2626'
    };
    return colors[priority] || '#6b7280';
  };

  const getLinkedBusinessCasesNames = (linkedBCIds) => {
    if (!linkedBCIds || linkedBCIds.length === 0) return 'None';
    
    return linkedBCIds
      .map(bcId => {
        const bc = businessCases.find(bc => bc.id === bcId);
        return bc ? bc.name : `BC-${bcId}`;
      })
      .join(', ');
  };

  const getAssociatedProgramName = (programId) => {
    if (!programId) return 'None';
    
    const program = programs.find(p => p.id === programId);
    return program ? program.name : `Program-${programId}`;
  };

  if (!projects || projects.length === 0) {
    return (
      <div className="project-epic-list">
        <div className="empty-state">
          <i className="fas fa-project-diagram"></i>
          <h3>No Projects or Epics</h3>
          <p>Create your first project or epic to get started with milestone tracking and business case linking.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="project-epic-list">
      {/* Filters */}
      <div className="list-filters">
        <div className="filter-group">
          <input
            type="text"
            placeholder="Search projects and epics..."
            value={filter.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className="filter-input"
          />
        </div>
        
        <div className="filter-group">
          <select
            value={filter.type}
            onChange={(e) => handleFilterChange('type', e.target.value)}
            className="filter-select"
          >
            <option value="">All Types</option>
            <option value="project">Projects</option>
            <option value="epic">Epics</option>
          </select>
        </div>

        <div className="filter-group">
          <select
            value={filter.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            className="filter-select"
          >
            <option value="">All Statuses</option>
            <option value="planning">Planning</option>
            <option value="active">Active</option>
            <option value="on-hold">On Hold</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        <div className="filter-group">
          <select
            value={filter.program}
            onChange={(e) => handleFilterChange('program', e.target.value)}
            className="filter-select"
          >
            <option value="">All Programs</option>
            {programs && programs.map(program => (
              <option key={program.id} value={program.id}>
                {program.name}
              </option>
            ))}
          </select>
        </div>

        <div className="filter-actions">
          <button
            onClick={() => setFilter({ search: '', type: '', status: '', program: '' })}
            className="btn btn-outline btn-sm"
          >
            Clear Filters
          </button>
        </div>
      </div>

      {/* Results Summary */}
      <div className="results-summary">
        <p>
          Showing {filteredProjects.length} of {projects.length} projects/epics
          {filter.search && ` matching "${filter.search}"`}
        </p>
      </div>

      {/* Projects Grid */}
      <div className="projects-grid">
        {filteredProjects.map(project => (
          <div key={project.id} className="project-card">
            <div className="card-header">
              <div className="card-title-section">
                <div className="card-type">
                  <i className={project.type === 'epic' ? 'fas fa-layer-group' : 'fas fa-project-diagram'}></i>
                  {project.type === 'epic' ? 'Epic' : 'Project'}
                </div>
                <h3 className="card-title">{project.name}</h3>
              </div>
              <div className="card-actions">
                <button
                  onClick={() => onView(project)}
                  className="btn btn-outline btn-sm"
                  title="View Details"
                >
                  <i className="fas fa-eye"></i>
                </button>
                <button
                  onClick={() => onEdit(project)}
                  className="btn btn-outline btn-sm"
                  title="Edit"
                >
                  <i className="fas fa-edit"></i>
                </button>
                <button
                  onClick={() => handleDelete(project.id)}
                  className="btn btn-danger btn-sm"
                  title="Delete"
                  disabled={loading}
                >
                  <i className="fas fa-trash"></i>
                </button>
              </div>
            </div>

            <div className="card-content">
              {project.description && (
                <p className="card-description">{project.description}</p>
              )}

              <div className="card-info">
                <div className="info-row">
                  <span className="info-label">Status:</span>
                  <span 
                    className="status-badge"
                    style={{ backgroundColor: getStatusColor(project.status) }}
                  >
                    {project.status}
                  </span>
                </div>

                <div className="info-row">
                  <span className="info-label">Priority:</span>
                  <span 
                    className="priority-badge"
                    style={{ backgroundColor: getPriorityColor(project.priority) }}
                  >
                    {project.priority}
                  </span>
                </div>

                {project.owner && (
                  <div className="info-row">
                    <span className="info-label">Owner:</span>
                    <span className="info-value">{project.owner}</span>
                  </div>
                )}

                {project.startDate && (
                  <div className="info-row">
                    <span className="info-label">Start Date:</span>
                    <span className="info-value">
                      {new Date(project.startDate).toLocaleDateString()}
                    </span>
                  </div>
                )}

                {project.endDate && (
                  <div className="info-row">
                    <span className="info-label">End Date:</span>
                    <span className="info-value">
                      {new Date(project.endDate).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div className="card-footer">
              <div className="footer-section">
                <div className="footer-label">Associated Program:</div>
                <div className="footer-value">
                  {getAssociatedProgramName(project.associatedProgram)}
                </div>
              </div>

              <div className="footer-section">
                <div className="footer-label">Linked Business Cases:</div>
                <div className="footer-value">
                  {getLinkedBusinessCasesNames(project.linkedBusinessCases)}
                </div>
              </div>

              {project.milestones && project.milestones.length > 0 && (
                <div className="footer-section">
                  <div className="footer-label">Milestones:</div>
                  <div className="footer-value">
                    {project.milestones.length} defined
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {filteredProjects.length === 0 && filter.search && (
        <div className="no-results">
          <i className="fas fa-search"></i>
          <h3>No Results Found</h3>
          <p>No projects or epics match your current filters. Try adjusting your search criteria.</p>
        </div>
      )}
    </div>
  );
};

export default ProjectEpicList;
