import mongoose, { Document, Schema } from 'mongoose';

export interface IBusinessCase extends Document {
  name: string;
  description?: string;
  tags?: string[];
  businessUnit?: string;
  timeframe: {
    startYear: number;
    endYear: number;
  };
  financialData: {
    capex: Array<{
      year: number;
      amount: number;
      description?: string;
    }>;
    opex: Array<{
      year: number;
      amount: number;
      description?: string;
    }>;
    revenue?: Array<{
      year: number;
      amount: number;
      description?: string;
    }>;
    totalCapex: number;
    totalOpex: number;
  };
  calculatedMetrics?: {
    irr?: number;
    npv?: number;
    paybackPeriod?: number;
    grossMargin?: number;
    commercialMargin?: number;
  };
  status: 'draft' | 'active' | 'completed' | 'archived';
  createdBy: string;
  lastModifiedBy: string;
  createdAt: Date;
  updatedAt: Date;
}

const businessCaseSchema = new Schema<IBusinessCase>({
  name: {
    type: String,
    required: [true, 'Business case name is required'],
    trim: true,
    maxlength: [200, 'Name cannot exceed 200 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  tags: [{
    type: String,
    trim: true
  }],
  businessUnit: {
    type: String,
    trim: true,
    maxlength: [100, 'Business unit cannot exceed 100 characters']
  },
  timeframe: {
    startYear: {
      type: Number,
      required: [true, 'Start year is required'],
      min: [2020, 'Start year must be 2020 or later'],
      max: [2050, 'Start year cannot exceed 2050']
    },
    endYear: {
      type: Number,
      required: [true, 'End year is required'],
      min: [2020, 'End year must be 2020 or later'],
      max: [2050, 'End year cannot exceed 2050']
    }
  },
  financialData: {
    capex: [{
      year: {
        type: Number,
        required: true
      },
      amount: {
        type: Number,
        required: true,
        min: [0, 'CAPEX amount cannot be negative']
      },
      description: {
        type: String,
        trim: true
      }
    }],
    opex: [{
      year: {
        type: Number,
        required: true
      },
      amount: {
        type: Number,
        required: true,
        min: [0, 'OPEX amount cannot be negative']
      },
      description: {
        type: String,
        trim: true
      }
    }],
    revenue: [{
      year: {
        type: Number,
        required: true
      },
      amount: {
        type: Number,
        required: true,
        min: [0, 'Revenue amount cannot be negative']
      },
      description: {
        type: String,
        trim: true
      }
    }],
    totalCapex: {
      type: Number,
      required: true,
      min: [0, 'Total CAPEX cannot be negative']
    },
    totalOpex: {
      type: Number,
      required: true,
      min: [0, 'Total OPEX cannot be negative']
    }
  },
  calculatedMetrics: {
    irr: Number,
    npv: Number,
    paybackPeriod: Number,
    grossMargin: Number,
    commercialMargin: Number
  },
  status: {
    type: String,
    enum: ['draft', 'active', 'completed', 'archived'],
    default: 'draft'
  },
  createdBy: {
    type: String,
    required: [true, 'Creator is required']
  },
  lastModifiedBy: {
    type: String,
    required: [true, 'Last modifier is required']
  }
}, {
  timestamps: true
});

// Validate that end year is after start year
businessCaseSchema.pre('save', function(next) {
  if (this.timeframe.endYear <= this.timeframe.startYear) {
    next(new Error('End year must be after start year'));
  }
  next();
});

// Calculate total CAPEX and OPEX before saving
businessCaseSchema.pre('save', function(next) {
  this.financialData.totalCapex = this.financialData.capex.reduce((sum, item) => sum + item.amount, 0);
  this.financialData.totalOpex = this.financialData.opex.reduce((sum, item) => sum + item.amount, 0);
  next();
});

// Index for efficient searching
businessCaseSchema.index({ name: 'text', description: 'text', tags: 'text' });
businessCaseSchema.index({ createdBy: 1 });
businessCaseSchema.index({ status: 1 });
businessCaseSchema.index({ 'timeframe.startYear': 1, 'timeframe.endYear': 1 });

export const BusinessCase = mongoose.model<IBusinessCase>('BusinessCase', businessCaseSchema);
