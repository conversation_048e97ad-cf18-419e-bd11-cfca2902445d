"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getBusinessCaseStats = exports.exportBusinessCase = exports.deleteBusinessCase = exports.updateBusinessCase = exports.createBusinessCase = exports.getBusinessCase = exports.getBusinessCases = void 0;
const BusinessCase_1 = require("../models/BusinessCase");
const financialCalculations_1 = require("../services/financialCalculations");
const excelExport_1 = require("../services/excelExport");
const getBusinessCases = async (req, res) => {
    try {
        const { page = 1, limit = 10, search, status, startYear, endYear, createdBy } = req.query;
        const query = {};
        if (search) {
            query.$text = { $search: search };
        }
        if (status) {
            query.status = status;
        }
        if (startYear || endYear) {
            query['timeframe.startYear'] = {};
            if (startYear)
                query['timeframe.startYear'].$gte = parseInt(startYear);
            if (endYear)
                query['timeframe.endYear'] = { $lte: parseInt(endYear) };
        }
        if (createdBy) {
            query.createdBy = createdBy;
        }
        const options = {
            page: parseInt(page),
            limit: parseInt(limit),
            sort: { updatedAt: -1 },
            populate: []
        };
        const businessCases = await BusinessCase_1.BusinessCase.find(query)
            .sort(options.sort)
            .limit(options.limit * options.page)
            .skip((options.page - 1) * options.limit)
            .exec();
        const total = await BusinessCase_1.BusinessCase.countDocuments(query);
        res.json({
            success: true,
            data: {
                businessCases,
                pagination: {
                    page: options.page,
                    limit: options.limit,
                    total,
                    pages: Math.ceil(total / options.limit)
                }
            }
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
};
exports.getBusinessCases = getBusinessCases;
const getBusinessCase = async (req, res) => {
    try {
        const businessCase = await BusinessCase_1.BusinessCase.findById(req.params.id);
        if (!businessCase) {
            return res.status(404).json({
                success: false,
                error: 'Business case not found'
            });
        }
        res.json({
            success: true,
            data: businessCase
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
};
exports.getBusinessCase = getBusinessCase;
const createBusinessCase = async (req, res) => {
    try {
        const { name, description, tags, businessUnit, timeframe, financialData } = req.body;
        const calculatedMetrics = financialCalculations_1.FinancialCalculationService.calculateBusinessCaseMetrics({
            timeframe,
            financialData
        });
        const businessCase = await BusinessCase_1.BusinessCase.create({
            name,
            description,
            tags,
            businessUnit,
            timeframe,
            financialData,
            calculatedMetrics,
            createdBy: req.user?.email || 'unknown',
            lastModifiedBy: req.user?.email || 'unknown'
        });
        res.status(201).json({
            success: true,
            data: businessCase
        });
    }
    catch (error) {
        res.status(400).json({
            success: false,
            error: error.message
        });
    }
};
exports.createBusinessCase = createBusinessCase;
const updateBusinessCase = async (req, res) => {
    try {
        const businessCase = await BusinessCase_1.BusinessCase.findById(req.params.id);
        if (!businessCase) {
            return res.status(404).json({
                success: false,
                error: 'Business case not found'
            });
        }
        const { name, description, tags, businessUnit, timeframe, financialData, status } = req.body;
        let calculatedMetrics = businessCase.calculatedMetrics;
        if (financialData || timeframe) {
            calculatedMetrics = financialCalculations_1.FinancialCalculationService.calculateBusinessCaseMetrics({
                timeframe: timeframe || businessCase.timeframe,
                financialData: financialData || businessCase.financialData
            });
        }
        const updatedBusinessCase = await BusinessCase_1.BusinessCase.findByIdAndUpdate(req.params.id, {
            name,
            description,
            tags,
            businessUnit,
            timeframe,
            financialData,
            status,
            calculatedMetrics,
            lastModifiedBy: req.user?.email || 'unknown'
        }, {
            new: true,
            runValidators: true
        });
        res.json({
            success: true,
            data: updatedBusinessCase
        });
    }
    catch (error) {
        res.status(400).json({
            success: false,
            error: error.message
        });
    }
};
exports.updateBusinessCase = updateBusinessCase;
const deleteBusinessCase = async (req, res) => {
    try {
        const businessCase = await BusinessCase_1.BusinessCase.findById(req.params.id);
        if (!businessCase) {
            return res.status(404).json({
                success: false,
                error: 'Business case not found'
            });
        }
        await BusinessCase_1.BusinessCase.findByIdAndDelete(req.params.id);
        res.json({
            success: true,
            data: {}
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
};
exports.deleteBusinessCase = deleteBusinessCase;
const exportBusinessCase = async (req, res) => {
    try {
        const businessCase = await BusinessCase_1.BusinessCase.findById(req.params.id);
        if (!businessCase) {
            return res.status(404).json({
                success: false,
                error: 'Business case not found'
            });
        }
        const excelBuffer = await excelExport_1.ExcelExportService.exportBusinessCase(businessCase);
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="${businessCase.name}_financial_analysis.xlsx"`);
        res.send(excelBuffer);
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
};
exports.exportBusinessCase = exportBusinessCase;
const getBusinessCaseStats = async (req, res) => {
    try {
        const stats = await BusinessCase_1.BusinessCase.aggregate([
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 },
                    totalCapex: { $sum: '$financialData.totalCapex' },
                    totalOpex: { $sum: '$financialData.totalOpex' },
                    avgIRR: { $avg: '$calculatedMetrics.irr' },
                    avgNPV: { $avg: '$calculatedMetrics.npv' }
                }
            }
        ]);
        const totalStats = await BusinessCase_1.BusinessCase.aggregate([
            {
                $group: {
                    _id: null,
                    totalBusinessCases: { $sum: 1 },
                    totalInvestment: { $sum: { $add: ['$financialData.totalCapex', '$financialData.totalOpex'] } },
                    avgPaybackPeriod: { $avg: '$calculatedMetrics.paybackPeriod' }
                }
            }
        ]);
        res.json({
            success: true,
            data: {
                byStatus: stats,
                overall: totalStats[0] || {}
            }
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
};
exports.getBusinessCaseStats = getBusinessCaseStats;
//# sourceMappingURL=businessCaseController.js.map