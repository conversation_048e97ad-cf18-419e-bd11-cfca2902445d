/**
 * MasterBC - App Initialization Hook
 * 
 * <AUTHOR>
 * @repository https://github.com/mahegyaneshpandey/spm
 * @date January 27, 2025
 */

import { useState, useEffect } from 'react';
import { authService } from '../services/authService';
import { AUTH_CONFIG, ERROR_MESSAGES } from '../utils/constants';

/**
 * Custom hook for handling application initialization
 * 
 * Manages:
 * - Authentication state restoration from localStorage
 * - Initial data loading
 * - Error handling during startup
 * - Loading states
 * 
 * @returns {Object} - { isLoading, error, isAuthenticated, currentUser }
 */
export const useAppInitialization = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Check for existing authentication token
        const token = localStorage.getItem(AUTH_CONFIG.TOKEN_KEY);
        const savedUser = localStorage.getItem(AUTH_CONFIG.USER_KEY);

        if (token && savedUser) {
          try {
            // Validate the stored token
            const user = await authService.validateToken(token);
            
            if (user) {
              setIsAuthenticated(true);
              setCurrentUser(user);
            } else {
              // Token is invalid, clear stored data
              clearAuthData();
            }
          } catch (authError) {
            console.warn('Token validation failed:', authError);
            clearAuthData();
          }
        }

        // Perform any additional initialization tasks here
        await performAdditionalInitialization();

      } catch (initError) {
        console.error('App initialization failed:', initError);
        setError(ERROR_MESSAGES.SERVER_ERROR);
      } finally {
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []);

  /**
   * Clear authentication data from localStorage
   */
  const clearAuthData = () => {
    localStorage.removeItem(AUTH_CONFIG.TOKEN_KEY);
    localStorage.removeItem(AUTH_CONFIG.USER_KEY);
    setIsAuthenticated(false);
    setCurrentUser(null);
  };

  /**
   * Perform additional initialization tasks
   * This can include:
   * - Checking API connectivity
   * - Loading application configuration
   * - Setting up error tracking
   * - Initializing analytics
   */
  const performAdditionalInitialization = async () => {
    try {
      // Check API connectivity
      const healthCheck = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/health`, {
        method: 'GET',
        timeout: 5000,
      });

      if (!healthCheck.ok) {
        throw new Error('API health check failed');
      }

      // Load application configuration if needed
      // await loadAppConfiguration();

      // Initialize error tracking
      // initializeErrorTracking();

      // Set up analytics
      // initializeAnalytics();

    } catch (error) {
      console.warn('Additional initialization tasks failed:', error);
      // Don't throw here as these are non-critical failures
    }
  };

  return {
    isLoading,
    error,
    isAuthenticated,
    currentUser,
  };
};
