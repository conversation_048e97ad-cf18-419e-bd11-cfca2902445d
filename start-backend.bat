@echo off
title Master Business Case Management - Backend Launcher
color 0B

echo ========================================
echo  Master Business Case Management
echo  Backend API Server Launcher
echo ========================================
echo.

echo [INFO] Starting Backend API Server...
echo [INFO] This will start the API server on http://localhost:5000
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo [INFO] Please install Node.js from: https://nodejs.org/
    echo [INFO] Press any key to exit...
    pause >nul
    exit /b 1
)

echo [INFO] Node.js version:
node --version
echo.

REM Navigate to backend directory
echo [INFO] Navigating to backend directory...
cd /d "%~dp0apps\backend"

if not exist "package.json" (
    echo [ERROR] Backend directory not found or invalid
    echo [INFO] Make sure you're running this from the spm project root
    echo [INFO] Press any key to exit...
    pause >nul
    exit /b 1
)

REM Check if node_modules exists, if not install dependencies
if not exist "node_modules" (
    echo [INFO] Installing backend dependencies for the first time...
    echo [INFO] This may take 1-2 minutes...
    echo.
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install backend dependencies
        echo [INFO] Press any key to exit...
        pause >nul
        exit /b 1
    )
    echo.
    echo [SUCCESS] Backend dependencies installed successfully!
    echo.
)

echo [INFO] Starting Node.js API server...
echo [INFO] Backend API URL: http://localhost:5000
echo.
echo [INFO] To stop the server, press Ctrl+C in this window
echo.

REM Start the backend server
npm start

REM If we reach here, the server has stopped
echo.
echo [INFO] Backend server has stopped
echo [INFO] Press any key to exit...
pause >nul
