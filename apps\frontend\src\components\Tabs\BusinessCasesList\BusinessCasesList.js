import React, { useState, useEffect } from 'react';
import { useData } from '../../../context/DataContext';
import AdvancedFilters from './AdvancedFilters';
import BusinessCasesTable from './BusinessCasesTable';
import BusinessCaseDetailModal from './BusinessCaseDetailModal';
import BusinessCasesSummary from './BusinessCasesSummary';
import businessCaseService from '../../../services/businessCaseService';

const BusinessCasesList = () => {
  const { businessCases, loadBusinessCases } = useData();
  const [filteredBusinessCases, setFilteredBusinessCases] = useState([]);
  const [filters, setFilters] = useState({});
  const [selectedBusinessCase, setSelectedBusinessCase] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [notification, setNotification] = useState(null);

  useEffect(() => {
    // Load business cases when component mounts
    if (!businessCases || businessCases.length === 0) {
      loadBusinessCases();
    }
  }, [businessCases, loadBusinessCases]);

  useEffect(() => {
    // Apply filters whenever business cases or filters change
    if (businessCases) {
      applyFilters();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [businessCases, filters]);

  const showNotification = (message, type = 'success') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  };

  const applyFilters = () => {
    if (!businessCases) return;

    let filtered = [...businessCases];

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(bc =>
        bc.name.toLowerCase().includes(searchLower) ||
        bc.description.toLowerCase().includes(searchLower) ||
        (bc.tags && bc.tags.some(tag => tag.toLowerCase().includes(searchLower)))
      );
    }

    // Status filter
    if (filters.status) {
      filtered = filtered.filter(bc => bc.status === filters.status);
    }

    // Business unit filter
    if (filters.businessUnit) {
      filtered = filtered.filter(bc => bc.businessUnit === filters.businessUnit);
    }

    // Year filters
    if (filters.startYear) {
      filtered = filtered.filter(bc => bc.startYear >= parseInt(filters.startYear));
    }
    if (filters.endYear) {
      filtered = filtered.filter(bc => bc.endYear <= parseInt(filters.endYear));
    }

    // Revenue filters
    if (filters.minRevenue) {
      filtered = filtered.filter(bc => (bc.totalRevenue || 0) >= parseFloat(filters.minRevenue));
    }
    if (filters.maxRevenue) {
      filtered = filtered.filter(bc => (bc.totalRevenue || 0) <= parseFloat(filters.maxRevenue));
    }

    // ROI filters
    if (filters.minROI || filters.maxROI) {
      filtered = filtered.filter(bc => {
        const investment = (bc.totalCapex || 0) + (bc.totalOpex || 0);
        const revenue = bc.totalRevenue || 0;
        const roi = investment > 0 ? ((revenue - investment) / investment) * 100 : 0;

        if (filters.minROI && roi < parseFloat(filters.minROI)) return false;
        if (filters.maxROI && roi > parseFloat(filters.maxROI)) return false;
        return true;
      });
    }

    // Promoted from idea filter
    if (filters.promotedFromIdea === 'yes') {
      filtered = filtered.filter(bc => bc.promotedFromIdea);
    } else if (filters.promotedFromIdea === 'no') {
      filtered = filtered.filter(bc => !bc.promotedFromIdea);
    }

    // Tags filter
    if (filters.tags) {
      const filterTags = filters.tags.split(',').map(tag => tag.trim().toLowerCase());
      filtered = filtered.filter(bc =>
        bc.tags && filterTags.some(filterTag =>
          bc.tags.some(bcTag => bcTag.toLowerCase().includes(filterTag))
        )
      );
    }

    // Date range filter
    if (filters.dateRange && filters.dateRange !== 'all') {
      const now = new Date();
      let startDate;

      switch (filters.dateRange) {
        case 'last30':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case 'last90':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        case 'thisYear':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        case 'lastYear':
          startDate = new Date(now.getFullYear() - 1, 0, 1);
          const endDate = new Date(now.getFullYear() - 1, 11, 31);
          filtered = filtered.filter(bc => {
            const bcDate = new Date(bc.updatedAt);
            return bcDate >= startDate && bcDate <= endDate;
          });
          break;
        default:
          break;
      }

      if (startDate && filters.dateRange !== 'lastYear') {
        filtered = filtered.filter(bc => new Date(bc.updatedAt) >= startDate);
      }
    }

    setFilteredBusinessCases(filtered);
  };

  const handleView = (businessCase) => {
    setSelectedBusinessCase(businessCase);
    setShowDetailModal(true);
  };

  const handleEdit = (businessCase) => {
    // Navigate to edit form (this would typically use React Router)
    console.log('Edit business case:', businessCase);
    showNotification('Edit functionality would navigate to edit form', 'info');
  };

  const handleDelete = async (businessCase) => {
    if (window.confirm(`Are you sure you want to delete "${businessCase.name}"?`)) {
      setIsLoading(true);
      try {
        const response = await businessCaseService.delete(businessCase.id);
        if (response.success) {
          showNotification('Business case deleted successfully!');
          await loadBusinessCases();
        } else {
          showNotification(response.error || 'Failed to delete business case', 'error');
        }
      } catch (error) {
        console.error('Error deleting business case:', error);
        showNotification('An error occurred while deleting the business case', 'error');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleExport = async (businessCase) => {
    try {
      const response = await businessCaseService.exportBusinessCase(businessCase.id);
      if (response.success) {
        showNotification('Business case exported successfully!');
      } else {
        showNotification(response.error || 'Failed to export business case', 'error');
      }
    } catch (error) {
      console.error('Error exporting business case:', error);
      showNotification('An error occurred while exporting the business case', 'error');
    }
  };

  const handleBulkAction = async (action, selectedIds) => {
    if (action === 'delete') {
      if (window.confirm(`Are you sure you want to delete ${selectedIds.length} business case(s)?`)) {
        setIsLoading(true);
        try {
          // Delete each selected business case
          for (const id of selectedIds) {
            await businessCaseService.delete(id);
          }
          showNotification(`${selectedIds.length} business case(s) deleted successfully!`);
          await loadBusinessCases();
        } catch (error) {
          console.error('Error deleting business cases:', error);
          showNotification('An error occurred while deleting business cases', 'error');
        } finally {
          setIsLoading(false);
        }
      }
    } else if (action === 'export') {
      try {
        // Export each selected business case
        for (const id of selectedIds) {
          await businessCaseService.exportBusinessCase(id);
        }
        showNotification(`${selectedIds.length} business case(s) exported successfully!`);
      } catch (error) {
        console.error('Error exporting business cases:', error);
        showNotification('An error occurred while exporting business cases', 'error');
      }
    }
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      await loadBusinessCases();
      showNotification('Business cases refreshed successfully!');
    } catch (error) {
      console.error('Error refreshing business cases:', error);
      showNotification('An error occurred while refreshing business cases', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      const response = await businessCaseService.downloadTemplate();
      if (response.success) {
        showNotification('Template downloaded successfully!');
      } else {
        showNotification(response.error || 'Failed to download template', 'error');
      }
    } catch (error) {
      console.error('Error downloading template:', error);
      showNotification('An error occurred while downloading template', 'error');
    }
  };

  const handleExportAll = async () => {
    if (!businessCases || businessCases.length === 0) {
      showNotification('No business cases to export', 'info');
      return;
    }

    try {
      setIsLoading(true);
      // Export all business cases
      for (const bc of businessCases) {
        await businessCaseService.exportBusinessCase(bc.id);
      }
      showNotification(`All ${businessCases.length} business cases exported successfully!`);
    } catch (error) {
      console.error('Error exporting all business cases:', error);
      showNotification('An error occurred while exporting business cases', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Notification */}
      {notification && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
          notification.type === 'success' ? 'bg-green-500 text-white' :
          notification.type === 'error' ? 'bg-red-500 text-white' :
          'bg-blue-500 text-white'
        }`}>
          <div className="flex items-center">
            <i className={`fas ${
              notification.type === 'success' ? 'fa-check-circle' :
              notification.type === 'error' ? 'fa-exclamation-circle' :
              'fa-info-circle'
            } mr-2`}></i>
            {notification.message}
          </div>
        </div>
      )}

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Business Cases</h2>
          <p className="text-gray-600">View and manage all business cases with advanced filtering</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleDownloadTemplate}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
          >
            <i className="fas fa-download mr-2"></i>Template
          </button>
          <button
            onClick={handleExportAll}
            className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700"
            disabled={!businessCases || businessCases.length === 0}
          >
            <i className="fas fa-file-excel mr-2"></i>Export All
          </button>
          <button
            onClick={handleRefresh}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            disabled={isLoading}
          >
            <i className={`fas ${isLoading ? 'fa-spinner fa-spin' : 'fa-refresh'} mr-2`}></i>
            Refresh
          </button>
        </div>
      </div>

      {/* Summary Statistics */}
      {businessCases && businessCases.length > 0 && (
        <BusinessCasesSummary
          businessCases={businessCases}
          filteredCount={filteredBusinessCases.length}
        />
      )}

      {/* Advanced Filters */}
      <AdvancedFilters
        onFiltersChange={setFilters}
        businessCases={businessCases || []}
      />

      {/* Business Cases Table */}
      <BusinessCasesTable
        businessCases={filteredBusinessCases}
        onView={handleView}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onExport={handleExport}
        onBulkAction={handleBulkAction}
        loading={isLoading}
      />

      {/* Detail Modal */}
      <BusinessCaseDetailModal
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        businessCase={selectedBusinessCase}
        onEdit={handleEdit}
        onExport={handleExport}
      />

      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 flex items-center">
            <i className="fas fa-spinner fa-spin text-2xl text-blue-600 mr-4"></i>
            <span className="text-lg">Processing...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default BusinessCasesList;
