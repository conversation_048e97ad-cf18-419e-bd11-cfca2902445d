import React, { useMemo } from 'react';

const FinancialPerformance = ({ dashboardData, masterBusinessCases, businessCases }) => {
  console.log('🔍 FinancialPerformance received data:', { 
    dashboardData: !!dashboardData, 
    masterBusinessCases: masterBusinessCases?.length, 
    businessCases: businessCases?.length 
  });

  // Calculate financial metrics based on real data
  const financialMetrics = useMemo(() => {
    if (!masterBusinessCases || !Array.isArray(masterBusinessCases)) {
      return {
        totalCapex: 0,
        totalOpex: 0,
        totalInvestment: 0,
        totalNPV: 0,
        avgIRR: 0,
        avgPaybackPeriod: 0,
        profitableCount: 0,
        unprofitableCount: 0,
        performanceDistribution: [],
        investmentByCategory: {}
      };
    }

    let totalCapex = 0;
    let totalOpex = 0;
    let totalInvestment = 0;
    let totalNPV = 0;
    let totalIRR = 0;
    let totalPayback = 0;
    let irrCount = 0;
    let paybackCount = 0;
    let profitableCount = 0;
    let unprofitableCount = 0;
    const investmentByCategory = {};
    const performanceDistribution = [];

    masterBusinessCases.forEach(mbc => {
      if (mbc.aggregatedMetrics) {
        const metrics = mbc.aggregatedMetrics;
        
        totalCapex += metrics.totalCapex || 0;
        totalOpex += metrics.totalOpex || 0;
        totalInvestment += metrics.totalInvestment || 0;
        totalNPV += metrics.totalNPV || 0;

        if (metrics.avgIRR) {
          totalIRR += metrics.avgIRR;
          irrCount++;
        }

        if (metrics.avgPaybackPeriod) {
          totalPayback += metrics.avgPaybackPeriod;
          paybackCount++;
        }

        // Profitability analysis
        if (metrics.totalNPV > 0) {
          profitableCount++;
        } else {
          unprofitableCount++;
        }

        // Investment by category
        const category = mbc.category || 'Uncategorized';
        investmentByCategory[category] = (investmentByCategory[category] || 0) + (metrics.totalInvestment || 0);

        // Performance distribution
        performanceDistribution.push({
          name: mbc.name,
          npv: metrics.totalNPV || 0,
          irr: metrics.avgIRR || 0,
          investment: metrics.totalInvestment || 0,
          category: category
        });
      }
    });

    return {
      totalCapex,
      totalOpex,
      totalInvestment,
      totalNPV,
      avgIRR: irrCount > 0 ? totalIRR / irrCount : 0,
      avgPaybackPeriod: paybackCount > 0 ? totalPayback / paybackCount : 0,
      profitableCount,
      unprofitableCount,
      performanceDistribution: performanceDistribution.sort((a, b) => b.npv - a.npv),
      investmentByCategory
    };
  }, [masterBusinessCases]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${Number(value).toFixed(1)}%`;
  };

  const formatYears = (value) => {
    return `${Number(value).toFixed(1)} years`;
  };

  return (
    <div className="financial-performance">
      {/* Financial Summary */}
      <div className="financial-summary">
        <h3 className="section-title">Financial Summary</h3>
        <div className="summary-grid">
          <div className="summary-card capex">
            <div className="summary-header">
              <h4>Total CAPEX</h4>
              <i className="fas fa-building"></i>
            </div>
            <p className="summary-value">{formatCurrency(financialMetrics.totalCapex)}</p>
            <span className="summary-detail">Capital expenditure</span>
          </div>

          <div className="summary-card opex">
            <div className="summary-header">
              <h4>Total OPEX</h4>
              <i className="fas fa-cogs"></i>
            </div>
            <p className="summary-value">{formatCurrency(financialMetrics.totalOpex)}</p>
            <span className="summary-detail">Operational expenditure</span>
          </div>

          <div className="summary-card investment">
            <div className="summary-header">
              <h4>Total Investment</h4>
              <i className="fas fa-dollar-sign"></i>
            </div>
            <p className="summary-value">{formatCurrency(financialMetrics.totalInvestment)}</p>
            <span className="summary-detail">CAPEX + OPEX</span>
          </div>

          <div className="summary-card npv">
            <div className="summary-header">
              <h4>Total NPV</h4>
              <i className="fas fa-trending-up"></i>
            </div>
            <p className="summary-value">{formatCurrency(financialMetrics.totalNPV)}</p>
            <span className="summary-detail">Net present value</span>
          </div>

          <div className="summary-card irr">
            <div className="summary-header">
              <h4>Average IRR</h4>
              <i className="fas fa-chart-line"></i>
            </div>
            <p className="summary-value">{formatPercentage(financialMetrics.avgIRR)}</p>
            <span className="summary-detail">Internal rate of return</span>
          </div>

          <div className="summary-card payback">
            <div className="summary-header">
              <h4>Avg Payback Period</h4>
              <i className="fas fa-clock"></i>
            </div>
            <p className="summary-value">{formatYears(financialMetrics.avgPaybackPeriod)}</p>
            <span className="summary-detail">Time to break even</span>
          </div>
        </div>
      </div>

      {/* Profitability Analysis */}
      <div className="profitability-section">
        <h3 className="section-title">Profitability Analysis</h3>
        <div className="profitability-grid">
          <div className="profitability-card profitable">
            <div className="profitability-icon">
              <i className="fas fa-arrow-up"></i>
            </div>
            <div className="profitability-content">
              <h4>Profitable Projects</h4>
              <p className="profitability-count">{financialMetrics.profitableCount}</p>
              <span className="profitability-percentage">
                {((financialMetrics.profitableCount / (financialMetrics.profitableCount + financialMetrics.unprofitableCount)) * 100).toFixed(1)}% of portfolio
              </span>
            </div>
          </div>

          <div className="profitability-card unprofitable">
            <div className="profitability-icon">
              <i className="fas fa-arrow-down"></i>
            </div>
            <div className="profitability-content">
              <h4>Unprofitable Projects</h4>
              <p className="profitability-count">{financialMetrics.unprofitableCount}</p>
              <span className="profitability-percentage">
                {((financialMetrics.unprofitableCount / (financialMetrics.profitableCount + financialMetrics.unprofitableCount)) * 100).toFixed(1)}% of portfolio
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Investment by Category */}
      <div className="investment-category-section">
        <h3 className="section-title">Investment by Category</h3>
        <div className="category-investment-grid">
          {Object.entries(financialMetrics.investmentByCategory)
            .sort(([,a], [,b]) => b - a)
            .map(([category, investment]) => (
            <div key={category} className="category-investment-card">
              <h4>{category}</h4>
              <p className="investment-amount">{formatCurrency(investment)}</p>
              <div className="investment-bar">
                <div 
                  className="investment-fill"
                  style={{ 
                    width: `${(investment / financialMetrics.totalInvestment) * 100}%` 
                  }}
                ></div>
              </div>
              <span className="investment-percentage">
                {((investment / financialMetrics.totalInvestment) * 100).toFixed(1)}% of total
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Performance Distribution */}
      <div className="performance-section">
        <h3 className="section-title">Performance Distribution</h3>
        <div className="performance-list">
          {financialMetrics.performanceDistribution.slice(0, 10).map((item, index) => (
            <div key={index} className="performance-item">
              <div className="performance-rank">#{index + 1}</div>
              <div className="performance-content">
                <h4>{item.name}</h4>
                <div className="performance-metrics">
                  <span className="metric npv-metric">
                    NPV: {formatCurrency(item.npv)}
                  </span>
                  <span className="metric irr-metric">
                    IRR: {formatPercentage(item.irr)}
                  </span>
                  <span className="metric investment-metric">
                    Investment: {formatCurrency(item.investment)}
                  </span>
                  <span className="metric category-metric">
                    {item.category}
                  </span>
                </div>
              </div>
              <div className="performance-indicator">
                <div className={`npv-indicator ${item.npv > 0 ? 'positive' : 'negative'}`}>
                  <i className={`fas ${item.npv > 0 ? 'fa-arrow-up' : 'fa-arrow-down'}`}></i>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <style jsx>{`
        .financial-performance {
          padding: 24px;
        }

        .section-title {
          font-size: 20px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 20px 0;
        }

        .financial-summary {
          margin-bottom: 32px;
        }

        .summary-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;
        }

        .summary-card {
          padding: 20px;
          background: white;
          border-radius: 12px;
          border: 1px solid #e2e8f0;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .summary-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
        }

        .summary-header h4 {
          font-size: 14px;
          font-weight: 500;
          color: #64748b;
          margin: 0;
        }

        .summary-header i {
          color: #3b82f6;
          font-size: 18px;
        }

        .summary-value {
          font-size: 20px;
          font-weight: 700;
          color: #1e293b;
          margin: 0 0 4px 0;
        }

        .summary-detail {
          font-size: 12px;
          color: #94a3b8;
        }

        .profitability-section,
        .investment-category-section,
        .performance-section {
          margin-bottom: 32px;
        }

        .profitability-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;
        }

        .profitability-card {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 20px;
          background: white;
          border-radius: 12px;
          border: 1px solid #e2e8f0;
        }

        .profitability-card.profitable {
          border-left: 4px solid #10b981;
        }

        .profitability-card.unprofitable {
          border-left: 4px solid #ef4444;
        }

        .profitability-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
        }

        .profitable .profitability-icon {
          background: #dcfce7;
          color: #166534;
        }

        .unprofitable .profitability-icon {
          background: #fee2e2;
          color: #dc2626;
        }

        .profitability-content h4 {
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 8px 0;
        }

        .profitability-count {
          font-size: 24px;
          font-weight: 700;
          color: #1e293b;
          margin: 0 0 4px 0;
        }

        .profitability-percentage {
          font-size: 12px;
          color: #64748b;
        }

        .category-investment-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;
        }

        .category-investment-card {
          padding: 16px;
          background: white;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
        }

        .category-investment-card h4 {
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 8px 0;
        }

        .investment-amount {
          font-size: 18px;
          font-weight: 700;
          color: #3b82f6;
          margin: 0 0 12px 0;
        }

        .investment-bar {
          height: 6px;
          background: #e2e8f0;
          border-radius: 3px;
          overflow: hidden;
          margin-bottom: 8px;
        }

        .investment-fill {
          height: 100%;
          background: #3b82f6;
          transition: width 0.3s ease;
        }

        .investment-percentage {
          font-size: 12px;
          color: #64748b;
        }

        .performance-list {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .performance-item {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 16px;
          background: white;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
        }

        .performance-rank {
          width: 32px;
          height: 32px;
          background: #3b82f6;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 14px;
          flex-shrink: 0;
        }

        .performance-content {
          flex: 1;
        }

        .performance-content h4 {
          font-size: 14px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 8px 0;
        }

        .performance-metrics {
          display: flex;
          gap: 12px;
          flex-wrap: wrap;
        }

        .metric {
          font-size: 11px;
          padding: 3px 8px;
          border-radius: 12px;
          font-weight: 500;
        }

        .npv-metric {
          background: #dbeafe;
          color: #1e40af;
        }

        .irr-metric {
          background: #dcfce7;
          color: #166534;
        }

        .investment-metric {
          background: #fef3c7;
          color: #92400e;
        }

        .category-metric {
          background: #f3e8ff;
          color: #7c3aed;
        }

        .performance-indicator {
          flex-shrink: 0;
        }

        .npv-indicator {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
        }

        .npv-indicator.positive {
          background: #dcfce7;
          color: #166534;
        }

        .npv-indicator.negative {
          background: #fee2e2;
          color: #dc2626;
        }

        @media (max-width: 768px) {
          .financial-performance {
            padding: 16px;
          }

          .summary-grid {
            grid-template-columns: 1fr;
          }

          .profitability-grid {
            grid-template-columns: 1fr;
          }

          .performance-item {
            flex-direction: column;
            text-align: center;
          }

          .performance-metrics {
            justify-content: center;
          }
        }
      `}</style>
    </div>
  );
};

export default FinancialPerformance;
