import React from 'react';

const BusinessCaseEmptyState = ({ onCreateNew, onPromoteIdea, onImportExcel }) => {
  return (
    <div className="text-center py-12">
      <div className="max-w-4xl mx-auto">
        {/* Main Icon */}
        <div className="mb-8">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-6">
            <i className="fas fa-briefcase text-3xl text-white"></i>
          </div>
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Welcome to Business Case Creation
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Transform your ideas into comprehensive business cases with financial analysis and strategic planning
          </p>
        </div>

        {/* Action Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* Create New Business Case */}
          <div className="bg-white border-2 border-dashed border-blue-300 rounded-xl p-6 hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 cursor-pointer group"
               onClick={onCreateNew}>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4 group-hover:bg-blue-200 transition-colors">
                <i className="fas fa-plus text-2xl text-blue-600"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Create New Business Case</h3>
              <p className="text-sm text-gray-600 mb-4">
                Start from scratch with our comprehensive business case template
              </p>
              <div className="text-xs text-blue-600 font-medium">
                <i className="fas fa-arrow-right mr-1"></i>
                Get Started
              </div>
            </div>
          </div>

          {/* Promote from Ideas */}
          <div className="bg-white border-2 border-dashed border-green-300 rounded-xl p-6 hover:border-green-500 hover:bg-green-50 transition-all duration-200 cursor-pointer group"
               onClick={onPromoteIdea}>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4 group-hover:bg-green-200 transition-colors">
                <i className="fas fa-lightbulb text-2xl text-green-600"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Promote from Ideas</h3>
              <p className="text-sm text-gray-600 mb-4">
                Convert approved ideas into detailed business cases
              </p>
              <div className="text-xs text-green-600 font-medium">
                <i className="fas fa-arrow-right mr-1"></i>
                Browse Ideas
              </div>
            </div>
          </div>

          {/* Import from Excel */}
          <div className="bg-white border-2 border-dashed border-orange-300 rounded-xl p-6 hover:border-orange-500 hover:bg-orange-50 transition-all duration-200 cursor-pointer group"
               onClick={onImportExcel}>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-100 rounded-full mb-4 group-hover:bg-orange-200 transition-colors">
                <i className="fas fa-file-excel text-2xl text-orange-600"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Import from Excel</h3>
              <p className="text-sm text-gray-600 mb-4">
                Upload existing business case data from Excel templates
              </p>
              <div className="text-xs text-orange-600 font-medium">
                <i className="fas fa-arrow-right mr-1"></i>
                Upload File
              </div>
            </div>
          </div>
        </div>

        {/* Features Overview */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8 border border-blue-200">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">
            <i className="fas fa-star text-yellow-500 mr-2"></i>
            Business Case Features
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-3">
                <i className="fas fa-calculator text-blue-600"></i>
              </div>
              <h4 className="font-medium text-gray-900 mb-1">Financial Analysis</h4>
              <p className="text-sm text-gray-600">IRR, NPV, Payback Period calculations</p>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-3">
                <i className="fas fa-chart-line text-green-600"></i>
              </div>
              <h4 className="font-medium text-gray-900 mb-1">Revenue Modeling</h4>
              <p className="text-sm text-gray-600">Multi-year revenue projections</p>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-3">
                <i className="fas fa-link text-purple-600"></i>
              </div>
              <h4 className="font-medium text-gray-900 mb-1">Project Linking</h4>
              <p className="text-sm text-gray-600">Connect to projects and epics</p>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-orange-100 rounded-full mb-3">
                <i className="fas fa-file-export text-orange-600"></i>
              </div>
              <h4 className="font-medium text-gray-900 mb-1">Export Options</h4>
              <p className="text-sm text-gray-600">Excel, PDF, and presentation formats</p>
            </div>
          </div>
        </div>

        {/* Quick Tips */}
        <div className="mt-8 text-left">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h4 className="font-semibold text-gray-900 mb-4">
              <i className="fas fa-info-circle text-blue-500 mr-2"></i>
              Quick Tips for Business Case Creation
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-start">
                  <i className="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                  <span className="text-sm text-gray-700">Start with clear problem statement and objectives</span>
                </div>
                <div className="flex items-start">
                  <i className="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                  <span className="text-sm text-gray-700">Include detailed financial projections and assumptions</span>
                </div>
                <div className="flex items-start">
                  <i className="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                  <span className="text-sm text-gray-700">Consider risks and mitigation strategies</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-start">
                  <i className="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                  <span className="text-sm text-gray-700">Define success metrics and KPIs</span>
                </div>
                <div className="flex items-start">
                  <i className="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                  <span className="text-sm text-gray-700">Align with strategic business objectives</span>
                </div>
                <div className="flex items-start">
                  <i className="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                  <span className="text-sm text-gray-700">Include implementation timeline and resources</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessCaseEmptyState;
