import React, { useState, useEffect } from 'react';
import { useData } from '../../../context/DataContext';
import { useAuth } from '../../../context/AuthContext';
import RequestForm from './RequestForm';
import RequestList from './RequestList';
import RequestStats from './RequestStats';
import ProcessFlow from './ProcessFlow/ProcessFlow';
import './RequestManagement.css';

const RequestManagement = () => {
  const { user } = useAuth();
  const [activeView, setActiveView] = useState('list'); // 'list', 'create', 'edit', 'stats', 'process'
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    requestType: '',
    search: ''
  });
  const [notification, setNotification] = useState(null);

  // Load requests on component mount
  useEffect(() => {
    loadRequests();
  }, []);

  const loadRequests = async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call
      const mockRequests = [
        {
          id: 'req_001',
          requestId: 'REQ-2024-001',
          dateSubmitted: '2024-01-15T10:30:00Z',
          submittedBy: 'John Doe',
          submitterId: '<EMAIL>',
          title: 'Enhanced Customer Analytics Dashboard',
          description: 'Request for advanced analytics capabilities in customer dashboard',
          requestType: 'Enhancement',
          category: 'Analytics',
          priority: 'High',
          status: 'In Review',
          affectedApplications: ['Customer Portal', 'Analytics Engine'],
          businessImpact: 'Improved customer insights and decision making',
          customerValue: 'Better user experience and data-driven decisions',
          deadline: '2024-03-15',
          urgencyScore: 4,
          customerValueScore: 5,
          strategicAlignmentScore: 4,
          effortEstimation: 21,
          finalPriorityRank: 'High'
        },
        {
          id: 'req_002',
          requestId: 'REQ-2024-002',
          dateSubmitted: '2024-01-14T14:20:00Z',
          submittedBy: 'Jane Smith',
          submitterId: '<EMAIL>',
          title: 'Mobile App Performance Optimization',
          description: 'Optimize mobile application performance and loading times',
          requestType: 'Bug Fix',
          category: 'Performance',
          priority: 'Medium',
          status: 'Open',
          affectedApplications: ['Mobile App'],
          businessImpact: 'Reduced user churn and improved satisfaction',
          customerValue: 'Faster app performance and better user experience',
          deadline: '2024-02-28',
          urgencyScore: 3,
          customerValueScore: 4,
          strategicAlignmentScore: 3,
          effortEstimation: 13,
          finalPriorityRank: 'Medium'
        }
      ];
      setRequests(mockRequests);
    } catch (error) {
      console.error('Error loading requests:', error);
      showNotification('Failed to load requests', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateRequest = async (requestData) => {
    try {
      setLoading(true);
      
      // Generate new request
      const newRequest = {
        id: `req_${Date.now()}`,
        requestId: `REQ-${new Date().getFullYear()}-${String(requests.length + 1).padStart(3, '0')}`,
        dateSubmitted: new Date().toISOString(),
        submittedBy: user?.name || 'Unknown User',
        submitterId: user?.email || '<EMAIL>',
        ...requestData,
        status: 'Open'
      };

      // TODO: Replace with actual API call
      setRequests(prev => [newRequest, ...prev]);
      setActiveView('list');
      showNotification('Request created successfully!', 'success');
      
      return { success: true, data: newRequest };
    } catch (error) {
      console.error('Error creating request:', error);
      showNotification('Failed to create request', 'error');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateRequest = async (requestId, updateData) => {
    try {
      setLoading(true);
      
      // TODO: Replace with actual API call
      setRequests(prev => prev.map(req => 
        req.id === requestId 
          ? { ...req, ...updateData, updatedAt: new Date().toISOString() }
          : req
      ));
      
      setActiveView('list');
      setSelectedRequest(null);
      showNotification('Request updated successfully!', 'success');
      
      return { success: true };
    } catch (error) {
      console.error('Error updating request:', error);
      showNotification('Failed to update request', 'error');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRequest = async (requestId) => {
    if (!window.confirm('Are you sure you want to delete this request?')) {
      return;
    }

    try {
      setLoading(true);
      
      // TODO: Replace with actual API call
      setRequests(prev => prev.filter(req => req.id !== requestId));
      showNotification('Request deleted successfully!', 'success');
      
      return { success: true };
    } catch (error) {
      console.error('Error deleting request:', error);
      showNotification('Failed to delete request', 'error');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const handleViewChange = (view, request = null) => {
    setActiveView(view);
    setSelectedRequest(request);
  };

  const showNotification = (message, type = 'info') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  };

  // Filter requests based on current filters
  const filteredRequests = requests.filter(request => {
    if (filters.status && request.status !== filters.status) return false;
    if (filters.priority && request.priority !== filters.priority) return false;
    if (filters.requestType && request.requestType !== filters.requestType) return false;
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      return (
        request.title.toLowerCase().includes(searchLower) ||
        request.description.toLowerCase().includes(searchLower) ||
        request.submittedBy.toLowerCase().includes(searchLower) ||
        request.requestId.toLowerCase().includes(searchLower)
      );
    }
    return true;
  });

  return (
    <div className="request-management">
      {/* Header */}
      <div className="request-management-header">
        <div className="header-content">
          <div className="header-info">
            <h2>Request Management</h2>
            <p>Comprehensive request tracking, validation, and change management</p>
          </div>
          <div className="header-actions">
            <button
              className={`view-btn ${activeView === 'process' ? 'active' : ''}`}
              onClick={() => handleViewChange('process')}
            >
              <i className="fas fa-project-diagram"></i>
              Process Flow
            </button>
            <button
              className={`view-btn ${activeView === 'stats' ? 'active' : ''}`}
              onClick={() => handleViewChange('stats')}
            >
              <i className="fas fa-chart-bar"></i>
              Statistics
            </button>
            <button
              className={`view-btn ${activeView === 'list' ? 'active' : ''}`}
              onClick={() => handleViewChange('list')}
            >
              <i className="fas fa-list"></i>
              View All
            </button>
            <button
              className="create-btn"
              onClick={() => handleViewChange('create')}
            >
              <i className="fas fa-plus"></i>
              Create Request
            </button>
          </div>
        </div>
      </div>

      {/* Notification */}
      {notification && (
        <div className={`notification ${notification.type}`}>
          <i className={`fas ${notification.type === 'success' ? 'fa-check-circle' : 
                              notification.type === 'error' ? 'fa-times-circle' : 'fa-info-circle'}`}></i>
          <span>{notification.message}</span>
          <button onClick={() => setNotification(null)}>
            <i className="fas fa-times"></i>
          </button>
        </div>
      )}

      {/* Content */}
      <div className="request-management-content">
        {loading && (
          <div className="loading-overlay">
            <div className="loading-spinner"></div>
            <p>Loading...</p>
          </div>
        )}

        {activeView === 'create' && (
          <RequestForm
            onSubmit={handleCreateRequest}
            onCancel={() => handleViewChange('list')}
            isEditing={false}
          />
        )}

        {activeView === 'edit' && selectedRequest && (
          <RequestForm
            request={selectedRequest}
            onSubmit={(data) => handleUpdateRequest(selectedRequest.id, data)}
            onCancel={() => handleViewChange('list')}
            isEditing={true}
          />
        )}

        {activeView === 'list' && (
          <RequestList
            requests={filteredRequests}
            filters={filters}
            onFiltersChange={setFilters}
            onEditRequest={(request) => handleViewChange('edit', request)}
            onDeleteRequest={handleDeleteRequest}
            onCreateNew={() => handleViewChange('create')}
            loading={loading}
          />
        )}

        {activeView === 'stats' && (
          <RequestStats requests={requests} />
        )}

        {activeView === 'process' && (
          <ProcessFlow />
        )}
      </div>
    </div>
  );
};

export default RequestManagement;
