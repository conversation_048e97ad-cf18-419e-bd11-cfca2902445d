import { apiGet, apiPost, apiPut, apiDelete } from './api';

class ProjectService {
  // Projects
  async getAll() {
    return await apiGet('/api/projects');
  }

  async getById(id) {
    return await apiGet(`/api/projects/${id}`);
  }

  async create(projectData) {
    return await apiPost('/api/projects', projectData);
  }

  async update(id, projectData) {
    return await apiPut(`/api/projects/${id}`, projectData);
  }

  async delete(id) {
    return await apiDelete(`/api/projects/${id}`);
  }

  // Convenience methods for better naming
  async createProject(projectData) {
    return await this.create(projectData);
  }

  async updateProject(id, projectData) {
    return await this.update(id, projectData);
  }

  async deleteProject(id) {
    return await this.delete(id);
  }

  // Epics
  async getEpics() {
    return await apiGet('/api/epics');
  }

  async getEpicById(id) {
    return await apiGet(`/api/epics/${id}`);
  }

  async createEpic(epicData) {
    return await apiPost('/api/epics', epicData);
  }

  async updateEpic(id, epicData) {
    return await apiPut(`/api/epics/${id}`, epicData);
  }

  async deleteEpic(id) {
    return await apiDelete(`/api/epics/${id}`);
  }

  // Programs
  async getPrograms() {
    return await apiGet('/api/programs');
  }

  async getProgramById(id) {
    return await apiGet(`/api/programs/${id}`);
  }

  async createProgram(programData) {
    return await apiPost('/api/programs', programData);
  }

  async updateProgram(id, programData) {
    return await apiPut(`/api/programs/${id}`, programData);
  }

  async deleteProgram(id) {
    return await apiDelete(`/api/programs/${id}`);
  }

  // Link Program to Master Business Case
  async linkProgramToMasterBC(programId, masterBCId) {
    return await apiPost(`/api/programs/${programId}/link-master-bc`, { masterBCId });
  }

  // Milestones
  async getMilestones(projectId) {
    return await apiGet(`/api/projects/${projectId}/milestones`);
  }

  async createMilestone(projectId, milestoneData) {
    return await apiPost(`/api/projects/${projectId}/milestones`, milestoneData);
  }

  async updateMilestone(milestoneId, milestoneData) {
    return await apiPut(`/api/milestones/${milestoneId}`, milestoneData);
  }

  async deleteMilestone(milestoneId) {
    return await apiDelete(`/milestones/${milestoneId}`);
  }
}

export const projectService = new ProjectService();
export default projectService;
