import React, { useState } from 'react';

const IdeaEmptyState = ({ onCreateFirst }) => {
  const [currentTip, setCurrentTip] = useState(0);

  const ideaTips = [
    {
      icon: 'fas fa-lightbulb',
      title: 'Start with a Problem',
      description: 'Great ideas solve real problems. Think about challenges you face daily.',
      color: 'text-yellow-500'
    },
    {
      icon: 'fas fa-users',
      title: 'Consider Your Audience',
      description: 'Who will benefit from your idea? Understanding your audience is key.',
      color: 'text-blue-500'
    },
    {
      icon: 'fas fa-chart-line',
      title: 'Think Impact',
      description: 'How will your idea improve efficiency, reduce costs, or increase revenue?',
      color: 'text-green-500'
    },
    {
      icon: 'fas fa-cogs',
      title: 'Keep it Simple',
      description: 'The best ideas are often the simplest. Focus on clear, actionable solutions.',
      color: 'text-purple-500'
    }
  ];

  const nextTip = () => {
    setCurrentTip((prev) => (prev + 1) % ideaTips.length);
  };

  const prevTip = () => {
    setCurrentTip((prev) => (prev - 1 + ideaTips.length) % ideaTips.length);
  };

  return (
    <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl border border-blue-200 overflow-hidden">
      <div className="p-8 lg:p-12">
        <div className="max-w-4xl mx-auto">
          {/* Main Hero Section */}
          <div className="text-center mb-12">
            <div className="relative inline-block mb-6">
              <div className="absolute inset-0 bg-yellow-400 rounded-full animate-pulse opacity-20"></div>
              <i className="fas fa-lightbulb text-6xl text-yellow-500 relative z-10"></i>
            </div>
            
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Your Innovation Journey Starts Here
            </h2>
            
            <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              Transform your brilliant ideas into actionable business opportunities. 
              Every great innovation begins with a single idea – what's yours?
            </p>

            <button
              onClick={onCreateFirst}
              className="bg-gradient-to-r from-green-500 to-green-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:from-green-600 hover:to-green-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <i className="fas fa-plus mr-3"></i>
              Submit Your First Idea
            </button>
          </div>

          {/* Interactive Tips Section */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                <i className="fas fa-graduation-cap mr-2 text-indigo-500"></i>
                Idea Creation Tips
              </h3>
              <div className="flex space-x-2">
                <button
                  onClick={prevTip}
                  className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                >
                  <i className="fas fa-chevron-left text-gray-600"></i>
                </button>
                <button
                  onClick={nextTip}
                  className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                >
                  <i className="fas fa-chevron-right text-gray-600"></i>
                </button>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className={`text-3xl ${ideaTips[currentTip].color}`}>
                <i className={ideaTips[currentTip].icon}></i>
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900 mb-1">
                  {ideaTips[currentTip].title}
                </h4>
                <p className="text-gray-600">
                  {ideaTips[currentTip].description}
                </p>
              </div>
            </div>

            {/* Tip indicators */}
            <div className="flex justify-center mt-4 space-x-2">
              {ideaTips.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTip(index)}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    index === currentTip ? 'bg-indigo-500' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Process Steps */}
          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <div className="text-center p-6 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-edit text-blue-600 text-xl"></i>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">1. Submit</h4>
              <p className="text-gray-600 text-sm">
                Share your idea with a clear problem statement and proposed solution
              </p>
            </div>

            <div className="text-center p-6 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-search text-yellow-600 text-xl"></i>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">2. Review</h4>
              <p className="text-gray-600 text-sm">
                Your idea will be evaluated by our innovation team for feasibility
              </p>
            </div>

            <div className="text-center p-6 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-rocket text-green-600 text-xl"></i>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">3. Implement</h4>
              <p className="text-gray-600 text-sm">
                Approved ideas become business cases and move to implementation
              </p>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg p-6 text-white">
            <h3 className="text-xl font-semibold mb-2">Ready to Make an Impact?</h3>
            <p className="mb-4 opacity-90">
              Join innovators who are shaping the future of our organization
            </p>
            <button
              onClick={onCreateFirst}
              className="bg-white text-indigo-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Get Started Now
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IdeaEmptyState;
