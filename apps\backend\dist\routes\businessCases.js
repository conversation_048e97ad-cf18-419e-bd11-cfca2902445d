"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const businessCaseController_1 = require("../controllers/businessCaseController");
const auth_1 = require("../middleware/auth");
const types_1 = require("../../../shared/src/types");
const router = express_1.default.Router();
router.use(auth_1.authMiddleware);
router.get('/stats', businessCaseController_1.getBusinessCaseStats);
router.get('/', businessCaseController_1.getBusinessCases);
router.get('/:id', businessCaseController_1.getBusinessCase);
router.post('/', (0, auth_1.requireRole)([types_1.UserRole.FINANCIAL_ANALYST, types_1.UserRole.ADMIN]), businessCaseController_1.createBusinessCase);
router.put('/:id', (0, auth_1.requireRole)([types_1.UserRole.FINANCIAL_ANALYST, types_1.UserRole.ADMIN]), businessCaseController_1.updateBusinessCase);
router.delete('/:id', (0, auth_1.requireRole)([types_1.UserRole.ADMIN]), businessCaseController_1.deleteBusinessCase);
router.get('/:id/export', businessCaseController_1.exportBusinessCase);
exports.default = router;
//# sourceMappingURL=businessCases.js.map