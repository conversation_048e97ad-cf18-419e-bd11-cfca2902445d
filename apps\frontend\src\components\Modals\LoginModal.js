import React, { useState } from 'react';
import PropTypes from 'prop-types';
import Modal from './Modal';

const LoginModal = ({ isOpen, onClose, onLogin }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const result = await onLogin(formData);
      if (result.success) {
        // Login successful, modal will be closed by parent component
        setFormData({ email: '', password: '' });
      } else {
        setError(result.error || 'Login failed');
      }
    } catch (err) {
      setError(err.message || 'Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({ email: '', password: '' });
    setError('');
    onClose();
  };

  const fillDemoCredentials = (type) => {
    switch (type) {
      case 'test':
        setFormData({ email: 'test', password: 'test' });
        break;
      case 'admin':
        setFormData({ email: 'admin', password: 'admin' });
        break;
      case 'gyanesh':
        setFormData({ email: 'gyanesh', password: 'gyanesh123' });
        break;
      case 'full-admin':
        setFormData({ email: '<EMAIL>', password: 'password123' });
        break;
      default:
        break;
    }
    setError('');
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Login">
      <form onSubmit={handleSubmit} autoComplete="off">
        {/* Hidden fields to break browser autocomplete */}
        <input type="text" style={{ display: 'none' }} autoComplete="off" />
        <input type="password" style={{ display: 'none' }} autoComplete="off" />

        {/* Error Message */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            <i className="fas fa-exclamation-triangle mr-2"></i>
            {error}
          </div>
        )}

        {/* Email/Username Field */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email/Username
          </label>
          <input
            type="text"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            autoComplete="new-password"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            required
            disabled={isLoading}
          />
        </div>

        {/* Password Field */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Password
          </label>
          <input
            type="password"
            name="password"
            value={formData.password}
            onChange={handleInputChange}
            autoComplete="new-password"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            required
            disabled={isLoading}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 mb-6">
          <button
            type="button"
            onClick={handleClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="loading-spinner mr-2"></div>
                Logging in...
              </>
            ) : (
              'Login'
            )}
          </button>
        </div>

        {/* Demo Credentials */}
        <div className="border-t border-gray-200 pt-4">
          <p className="font-medium text-gray-900 mb-3">Demo Credentials:</p>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <button
              type="button"
              onClick={() => fillDemoCredentials('test')}
              className="p-2 text-left bg-gray-50 hover:bg-gray-100 rounded border"
              disabled={isLoading}
            >
              <strong>Simple:</strong> test / test
            </button>
            <button
              type="button"
              onClick={() => fillDemoCredentials('admin')}
              className="p-2 text-left bg-gray-50 hover:bg-gray-100 rounded border"
              disabled={isLoading}
            >
              <strong>Admin:</strong> admin / admin
            </button>
            <button
              type="button"
              onClick={() => fillDemoCredentials('gyanesh')}
              className="p-2 text-left bg-gray-50 hover:bg-gray-100 rounded border"
              disabled={isLoading}
            >
              <strong>Gyanesh:</strong> gyanesh / gyanesh123
            </button>
            <button
              type="button"
              onClick={() => fillDemoCredentials('full-admin')}
              className="p-2 text-left bg-gray-50 hover:bg-gray-100 rounded border"
              disabled={isLoading}
            >
              <strong>Full Admin:</strong> <EMAIL> / password123
            </button>
          </div>
          <p className="mt-2 text-xs text-blue-600">
            💡 Tip: Use "test/test" for quick access
          </p>
        </div>
      </form>
    </Modal>
  );
};

LoginModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onLogin: PropTypes.func.isRequired,
};

export default LoginModal;
