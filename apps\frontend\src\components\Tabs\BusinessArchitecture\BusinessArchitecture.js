import React from 'react';
import './FinancialFormulas.css';

const BusinessArchitecture = () => {
  const handleDownloadFormulas = () => {
    // Create downloadable documentation
    const content = generateFormulaDocumentation();
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'Financial_Formulas_Documentation.txt';
    document.body.appendChild(a);
    URL.revokeObjectURL(url);
  };

  const generateFormulaDocumentation = () => {
    return `FINANCIAL FORMULAS DOCUMENTATION
Strategic Portfolio Dashboard
Generated: ${new Date().toLocaleDateString()}

=== PROFITABILITY METRICS ===

1. NET PRESENT VALUE (NPV)
   Formula: NPV = Σ(CFt / (1 + r)^t) - Initial Investment
   Where: CFt = Cash flow at time t, r = Discount rate, t = Time period

2. INTERNAL RATE OF RETURN (IRR)
   Formula: 0 = Σ(CFt / (1 + IRR)^t) - Initial Investment
   Solved iteratively for IRR

3. PAYBACK PERIOD
   Formula: Payback Period = Initial Investment / Average Annual Cash Flow

4. PROFITABILITY PERCENTAGE
   Formula: Profitability % = (NPV / Total Investment) × 100

=== PORTFOLIO METRICS ===

5. PORTFOLIO NPV
   Formula: Total NPV = Σ(Individual Master BC NPVs)

6. AVERAGE IRR
   Formula: Avg IRR = Σ(Individual IRRs) / Number of Master BCs

7. SUCCESS RATE
   Formula: Success Rate = (Profitable Master BCs / Total Master BCs) × 100

=== RISK SCORING ===

8. RISK SCORE CALCULATION
   Investment Risk: >$5M = 3 points, >$2M = 2 points, else 1 point
   IRR Risk: <10% = 3 points, <15% = 2 points, else 1 point
   Payback Risk: >4 years = 3 points, >2 years = 2 points, else 1 point
   Total Risk Score = Investment Risk + IRR Risk + Payback Risk (Max: 10)

=== MARGIN CALCULATIONS ===

9. GROSS MARGIN
   Formula: Gross Margin = ((Revenue - COGS) / Revenue) × 100

10. COMMERCIAL MARGIN
    Formula: Commercial Margin = ((Revenue - Total Costs) / Revenue) × 100

This documentation covers all formulas used in the Strategic Portfolio Dashboard.`;
  };

  return (
    <div className="financial-formulas">
      {/* Header */}
      <div className="formulas-header">
        <div className="header-content">
          <h2>Financial Formulas Documentation</h2>
          <p>Comprehensive guide to all financial calculations used in the Strategic Portfolio Dashboard</p>
        </div>
        <button onClick={handleDownloadFormulas} className="download-btn">
          <i className="fas fa-download"></i>
          Download Documentation
        </button>
      </div>

      {/* Core Financial Metrics */}
      <div className="formula-categories">
        <div className="formula-category">
          <div className="category-header">
            <h3>Core Financial Metrics</h3>
            <p>Primary profitability and investment return calculations</p>
          </div>
          <div className="category-content">

            <div className="formula-item">
              <div className="formula-name">Net Present Value (NPV)</div>
              <div className="formula-description">
                Calculates the present value of future cash flows minus the initial investment.
                Used to determine if an investment will add value to the portfolio.
              </div>
              <div className="formula-equation">
                NPV = Σ(CFt / (1 + r)^t) - Initial Investment
              </div>
              <div className="formula-variables">
                <h5>Variables:</h5>
                <ul className="variable-list">
                  <li>CFt = Cash flow at time period t</li>
                  <li>r = Discount rate (cost of capital)</li>
                  <li>t = Time period (years)</li>
                  <li>Initial Investment = Total CAPEX + OPEX</li>
                </ul>
              </div>
            </div>

            <div className="formula-item">
              <div className="formula-name">Internal Rate of Return (IRR)</div>
              <div className="formula-description">
                The discount rate that makes NPV equal to zero. Represents the expected annual rate of return.
                Higher IRR indicates better investment performance.
              </div>
              <div className="formula-equation">
                0 = Σ(CFt / (1 + IRR)^t) - Initial Investment
              </div>
              <div className="formula-variables">
                <h5>Variables:</h5>
                <ul className="variable-list">
                  <li>IRR = Internal Rate of Return (solved iteratively)</li>
                  <li>CFt = Cash flow at time period t</li>
                  <li>t = Time period (years)</li>
                  <li>Initial Investment = Total project cost</li>
                </ul>
              </div>
            </div>

            <div className="formula-item">
              <div className="formula-name">Payback Period</div>
              <div className="formula-description">
                Time required to recover the initial investment from cash flows.
                Shorter payback periods indicate faster capital recovery.
              </div>
              <div className="formula-equation">
                Payback Period = Initial Investment / Average Annual Cash Flow
              </div>
              <div className="formula-variables">
                <h5>Variables:</h5>
                <ul className="variable-list">
                  <li>Initial Investment = Total CAPEX + OPEX</li>
                  <li>Average Annual Cash Flow = Total Cash Flows / Number of Years</li>
                </ul>
              </div>
            </div>

          </div>
        </div>

        <div className="formula-category">
          <div className="category-header">
            <h3>Portfolio Analysis Metrics</h3>
            <p>Calculations for portfolio-level performance assessment</p>
          </div>
          <div className="category-content">

            <div className="formula-item">
              <div className="formula-name">Profitability Percentage</div>
              <div className="formula-description">
                Measures the percentage return on investment. Used to categorize Master Business Cases
                into profitability tiers (Excellent, Good, Marginal, Loss).
              </div>
              <div className="formula-equation">
                Profitability % = (NPV / Total Investment) × 100
              </div>
              <div className="formula-variables">
                <h5>Categories:</h5>
                <ul className="variable-list">
                  <li>Excellent: Greater than 20% profitability</li>
                  <li>Good: 10-20% profitability</li>
                  <li>Marginal: 0-10% profitability</li>
                  <li>Loss: Less than 0% profitability</li>
                </ul>
              </div>
            </div>

            <div className="formula-item">
              <div className="formula-name">Portfolio Total NPV</div>
              <div className="formula-description">
                Aggregates NPV across all Master Business Cases to show total portfolio value creation.
              </div>
              <div className="formula-equation">
                Portfolio NPV = Σ(Individual Master BC NPVs)
              </div>
            </div>

            <div className="formula-item">
              <div className="formula-name">Average Portfolio IRR</div>
              <div className="formula-description">
                Calculates the mean IRR across all Master Business Cases to assess overall portfolio performance.
              </div>
              <div className="formula-equation">
                Average IRR = Σ(Individual IRRs) / Number of Master BCs
              </div>
            </div>

            <div className="formula-item">
              <div className="formula-name">Portfolio Success Rate</div>
              <div className="formula-description">
                Percentage of Master Business Cases that are profitable (positive NPV).
              </div>
              <div className="formula-equation">
                Success Rate = (Profitable Master BCs / Total Master BCs) × 100
              </div>
            </div>

          </div>
        </div>

        <div className="formula-category">
          <div className="category-header">
            <h3>Risk Assessment Formulas</h3>
            <p>Risk scoring and evaluation calculations</p>
          </div>
          <div className="category-content">

            <div className="formula-item">
              <div className="formula-name">Master BC Risk Score</div>
              <div className="formula-description">
                Composite risk score based on investment size, IRR performance, and payback period.
                Scale: 1-10 (higher = more risky).
              </div>
              <div className="formula-equation">
                Risk Score = Investment Risk + IRR Risk + Payback Risk (Max: 10)
              </div>
              <div className="formula-variables">
                <h5>Risk Components:</h5>
                <ul className="variable-list">
                  <li>Investment Risk: Greater than $5M = 3 pts, Greater than $2M = 2 pts, Less than or equal to $2M = 1 pt</li>
                  <li>IRR Risk: Less than 10% = 3 pts, Less than 15% = 2 pts, Greater than or equal to 15% = 1 pt</li>
                  <li>Payback Risk: Greater than 4 years = 3 pts, Greater than 2 years = 2 pts, Less than or equal to 2 years = 1 pt</li>
                </ul>
              </div>
            </div>

            <div className="formula-item">
              <div className="formula-name">Risk Distribution Metrics</div>
              <div className="formula-description">
                Categorizes portfolio risk distribution and calculates average risk exposure.
              </div>
              <div className="formula-equation">
                High Risk Count = Count(Risk Score ≥ 7)
                <br/>
                Medium Risk Count = Count(4 ≤ Risk Score less than 7)
                <br/>
                Low Risk Count = Count(Risk Score less than 4)
                <br/>
                Avg Risk Score = Σ(Risk Scores) / Total Count
              </div>
              <div className="formula-variables">
                <h5>Risk Categories:</h5>
                <ul className="variable-list">
                  <li>High Risk: Score 7-10 (Requires immediate attention)</li>
                  <li>Medium Risk: Score 4-6 (Monitor closely)</li>
                  <li>Low Risk: Score 1-3 (Acceptable risk level)</li>
                </ul>
              </div>
            </div>

          </div>
        </div>

        <div className="formula-category">
          <div className="category-header">
            <h3>Margin & Profitability Ratios</h3>
            <p>Revenue and cost analysis calculations</p>
          </div>
          <div className="category-content">

            <div className="formula-item">
              <div className="formula-name">Gross Margin</div>
              <div className="formula-description">
                Percentage of revenue remaining after deducting cost of goods sold (COGS).
                Indicates operational efficiency.
              </div>
              <div className="formula-equation">
                Gross Margin % = ((Revenue - COGS) / Revenue) × 100
              </div>
              <div className="formula-variables">
                <h5>Variables:</h5>
                <ul className="variable-list">
                  <li>Revenue = Total sales revenue</li>
                  <li>COGS = Cost of Goods Sold (direct production costs)</li>
                </ul>
              </div>
            </div>

            <div className="formula-item">
              <div className="formula-name">Commercial Margin</div>
              <div className="formula-description">
                Percentage of revenue remaining after all costs (including overhead, marketing, admin).
                Shows overall business profitability.
              </div>
              <div className="formula-equation">
                Commercial Margin % = ((Revenue - Total Costs) / Revenue) × 100
              </div>
              <div className="formula-variables">
                <h5>Variables:</h5>
                <ul className="variable-list">
                  <li>Revenue = Total sales revenue</li>
                  <li>Total Costs = COGS + Operating Expenses + Overhead</li>
                </ul>
              </div>
            </div>

          </div>
        </div>
      </div>

      {/* Implementation Examples */}
      <div className="implementation-section">
        <div className="implementation-header">
          <h3>JavaScript Implementation Examples</h3>
          <p>Code examples showing how these formulas are implemented in the application</p>
        </div>
        <div className="implementation-content">

          <h4>NPV Calculation Function</h4>
          <div className="code-block">
            <span className="code-comment">// Calculate Net Present Value</span><br/>
            <span className="code-keyword">const</span> calculateNPV = (cashFlows, discountRate, initialInvestment) => {`{`}<br/>
            &nbsp;&nbsp;<span className="code-keyword">let</span> npv = <span className="code-number">0</span>;<br/>
            &nbsp;&nbsp;<span className="code-keyword">for</span> (<span className="code-keyword">let</span> t = <span className="code-number">0</span>; t &lt; cashFlows.length; t++) {`{`}<br/>
            &nbsp;&nbsp;&nbsp;&nbsp;npv += cashFlows[t] / Math.pow(<span className="code-number">1</span> + discountRate, t + <span className="code-number">1</span>);<br/>
            &nbsp;&nbsp;{`}`}<br/>
            &nbsp;&nbsp;<span className="code-keyword">return</span> npv - initialInvestment;<br/>
            {`}`};
          </div>

          <h4>Profitability Percentage Function</h4>
          <div className="code-block">
            <span className="code-comment">// Calculate profitability percentage used in Strategic Portfolio</span><br/>
            <span className="code-keyword">const</span> calculateProfitability = (metrics) => {`{`}<br/>
            &nbsp;&nbsp;<span className="code-keyword">if</span> (!metrics || !metrics.totalNPV || !metrics.totalInvestment) <span className="code-keyword">return</span> <span className="code-number">0</span>;<br/>
            &nbsp;&nbsp;<span className="code-keyword">return</span> (metrics.totalNPV / metrics.totalInvestment) * <span className="code-number">100</span>;<br/>
            {`}`};
          </div>

          <h4>Risk Score Calculation Function</h4>
          <div className="code-block">
            <span className="code-comment">// Calculate risk score for Master Business Cases</span><br/>
            <span className="code-keyword">const</span> calculateRiskScore = (mbc) => {`{`}<br/>
            &nbsp;&nbsp;<span className="code-keyword">const</span> investment = mbc.aggregatedMetrics?.totalInvestment || <span className="code-number">0</span>;<br/>
            &nbsp;&nbsp;<span className="code-keyword">const</span> irr = mbc.aggregatedMetrics?.avgIRR || <span className="code-number">0</span>;<br/>
            &nbsp;&nbsp;<span className="code-keyword">const</span> payback = mbc.aggregatedMetrics?.avgPaybackPeriod || <span className="code-number">0</span>;<br/>
            &nbsp;&nbsp;<br/>
            &nbsp;&nbsp;<span className="code-keyword">let</span> riskScore = <span className="code-number">0</span>;<br/>
            &nbsp;&nbsp;<br/>
            &nbsp;&nbsp;<span className="code-comment">// Investment size risk</span><br/>
            &nbsp;&nbsp;<span className="code-keyword">if</span> (investment &gt; <span className="code-number">5000000</span>) riskScore += <span className="code-number">3</span>;<br/>
            &nbsp;&nbsp;<span className="code-keyword">else if</span> (investment &gt; <span className="code-number">2000000</span>) riskScore += <span className="code-number">2</span>;<br/>
            &nbsp;&nbsp;<span className="code-keyword">else</span> riskScore += <span className="code-number">1</span>;<br/>
            &nbsp;&nbsp;<br/>
            &nbsp;&nbsp;<span className="code-comment">// IRR risk</span><br/>
            &nbsp;&nbsp;<span className="code-keyword">if</span> (irr &lt; <span className="code-number">10</span>) riskScore += <span className="code-number">3</span>;<br/>
            &nbsp;&nbsp;<span className="code-keyword">else if</span> (irr &lt; <span className="code-number">15</span>) riskScore += <span className="code-number">2</span>;<br/>
            &nbsp;&nbsp;<span className="code-keyword">else</span> riskScore += <span className="code-number">1</span>;<br/>
            &nbsp;&nbsp;<br/>
            &nbsp;&nbsp;<span className="code-comment">// Payback period risk</span><br/>
            &nbsp;&nbsp;<span className="code-keyword">if</span> (payback &gt; <span className="code-number">4</span>) riskScore += <span className="code-number">3</span>;<br/>
            &nbsp;&nbsp;<span className="code-keyword">else if</span> (payback &gt; <span className="code-number">2</span>) riskScore += <span className="code-number">2</span>;<br/>
            &nbsp;&nbsp;<span className="code-keyword">else</span> riskScore += <span className="code-number">1</span>;<br/>
            &nbsp;&nbsp;<br/>
            &nbsp;&nbsp;<span className="code-keyword">return</span> Math.min(riskScore, <span className="code-number">10</span>); <span className="code-comment">// Cap at 10</span><br/>
            {`}`};
          </div>

          <h4>Portfolio Metrics Aggregation</h4>
          <div className="code-block">
            <span className="code-comment">// Calculate portfolio-level metrics</span><br/>
            <span className="code-keyword">const</span> calculatePortfolioMetrics = (masterBCs) => {`{`}<br/>
            &nbsp;&nbsp;<span className="code-keyword">const</span> totalInvestment = masterBCs.reduce((sum, mbc) => <br/>
            &nbsp;&nbsp;&nbsp;&nbsp;sum + (mbc.aggregatedMetrics?.totalInvestment || <span className="code-number">0</span>), <span className="code-number">0</span>);<br/>
            &nbsp;&nbsp;<br/>
            &nbsp;&nbsp;<span className="code-keyword">const</span> totalNPV = masterBCs.reduce((sum, mbc) => <br/>
            &nbsp;&nbsp;&nbsp;&nbsp;sum + (mbc.aggregatedMetrics?.totalNPV || <span className="code-number">0</span>), <span className="code-number">0</span>);<br/>
            &nbsp;&nbsp;<br/>
            &nbsp;&nbsp;<span className="code-keyword">const</span> avgIRR = masterBCs.reduce((sum, mbc) => <br/>
            &nbsp;&nbsp;&nbsp;&nbsp;sum + (mbc.aggregatedMetrics?.avgIRR || <span className="code-number">0</span>), <span className="code-number">0</span>) / masterBCs.length || <span className="code-number">0</span>;<br/>
            &nbsp;&nbsp;<br/>
            &nbsp;&nbsp;<span className="code-keyword">const</span> profitableCount = masterBCs.filter(mbc => <br/>
            &nbsp;&nbsp;&nbsp;&nbsp;(mbc.aggregatedMetrics?.totalNPV || <span className="code-number">0</span>) &gt; <span className="code-number">0</span>).length;<br/>
            &nbsp;&nbsp;<br/>
            &nbsp;&nbsp;<span className="code-keyword">return</span> {`{`}<br/>
            &nbsp;&nbsp;&nbsp;&nbsp;totalInvestment,<br/>
            &nbsp;&nbsp;&nbsp;&nbsp;totalNPV,<br/>
            &nbsp;&nbsp;&nbsp;&nbsp;avgIRR,<br/>
            &nbsp;&nbsp;&nbsp;&nbsp;profitableCount,<br/>
            &nbsp;&nbsp;&nbsp;&nbsp;totalCount: masterBCs.length,<br/>
            &nbsp;&nbsp;&nbsp;&nbsp;successRate: (profitableCount / masterBCs.length) * <span className="code-number">100</span><br/>
            &nbsp;&nbsp;{`}`};<br/>
            {`}`};
          </div>

        </div>
      </div>

      {/* Usage Guidelines */}
      <div className="usage-guidelines">
        <div className="guidelines-header">
          <h3>Formula Usage Guidelines</h3>
          <p>Best practices for applying these formulas in business case analysis</p>
        </div>
        <div className="guidelines-content">

          <div className="guideline-item">
            <div className="guideline-icon">1</div>
            <div className="guideline-content">
              <h4>NPV Interpretation</h4>
              <p>
                Positive NPV indicates value creation. Use consistent discount rates across projects for fair comparison.
                Consider using WACC (Weighted Average Cost of Capital) as the discount rate.
              </p>
            </div>
          </div>

          <div className="guideline-item">
            <div className="guideline-icon">2</div>
            <div className="guideline-content">
              <h4>IRR Benchmarking</h4>
              <p>
                Compare IRR against company's hurdle rate or cost of capital. IRR &gt; 15% is generally considered good,
                but this varies by industry and risk profile.
              </p>
            </div>
          </div>

          <div className="guideline-item">
            <div className="guideline-icon">3</div>
            <div className="guideline-content">
              <h4>Risk Score Application</h4>
              <p>
                Use risk scores for portfolio balancing. Aim for a mix of risk levels. High-risk projects should offer
                correspondingly high returns. Monitor risk distribution regularly.
              </p>
            </div>
          </div>

          <div className="guideline-item">
            <div className="guideline-icon">4</div>
            <div className="guideline-content">
              <h4>Portfolio Optimization</h4>
              <p>
                Target 70%+ success rate for portfolio health. Balance high-return/high-risk with stable/lower-risk investments.
                Use profitability categories to guide resource allocation decisions.
              </p>
            </div>
          </div>

          <div className="guideline-item">
            <div className="guideline-icon">5</div>
            <div className="guideline-content">
              <h4>Data Quality</h4>
              <p>
                Ensure accurate cash flow projections and realistic assumptions. Regularly update forecasts based on actual performance.
                Document all assumptions for transparency and future reference.
              </p>
            </div>
          </div>

          <div className="guideline-item">
            <div className="guideline-icon">6</div>
            <div className="guideline-content">
              <h4>Decision Making</h4>
              <p>
                Use multiple metrics together - don't rely on single indicators. Consider qualitative factors alongside quantitative analysis.
                Review portfolio performance quarterly and adjust strategy as needed.
              </p>
            </div>
          </div>

        </div>
      </div>

    </div>
  );
};

export default BusinessArchitecture;
