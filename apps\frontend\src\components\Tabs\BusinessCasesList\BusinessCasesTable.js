import React, { useState } from 'react';

const BusinessCasesTable = ({ 
  businessCases, 
  onView, 
  onEdit, 
  onDelete, 
  onExport, 
  onBulkAction,
  loading = false 
}) => {
  const [sortConfig, setSortConfig] = useState({ key: 'updatedAt', direction: 'desc' });
  const [selectedItems, setSelectedItems] = useState(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Sort business cases
  const sortedBusinessCases = React.useMemo(() => {
    if (!sortConfig.key) return businessCases;

    return [...businessCases].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [businessCases, sortConfig]);

  // Pagination
  const totalPages = Math.ceil(sortedBusinessCases.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedBusinessCases = sortedBusinessCases.slice(startIndex, startIndex + itemsPerPage);

  const handleSort = (key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedItems(new Set(paginatedBusinessCases.map(bc => bc.id)));
    } else {
      setSelectedItems(new Set());
    }
  };

  const handleSelectItem = (id, checked) => {
    const newSelected = new Set(selectedItems);
    if (checked) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedItems(newSelected);
  };

  const formatCurrency = (amount) => {
    if (!amount) return '$0';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const calculateROI = (bc) => {
    const totalInvestment = (bc.totalCapex || 0) + (bc.totalOpex || 0);
    const totalRevenue = bc.totalRevenue || 0;
    
    if (totalInvestment === 0) return 'N/A';
    
    const roi = ((totalRevenue - totalInvestment) / totalInvestment) * 100;
    return `${roi.toFixed(1)}%`;
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      draft: { color: 'bg-gray-100 text-gray-800', icon: 'fas fa-edit' },
      'under-review': { color: 'bg-yellow-100 text-yellow-800', icon: 'fas fa-clock' },
      approved: { color: 'bg-green-100 text-green-800', icon: 'fas fa-check-circle' },
      rejected: { color: 'bg-red-100 text-red-800', icon: 'fas fa-times-circle' },
      'on-hold': { color: 'bg-orange-100 text-orange-800', icon: 'fas fa-pause-circle' }
    };

    const config = statusConfig[status] || statusConfig.draft;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <i className={`${config.icon} mr-1`}></i>
        {status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </span>
    );
  };

  const getSortIcon = (columnKey) => {
    if (sortConfig.key !== columnKey) {
      return <i className="fas fa-sort text-gray-400"></i>;
    }
    return sortConfig.direction === 'asc' 
      ? <i className="fas fa-sort-up text-blue-600"></i>
      : <i className="fas fa-sort-down text-blue-600"></i>;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-8">
        <div className="flex items-center justify-center">
          <i className="fas fa-spinner fa-spin text-2xl text-blue-600 mr-4"></i>
          <span className="text-lg text-gray-600">Loading business cases...</span>
        </div>
      </div>
    );
  }

  if (businessCases.length === 0) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-8">
        <div className="text-center">
          <i className="fas fa-briefcase text-4xl text-gray-400 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No business cases found</h3>
          <p className="text-gray-600">No business cases match your current filters.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      {/* Table Header with Bulk Actions */}
      {selectedItems.size > 0 && (
        <div className="bg-blue-50 border-b border-blue-200 px-6 py-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-blue-800">
              {selectedItems.size} item(s) selected
            </span>
            <div className="flex space-x-2">
              <button
                onClick={() => onBulkAction('export', Array.from(selectedItems))}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                <i className="fas fa-download mr-1"></i>
                Export Selected
              </button>
              <button
                onClick={() => onBulkAction('delete', Array.from(selectedItems))}
                className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                <i className="fas fa-trash mr-1"></i>
                Delete Selected
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedItems.size === paginatedBusinessCases.length && paginatedBusinessCases.length > 0}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('name')}
              >
                <div className="flex items-center">
                  Name
                  {getSortIcon('name')}
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('businessUnit')}
              >
                <div className="flex items-center">
                  Business Unit
                  {getSortIcon('businessUnit')}
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('status')}
              >
                <div className="flex items-center">
                  Status
                  {getSortIcon('status')}
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('totalRevenue')}
              >
                <div className="flex items-center">
                  Revenue
                  {getSortIcon('totalRevenue')}
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                ROI
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Timeline
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('updatedAt')}
              >
                <div className="flex items-center">
                  Last Updated
                  {getSortIcon('updatedAt')}
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedBusinessCases.map((businessCase) => (
              <tr key={businessCase.id} className="hover:bg-gray-50">
                <td className="px-6 py-4">
                  <input
                    type="checkbox"
                    checked={selectedItems.has(businessCase.id)}
                    onChange={(e) => handleSelectItem(businessCase.id, e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </td>
                <td className="px-6 py-4">
                  <div className="flex flex-col">
                    <div className="text-sm font-medium text-gray-900 max-w-xs truncate">
                      {businessCase.name}
                    </div>
                    <div className="text-sm text-gray-500 max-w-xs truncate">
                      {businessCase.description}
                    </div>
                    {businessCase.promotedFromIdea && (
                      <div className="mt-1">
                        <span className="inline-flex items-center text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
                          <i className="fas fa-lightbulb mr-1"></i>
                          From Idea
                        </span>
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  {businessCase.businessUnit}
                </td>
                <td className="px-6 py-4">
                  {getStatusBadge(businessCase.status)}
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  {formatCurrency(businessCase.totalRevenue)}
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  <span className={`font-medium ${
                    calculateROI(businessCase) !== 'N/A' && parseFloat(calculateROI(businessCase)) > 0
                      ? 'text-green-600' 
                      : 'text-red-600'
                  }`}>
                    {calculateROI(businessCase)}
                  </span>
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  {businessCase.startYear} - {businessCase.endYear}
                </td>
                <td className="px-6 py-4 text-sm text-gray-500">
                  {formatDate(businessCase.updatedAt)}
                </td>
                <td className="px-6 py-4 text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => onView(businessCase)}
                      className="text-blue-600 hover:text-blue-900"
                      title="View Details"
                    >
                      <i className="fas fa-eye"></i>
                    </button>
                    <button
                      onClick={() => onEdit(businessCase)}
                      className="text-gray-600 hover:text-gray-900"
                      title="Edit"
                    >
                      <i className="fas fa-edit"></i>
                    </button>
                    <button
                      onClick={() => onExport(businessCase)}
                      className="text-green-600 hover:text-green-900"
                      title="Export"
                    >
                      <i className="fas fa-download"></i>
                    </button>
                    <button
                      onClick={() => onDelete(businessCase)}
                      className="text-red-600 hover:text-red-900"
                      title="Delete"
                    >
                      <i className="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="text-sm text-gray-700">
              Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, sortedBusinessCases.length)} of {sortedBusinessCases.length} results
            </span>
            <select
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="ml-4 px-3 py-1 border border-gray-300 rounded text-sm"
            >
              <option value={10}>10 per page</option>
              <option value={25}>25 per page</option>
              <option value={50}>50 per page</option>
              <option value={100}>100 per page</option>
            </select>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Previous
            </button>
            
            {/* Page Numbers */}
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
              return (
                <button
                  key={pageNum}
                  onClick={() => setCurrentPage(pageNum)}
                  className={`px-3 py-1 border rounded text-sm ${
                    currentPage === pageNum
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {pageNum}
                </button>
              );
            })}
            
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessCasesTable;
