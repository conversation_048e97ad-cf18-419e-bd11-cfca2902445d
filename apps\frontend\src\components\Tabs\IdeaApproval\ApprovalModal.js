import React, { useState } from 'react';

const ApprovalModal = ({ isOpen, onClose, onSubmit, type, idea }) => {
  const [formData, setFormData] = useState({
    comments: '',
    reason: ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
    setFormData({ comments: '', reason: '' });
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (!isOpen) return null;

  const getModalConfig = () => {
    switch (type) {
      case 'approve':
        return {
          title: 'Approve Idea',
          icon: 'fas fa-check-circle',
          iconColor: 'text-green-600',
          buttonColor: 'bg-green-600 hover:bg-green-700',
          buttonText: 'Approve Idea',
          description: 'This idea will be approved and available for business case creation.'
        };
      case 'reject':
        return {
          title: 'Reject Idea',
          icon: 'fas fa-times-circle',
          iconColor: 'text-red-600',
          buttonColor: 'bg-red-600 hover:bg-red-700',
          buttonText: 'Reject Idea',
          description: 'This idea will be rejected and the submitter will be notified.'
        };
      case 'request-info':
        return {
          title: 'Request More Information',
          icon: 'fas fa-question-circle',
          iconColor: 'text-blue-600',
          buttonColor: 'bg-blue-600 hover:bg-blue-700',
          buttonText: 'Request Information',
          description: 'The idea status will be changed to "Under Review" and the submitter will be notified.'
        };
      default:
        return {
          title: 'Review Idea',
          icon: 'fas fa-eye',
          iconColor: 'text-gray-600',
          buttonColor: 'bg-gray-600 hover:bg-gray-700',
          buttonText: 'Submit',
          description: 'Review this idea.'
        };
    }
  };

  const config = getModalConfig();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <i className={`${config.icon} ${config.iconColor} text-2xl mr-3`}></i>
              <div>
                <h2 className="text-xl font-bold text-gray-900">{config.title}</h2>
                <p className="text-sm text-gray-600 mt-1">{config.description}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <i className="fas fa-times text-xl"></i>
            </button>
          </div>
        </div>

        {/* Idea Summary */}
        <div className="px-6 py-4 bg-blue-50 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900 mb-2">{idea.title}</h3>
          <p className="text-sm text-gray-600 line-clamp-3">
            {idea.description || idea.problemStatement || 'No description provided'}
          </p>
          <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
            <span>
              <i className="fas fa-user mr-1"></i>
              {idea.submitterName || 'Unknown'}
            </span>
            <span>
              <i className="fas fa-calendar mr-1"></i>
              {new Date(idea.submissionDate || idea.createdAt).toLocaleDateString()}
            </span>
            <span>
              <i className="fas fa-flag mr-1"></i>
              {idea.priority} priority
            </span>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          {/* Rejection Reason (only for reject) */}
          {type === 'reject' && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Rejection Reason *
              </label>
              <select
                value={formData.reason}
                onChange={(e) => handleChange('reason', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                required
              >
                <option value="">Select a reason...</option>
                <option value="insufficient-detail">Insufficient Detail</option>
                <option value="not-aligned">Not Aligned with Strategy</option>
                <option value="duplicate">Duplicate Idea</option>
                <option value="technical-feasibility">Technical Feasibility Issues</option>
                <option value="budget-constraints">Budget Constraints</option>
                <option value="regulatory-compliance">Regulatory/Compliance Issues</option>
                <option value="other">Other</option>
              </select>
            </div>
          )}

          {/* Comments */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {type === 'approve' ? 'Approval Comments' : 
               type === 'reject' ? 'Additional Feedback' : 
               'Information Requested'}
              {type !== 'approve' && ' *'}
            </label>
            <textarea
              value={formData.comments}
              onChange={(e) => handleChange('comments', e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder={
                type === 'approve' ? 'Optional comments about the approval...' :
                type === 'reject' ? 'Provide specific feedback to help the submitter understand the rejection...' :
                'Specify what additional information is needed...'
              }
              required={type !== 'approve'}
            />
            <p className="text-xs text-gray-500 mt-1">
              {type === 'approve' && 'Optional: Add any comments or next steps for the submitter.'}
              {type === 'reject' && 'Required: Provide constructive feedback to help the submitter.'}
              {type === 'request-info' && 'Required: Be specific about what information you need.'}
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className={`px-6 py-2 text-white rounded-lg transition-colors ${config.buttonColor}`}
            >
              <i className={`${config.icon} mr-2`}></i>
              {config.buttonText}
            </button>
          </div>
        </form>

        {/* Warning for rejection */}
        {type === 'reject' && (
          <div className="bg-red-50 border-t border-red-200 px-6 py-3">
            <div className="flex items-center text-red-800 text-sm">
              <i className="fas fa-exclamation-triangle mr-2"></i>
              <span>
                This action cannot be undone. The submitter will be notified of the rejection.
              </span>
            </div>
          </div>
        )}

        {/* Success note for approval */}
        {type === 'approve' && (
          <div className="bg-green-50 border-t border-green-200 px-6 py-3">
            <div className="flex items-center text-green-800 text-sm">
              <i className="fas fa-check-circle mr-2"></i>
              <span>
                Once approved, this idea will be available for business case creation.
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApprovalModal;
