.process-flow {
  padding: 24px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Header */
.process-flow-header {
  margin-bottom: 32px;
  text-align: center;
}

.process-flow-header h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
}

.process-flow-header p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

/* Process Selector */
.process-selector {
  margin-bottom: 32px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.process-selector h3 {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.process-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 16px;
}

.process-option {
  padding: 20px;
  background: #f9fafb;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
}

.process-option:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.process-option.active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.option-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.option-content p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

/* Process Visualization */
.process-visualization {
  margin-bottom: 32px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.process-visualization h3 {
  margin: 0 0 8px 0;
  font-size: 22px;
  font-weight: 600;
  color: #1f2937;
}

.process-visualization > p {
  margin: 0 0 24px 0;
  color: #6b7280;
  font-size: 16px;
}

/* Process Stages */
.process-stages {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.process-stage {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.stage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s;
}

.stage-header:hover {
  opacity: 0.9;
}

.stage-header.active {
  border-bottom: 1px solid #e5e7eb;
}

.stage-info h4 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
}

.stage-info h5 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.stage-info p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.stage-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
}

.stage-toggle i {
  font-size: 14px;
  color: #374151;
}

/* Stage Details */
.stage-details {
  padding: 0 20px 20px 20px;
  background: #fafbfc;
}

.step-detail {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.step-detail:last-child {
  margin-bottom: 0;
}

.step-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.step-header h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.step-header p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.step-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.step-section {
  margin-bottom: 16px;
}

.step-section h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.step-section ul {
  margin: 0;
  padding-left: 20px;
}

.step-section li {
  margin-bottom: 4px;
  font-size: 14px;
  color: #6b7280;
}

.checklist {
  list-style: none !important;
  padding-left: 0 !important;
}

.checklist li {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.checklist input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.checklist label {
  font-size: 14px;
  color: #374151;
  cursor: pointer;
}

/* Step Metadata */
.step-metadata {
  grid-column: 1 / -1;
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.metadata-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.metadata-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metadata-label {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
}

.metadata-value {
  font-size: 14px;
  color: #374151;
}

/* Process Actions */
.process-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn.primary {
  background: #3b82f6;
  color: white;
}

.action-btn.primary:hover {
  background: #2563eb;
}

.action-btn.secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.action-btn.secondary:hover {
  background: #e5e7eb;
}

/* Step Progress Indicator */
.step-progress-indicator {
  margin-top: 12px;
  padding: 8px 12px;
  background: #f9fafb;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.step-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.step-status.completed {
  color: #059669;
}

.step-status.pending {
  color: #d97706;
}

/* Progress Tracker */
.progress-tracker {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 320px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  z-index: 1000;
}

.tracker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
}

.tracker-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.close-tracker {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.close-tracker:hover {
  background: #f3f4f6;
  color: #374151;
}

.progress-overview {
  padding: 16px 20px;
  max-height: 300px;
  overflow-y: auto;
}

.stage-progress {
  margin-bottom: 16px;
}

.stage-progress:last-child {
  margin-bottom: 0;
}

.stage-progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.stage-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.progress-text {
  font-size: 12px;
  color: #6b7280;
}

.progress-bar {
  height: 6px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  transition: width 0.3s ease;
}

.tracker-actions {
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 8px;
}

.tracker-actions .action-btn {
  flex: 1;
  padding: 8px 12px;
  font-size: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .process-flow {
    padding: 16px;
  }
  
  .process-options {
    grid-template-columns: 1fr;
  }
  
  .step-content {
    grid-template-columns: 1fr;
  }
  
  .metadata-grid {
    grid-template-columns: 1fr;
  }
  
  .process-actions {
    flex-direction: column;
  }
  
  .stage-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
}
