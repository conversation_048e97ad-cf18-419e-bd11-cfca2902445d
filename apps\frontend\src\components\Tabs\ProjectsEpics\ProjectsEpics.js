import React, { useState, useEffect } from 'react';
import { useData } from '../../../context/DataContext';
import ProjectEpicForm from './ProjectEpicForm/ProjectEpicForm';
import ProjectEpicList from './ProjectEpicList/ProjectEpicList';
import ProjectEpicDetail from './ProjectEpicDetail/ProjectEpicDetail';
import './ProjectsEpics.css';

const ProjectsEpics = () => {
  const {
    projects,
    businessCases,
    programs,
    loading,
    errors,
    loadProjects,
    loadPrograms,
    loadBusinessCases
  } = useData();

  const [activeView, setActiveView] = useState('list');
  const [selectedProject, setSelectedProject] = useState(null);
  const [editingProject, setEditingProject] = useState(null);

  useEffect(() => {
    // Load data when component mounts
    loadProjects();
    loadPrograms();
    loadBusinessCases();
  }, [loadProjects, loadPrograms, loadBusinessCases]);

  const handleCreateNew = () => {
    setEditingProject(null);
    setSelectedProject(null);
    setActiveView('form');
  };

  const handleEdit = (project) => {
    setEditingProject(project);
    setSelectedProject(null);
    setActiveView('form');
  };

  const handleView = (project) => {
    setSelectedProject(project);
    setEditingProject(null);
    setActiveView('detail');
  };

  const handleFormSuccess = () => {
    setActiveView('list');
    setEditingProject(null);
    setSelectedProject(null);
    loadProjects(); // Refresh data
    loadBusinessCases(); // Refresh business cases in case they were updated
  };

  const handleFormCancel = () => {
    setActiveView('list');
    setEditingProject(null);
    setSelectedProject(null);
  };

  const handleBackToList = () => {
    setActiveView('list');
    setSelectedProject(null);
    setEditingProject(null);
  };

  // Loading state
  if (loading.projects) {
    return (
      <div className="projects-epics">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading projects and epics...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (errors.projects) {
    return (
      <div className="projects-epics">
        <div className="error-container">
          <h3>Error Loading Projects & Epics</h3>
          <p>{errors.projects}</p>
          <button onClick={loadProjects}>Retry</button>
        </div>
      </div>
    );
  }

  return (
    <div className="projects-epics">
      {/* Header */}
      <div className="projects-header">
        <div className="header-info">
          <h2>Projects & Epics</h2>
          <p>Manage projects and epics with milestone tracking and business case linking</p>
        </div>
        <div className="header-actions">
          {activeView !== 'list' && (
            <button onClick={handleBackToList} className="btn btn-secondary">
              <i className="fas fa-arrow-left"></i>
              Back to List
            </button>
          )}
          {activeView === 'list' && (
            <button onClick={handleCreateNew} className="btn btn-primary">
              <i className="fas fa-plus"></i>
              Create Project/Epic
            </button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="projects-content">
        {activeView === 'list' && (
          <ProjectEpicList
            projects={projects}
            businessCases={businessCases}
            programs={programs}
            onEdit={handleEdit}
            onView={handleView}
            onRefresh={loadProjects}
          />
        )}

        {activeView === 'form' && (
          <ProjectEpicForm
            project={editingProject}
            businessCases={businessCases}
            programs={programs}
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
          />
        )}

        {activeView === 'detail' && selectedProject && (
          <ProjectEpicDetail
            project={selectedProject}
            businessCases={businessCases}
            programs={programs}
            onEdit={handleEdit}
            onBack={handleBackToList}
          />
        )}
      </div>
    </div>
  );
};

export default ProjectsEpics;
