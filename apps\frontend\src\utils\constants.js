/**
 * MasterBC - Application Constants
 * 
 * <AUTHOR>
 * @repository https://github.com/mahegyaneshpandey/spm
 * @date January 27, 2025
 */

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:5000',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// Authentication Constants
export const AUTH_CONFIG = {
  TOKEN_KEY: 'authToken',
  USER_KEY: 'currentUser',
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  DEMO_CREDENTIALS: [
    { email: 'admin', password: 'admin', role: 'admin' },
    { email: 'test', password: 'test', role: 'user' },
    { email: 'gyanesh', password: 'gyanesh123', role: 'admin' },
  ],
};

// Toast Notification Configuration
export const TOAST_CONFIG = {
  position: 'top-right',
  autoClose: 5000,
  hideProgressBar: false,
  newestOnTop: false,
  closeOnClick: true,
  rtl: false,
  pauseOnFocusLoss: true,
  draggable: true,
  pauseOnHover: true,
  className: 'mt-16',
};

// Application Routes
export const ROUTES = {
  HOME: '/',
  DASHBOARD: '/dashboard',
  BUSINESS_CASES: '/business-cases',
  MASTER_BC: '/master-bc',
  PROJECTS: '/projects',
  PROGRAMS: '/programs',
  IDEAS: '/ideas',
  RELATIONSHIPS: '/relationships',
  BULK_OPERATIONS: '/bulk-operations',
  ARCHITECTURE: '/architecture',
};

// Business Case Status Options
export const BUSINESS_CASE_STATUS = {
  DRAFT: 'draft',
  ACTIVE: 'active',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
};

// Project/Epic Types
export const PROJECT_TYPES = {
  PROJECT: 'project',
  EPIC: 'epic',
};

// Priority Levels
export const PRIORITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
};

// Business Units
export const BUSINESS_UNITS = [
  'IT',
  'Operations',
  'Marketing',
  'Finance',
  'HR',
  'Sales',
  'R&D',
  'Legal',
];

// Financial Metrics
export const FINANCIAL_METRICS = {
  IRR: 'irr',
  NPV: 'npv',
  PAYBACK_PERIOD: 'paybackPeriod',
  GROSS_MARGIN: 'grossMargin',
  COMMERCIAL_MARGIN: 'commercialMargin',
  YIELD_INDEX: 'yieldIndex',
};

// Data Loading States
export const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
};

// Form Validation Rules
export const VALIDATION_RULES = {
  REQUIRED_FIELD: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  MIN_PASSWORD_LENGTH: 'Password must be at least 6 characters',
  INVALID_NUMBER: 'Please enter a valid number',
  INVALID_DATE: 'Please enter a valid date',
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_FILE_TYPES: ['.xlsx', '.xls', '.csv'],
};

// UI Configuration
export const UI_CONFIG = {
  ITEMS_PER_PAGE: 10,
  MAX_ITEMS_PER_PAGE: 100,
  DEBOUNCE_DELAY: 300,
  ANIMATION_DURATION: 200,
  MODAL_Z_INDEX: 1000,
  TOOLTIP_DELAY: 500,
};

// Export Configuration
export const EXPORT_CONFIG = {
  EXCEL_MIME_TYPE: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  CSV_MIME_TYPE: 'text/csv',
  DEFAULT_FILENAME: 'business_case_export',
  DATE_FORMAT: 'YYYY-MM-DD',
  CURRENCY_FORMAT: 'USD',
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
  AUTHENTICATION_FAILED: 'Authentication failed. Please check your credentials.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  FILE_UPLOAD_ERROR: 'File upload failed. Please try again.',
  EXPORT_ERROR: 'Export failed. Please try again.',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Successfully logged in!',
  LOGOUT_SUCCESS: 'Successfully logged out!',
  SAVE_SUCCESS: 'Successfully saved!',
  DELETE_SUCCESS: 'Successfully deleted!',
  UPDATE_SUCCESS: 'Successfully updated!',
  EXPORT_SUCCESS: 'Export completed successfully!',
  IMPORT_SUCCESS: 'Import completed successfully!',
};

// Application Metadata
export const APP_METADATA = {
  NAME: 'MasterBC',
  FULL_NAME: 'Master Business Case Management System',
  VERSION: '1.0.0',
  AUTHOR: 'Gyanesh K Pandey',
  REPOSITORY: 'https://github.com/mahegyaneshpandey/spm',
  DESCRIPTION: 'Strategic Portfolio Management and Business Case Analysis Platform',
};
