import { 
  SensitivityAnalysis, 
  SensitivityVariable, 
  Scenario, 
  ScenarioType, 
  FinancialModel, 
  SensitivityResult,
  VariableCategory 
} from '../../../shared/src/types';
import { FinancialCalculationService } from './financialCalculations';

export class SensitivityAnalysisService {
  /**
   * Perform comprehensive sensitivity analysis
   */
  static performSensitivityAnalysis(
    baseModel: FinancialModel,
    variables: SensitivityVariable[]
  ): SensitivityAnalysis {
    const scenarios = this.generateScenarios(baseModel, variables);
    const results = this.calculateSensitivityResults(scenarios, variables);

    return {
      id: this.generateId(),
      name: `Sensitivity Analysis - ${baseModel.name}`,
      variables,
      scenarios,
      results,
      createdBy: baseModel.createdBy,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  /**
   * Generate base, best, and worst case scenarios
   */
  private static generateScenarios(
    baseModel: FinancialModel,
    variables: SensitivityVariable[]
  ): Scenario[] {
    const scenarios: Scenario[] = [];

    // Base case scenario
    const baseVariables: Record<string, number> = {};
    variables.forEach(variable => {
      baseVariables[variable.id] = variable.baseValue;
    });

    const baseResults = FinancialCalculationService.calculateFinancialResults(
      this.applyVariablesToModel(baseModel, baseVariables)
    );

    scenarios.push({
      id: this.generateId(),
      name: 'Base Case',
      type: ScenarioType.BASE_CASE,
      variables: baseVariables,
      results: baseResults
    });

    // Best case scenario (optimistic values)
    const bestVariables: Record<string, number> = {};
    variables.forEach(variable => {
      bestVariables[variable.id] = this.getBestCaseValue(variable);
    });

    const bestResults = FinancialCalculationService.calculateFinancialResults(
      this.applyVariablesToModel(baseModel, bestVariables)
    );

    scenarios.push({
      id: this.generateId(),
      name: 'Best Case',
      type: ScenarioType.BEST_CASE,
      variables: bestVariables,
      results: bestResults
    });

    // Worst case scenario (pessimistic values)
    const worstVariables: Record<string, number> = {};
    variables.forEach(variable => {
      worstVariables[variable.id] = this.getWorstCaseValue(variable);
    });

    const worstResults = FinancialCalculationService.calculateFinancialResults(
      this.applyVariablesToModel(baseModel, worstVariables)
    );

    scenarios.push({
      id: this.generateId(),
      name: 'Worst Case',
      type: ScenarioType.WORST_CASE,
      variables: worstVariables,
      results: worstResults
    });

    // Generate individual variable scenarios for tornado chart
    variables.forEach(variable => {
      // High value scenario
      const highVariables = { ...baseVariables };
      highVariables[variable.id] = variable.maxValue;
      
      const highResults = FinancialCalculationService.calculateFinancialResults(
        this.applyVariablesToModel(baseModel, highVariables)
      );

      scenarios.push({
        id: this.generateId(),
        name: `${variable.name} - High`,
        type: ScenarioType.CUSTOM,
        variables: highVariables,
        results: highResults
      });

      // Low value scenario
      const lowVariables = { ...baseVariables };
      lowVariables[variable.id] = variable.minValue;
      
      const lowResults = FinancialCalculationService.calculateFinancialResults(
        this.applyVariablesToModel(baseModel, lowVariables)
      );

      scenarios.push({
        id: this.generateId(),
        name: `${variable.name} - Low`,
        type: ScenarioType.CUSTOM,
        variables: lowVariables,
        results: lowResults
      });
    });

    return scenarios;
  }

  /**
   * Calculate sensitivity results for tornado chart
   */
  private static calculateSensitivityResults(
    scenarios: Scenario[],
    variables: SensitivityVariable[]
  ): SensitivityResult[] {
    const baseScenario = scenarios.find(s => s.type === ScenarioType.BASE_CASE);
    if (!baseScenario) throw new Error('Base case scenario not found');

    const results: SensitivityResult[] = [];

    variables.forEach(variable => {
      const highScenario = scenarios.find(s => 
        s.name === `${variable.name} - High` && s.type === ScenarioType.CUSTOM
      );
      const lowScenario = scenarios.find(s => 
        s.name === `${variable.name} - Low` && s.type === ScenarioType.CUSTOM
      );

      if (highScenario && lowScenario) {
        const impact = this.calculateImpact(baseScenario, highScenario, lowScenario);
        const correlation = this.calculateCorrelation(variable, baseScenario, highScenario, lowScenario);
        const elasticity = this.calculateElasticity(variable, baseScenario, highScenario);

        results.push({
          variableId: variable.id,
          variableName: variable.name,
          impact,
          correlation,
          elasticity
        });
      }
    });

    // Sort by impact (highest first) for tornado chart
    return results.sort((a, b) => Math.abs(b.impact) - Math.abs(a.impact));
  }

  /**
   * Calculate impact of variable change on NPV
   */
  private static calculateImpact(
    baseScenario: Scenario,
    highScenario: Scenario,
    lowScenario: Scenario
  ): number {
    const baseNPV = baseScenario.results.npv;
    const highNPV = highScenario.results.npv;
    const lowNPV = lowScenario.results.npv;

    // Return the maximum absolute change from base case
    const highImpact = Math.abs(highNPV - baseNPV);
    const lowImpact = Math.abs(lowNPV - baseNPV);
    
    return Math.max(highImpact, lowImpact);
  }

  /**
   * Calculate correlation between variable and NPV
   */
  private static calculateCorrelation(
    variable: SensitivityVariable,
    baseScenario: Scenario,
    highScenario: Scenario,
    lowScenario: Scenario
  ): number {
    const baseNPV = baseScenario.results.npv;
    const highNPV = highScenario.results.npv;
    const lowNPV = lowScenario.results.npv;

    const variableChange = variable.maxValue - variable.minValue;
    const npvChange = highNPV - lowNPV;

    // Simple correlation: positive if both move in same direction
    return npvChange / Math.abs(variableChange);
  }

  /**
   * Calculate elasticity of NPV with respect to variable
   */
  private static calculateElasticity(
    variable: SensitivityVariable,
    baseScenario: Scenario,
    highScenario: Scenario
  ): number {
    const baseNPV = baseScenario.results.npv;
    const highNPV = highScenario.results.npv;

    if (baseNPV === 0 || variable.baseValue === 0) return 0;

    const percentChangeNPV = (highNPV - baseNPV) / baseNPV;
    const percentChangeVariable = (variable.maxValue - variable.baseValue) / variable.baseValue;

    return percentChangeNPV / percentChangeVariable;
  }

  /**
   * Apply variable values to financial model
   */
  private static applyVariablesToModel(
    baseModel: FinancialModel,
    variables: Record<string, number>
  ): FinancialModel {
    const model = JSON.parse(JSON.stringify(baseModel)); // Deep clone

    Object.entries(variables).forEach(([variableId, value]) => {
      // Find the variable definition to understand what it affects
      const variable = this.findVariableById(variableId);
      if (variable) {
        this.applyVariableToModel(model, variable, value);
      }
    });

    return model;
  }

  /**
   * Apply a single variable value to the model
   */
  private static applyVariableToModel(
    model: FinancialModel,
    variable: SensitivityVariable,
    value: number
  ): void {
    switch (variable.category) {
      case VariableCategory.RATE:
        if (variable.name.toLowerCase().includes('discount')) {
          model.parameters.discountRate = value;
        } else if (variable.name.toLowerCase().includes('tax')) {
          model.parameters.taxRate = value;
        }
        break;
      
      case VariableCategory.COST:
        // Apply to OPEX items (simplified - in real implementation, would be more specific)
        model.costStructure.opex.forEach(opex => {
          opex.amount *= (value / variable.baseValue);
        });
        break;
      
      case VariableCategory.PRICE:
        // Apply to sales prices
        model.salesStructure.regions.forEach(region => {
          region.price *= (value / variable.baseValue);
        });
        model.salesStructure.offers.forEach(offer => {
          offer.unitPrice *= (value / variable.baseValue);
        });
        break;
      
      case VariableCategory.VOLUME:
        // Apply to sales volumes
        model.salesStructure.regions.forEach(region => {
          region.volume *= (value / variable.baseValue);
        });
        model.salesStructure.offers.forEach(offer => {
          offer.volume *= (value / variable.baseValue);
        });
        break;
    }
  }

  /**
   * Get best case value for a variable
   */
  private static getBestCaseValue(variable: SensitivityVariable): number {
    // For costs, best case is minimum; for revenue drivers, best case is maximum
    switch (variable.category) {
      case VariableCategory.COST:
        return variable.minValue;
      case VariableCategory.PRICE:
      case VariableCategory.VOLUME:
        return variable.maxValue;
      case VariableCategory.RATE:
        // Depends on the specific rate
        return variable.name.toLowerCase().includes('tax') ? variable.minValue : variable.maxValue;
      default:
        return variable.maxValue;
    }
  }

  /**
   * Get worst case value for a variable
   */
  private static getWorstCaseValue(variable: SensitivityVariable): number {
    // Opposite of best case
    switch (variable.category) {
      case VariableCategory.COST:
        return variable.maxValue;
      case VariableCategory.PRICE:
      case VariableCategory.VOLUME:
        return variable.minValue;
      case VariableCategory.RATE:
        return variable.name.toLowerCase().includes('tax') ? variable.maxValue : variable.minValue;
      default:
        return variable.minValue;
    }
  }

  /**
   * Find variable by ID (placeholder - would connect to database)
   */
  private static findVariableById(id: string): SensitivityVariable | null {
    // This would typically query the database
    // For now, return null as this is a placeholder
    return null;
  }

  /**
   * Generate unique ID
   */
  private static generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}
