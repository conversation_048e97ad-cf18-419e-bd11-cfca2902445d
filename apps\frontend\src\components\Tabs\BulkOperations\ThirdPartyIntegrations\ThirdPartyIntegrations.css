.third-party-integrations {
  padding: 24px;
}

/* Header */
.integrations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-content h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.header-content p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* Integration Navigation */
.integration-nav {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 32px;
}

.integration-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s;
}

.integration-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.integration-card.active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: #f3f4f6;
  border-radius: 8px;
}

.card-icon i {
  font-size: 24px;
}

.card-info {
  flex: 1;
}

.card-info h5 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.card-info p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-configured {
  background: #dcfce7;
  color: #166534;
}

.status-available {
  background: #fef3c7;
  color: #92400e;
}

.status-disabled {
  background: #fee2e2;
  color: #991b1b;
}

.card-features {
  margin-top: 12px;
}

.features-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.feature-tag {
  padding: 4px 8px;
  background: #f3f4f6;
  color: #374151;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

/* Integration Content */
.integration-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 32px;
  min-height: 400px;
}

/* Integration Overview */
.integration-overview {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.overview-header {
  margin-bottom: 24px;
}

.overview-header h4 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.stat-card i {
  font-size: 24px;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}

.stat-label {
  display: block;
  font-size: 14px;
  color: #6b7280;
}

/* Recent Activity */
.recent-activity h5 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.activity-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: white;
  border-radius: 6px;
  flex-shrink: 0;
}

.activity-icon i {
  font-size: 18px;
}

.activity-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.activity-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.activity-description {
  font-size: 14px;
  color: #6b7280;
}

.activity-time {
  font-size: 12px;
  color: #9ca3af;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-outline {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-outline:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

/* Utility Classes */
.text-blue-500 { color: #3b82f6; }
.text-green-500 { color: #10b981; }
.text-purple-500 { color: #8b5cf6; }
.text-orange-500 { color: #f59e0b; }
.text-red-500 { color: #ef4444; }

/* Responsive Design */
@media (max-width: 768px) {
  .third-party-integrations {
    padding: 16px;
  }
  
  .integrations-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .card-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .overview-stats {
    grid-template-columns: 1fr;
  }
  
  .activity-item {
    flex-direction: column;
    text-align: center;
  }
}
