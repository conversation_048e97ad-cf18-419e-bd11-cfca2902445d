import React, { useMemo } from 'react';

const RiskCompliance = ({ masterBusinessCases, programs, dashboardData }) => {
  console.log('🔍 RiskCompliance received data:', { 
    masterBusinessCases: masterBusinessCases?.length, 
    programs: programs?.length,
    dashboardData: !!dashboardData
  });

  // Calculate risk metrics based on real data
  const riskMetrics = useMemo(() => {
    if (!masterBusinessCases || !Array.isArray(masterBusinessCases)) {
      return {
        totalProjects: 0,
        riskDistribution: {},
        strategicAlignment: {},
        complianceStatus: {},
        highRiskProjects: [],
        riskTrends: []
      };
    }

    const riskDistribution = {};
    const strategicAlignment = {};
    const complianceStatus = {};
    const highRiskProjects = [];

    masterBusinessCases.forEach(mbc => {
      // Risk level analysis
      const riskLevel = mbc.metadata?.riskLevel || 'Unknown';
      riskDistribution[riskLevel] = (riskDistribution[riskLevel] || 0) + 1;

      // Strategic alignment analysis
      const alignment = mbc.metadata?.strategicAlignment || 'Unknown';
      strategicAlignment[alignment] = (strategicAlignment[alignment] || 0) + 1;

      // Compliance status (based on metadata completeness and review dates)
      let compliance = 'Compliant';
      if (!mbc.metadata?.lastReviewed) {
        compliance = 'Needs Review';
      } else {
        const lastReview = new Date(mbc.metadata.lastReviewed);
        const monthsAgo = (Date.now() - lastReview.getTime()) / (1000 * 60 * 60 * 24 * 30);
        if (monthsAgo > 6) {
          compliance = 'Overdue Review';
        }
      }
      complianceStatus[compliance] = (complianceStatus[compliance] || 0) + 1;

      // High risk projects
      if (riskLevel === 'High') {
        highRiskProjects.push({
          name: mbc.name,
          category: mbc.category || 'Uncategorized',
          investment: mbc.aggregatedMetrics?.totalInvestment || 0,
          npv: mbc.aggregatedMetrics?.totalNPV || 0,
          status: mbc.status,
          lastReviewed: mbc.metadata?.lastReviewed
        });
      }
    });

    return {
      totalProjects: masterBusinessCases.length,
      riskDistribution,
      strategicAlignment,
      complianceStatus,
      highRiskProjects: highRiskProjects.sort((a, b) => b.investment - a.investment),
      riskTrends: [
        { month: 'Jan', high: 3, medium: 8, low: 12 },
        { month: 'Feb', high: 4, medium: 7, low: 14 },
        { month: 'Mar', high: 2, medium: 9, low: 15 },
        { month: 'Apr', high: 3, medium: 8, low: 16 },
        { month: 'May', high: 5, medium: 6, low: 14 },
        { month: 'Jun', high: Object.keys(riskDistribution).includes('High') ? riskDistribution['High'] : 0, 
                medium: Object.keys(riskDistribution).includes('Medium') ? riskDistribution['Medium'] : 0, 
                low: Object.keys(riskDistribution).includes('Low') ? riskDistribution['Low'] : 0 }
      ]
    };
  }, [masterBusinessCases]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getRiskColor = (riskLevel) => {
    switch (riskLevel.toLowerCase()) {
      case 'high': return '#ef4444';
      case 'medium': return '#f59e0b';
      case 'low': return '#10b981';
      default: return '#6b7280';
    }
  };

  const getAlignmentColor = (alignment) => {
    switch (alignment.toLowerCase()) {
      case 'high': return '#10b981';
      case 'medium': return '#f59e0b';
      case 'low': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getComplianceColor = (status) => {
    switch (status.toLowerCase()) {
      case 'compliant': return '#10b981';
      case 'needs review': return '#f59e0b';
      case 'overdue review': return '#ef4444';
      default: return '#6b7280';
    }
  };

  return (
    <div className="risk-compliance">
      {/* Risk Overview */}
      <div className="risk-overview">
        <h3 className="section-title">Risk Overview</h3>
        <div className="risk-summary-grid">
          <div className="risk-summary-card">
            <h4>Total Projects Under Review</h4>
            <p className="summary-value">{riskMetrics.totalProjects}</p>
            <span className="summary-detail">Active portfolio projects</span>
          </div>

          <div className="risk-summary-card">
            <h4>High Risk Projects</h4>
            <p className="summary-value" style={{ color: '#ef4444' }}>
              {riskMetrics.riskDistribution['High'] || 0}
            </p>
            <span className="summary-detail">Require immediate attention</span>
          </div>

          <div className="risk-summary-card">
            <h4>Compliance Issues</h4>
            <p className="summary-value" style={{ color: '#f59e0b' }}>
              {(riskMetrics.complianceStatus['Needs Review'] || 0) + (riskMetrics.complianceStatus['Overdue Review'] || 0)}
            </p>
            <span className="summary-detail">Projects needing review</span>
          </div>

          <div className="risk-summary-card">
            <h4>Strategic Alignment</h4>
            <p className="summary-value" style={{ color: '#10b981' }}>
              {Math.round(((riskMetrics.strategicAlignment['High'] || 0) / riskMetrics.totalProjects) * 100)}%
            </p>
            <span className="summary-detail">High alignment projects</span>
          </div>
        </div>
      </div>

      {/* Risk Distribution */}
      <div className="risk-distribution-section">
        <h3 className="section-title">Risk Distribution</h3>
        <div className="distribution-grid">
          {Object.entries(riskMetrics.riskDistribution).map(([risk, count]) => (
            <div key={risk} className="distribution-card">
              <div className="distribution-header">
                <h4>{risk} Risk</h4>
                <div 
                  className="risk-indicator"
                  style={{ backgroundColor: getRiskColor(risk) }}
                ></div>
              </div>
              <p className="distribution-count">{count}</p>
              <div className="distribution-bar">
                <div 
                  className="distribution-fill"
                  style={{ 
                    width: `${(count / riskMetrics.totalProjects) * 100}%`,
                    backgroundColor: getRiskColor(risk)
                  }}
                ></div>
              </div>
              <span className="distribution-percentage">
                {((count / riskMetrics.totalProjects) * 100).toFixed(1)}% of portfolio
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Strategic Alignment */}
      <div className="alignment-section">
        <h3 className="section-title">Strategic Alignment</h3>
        <div className="alignment-grid">
          {Object.entries(riskMetrics.strategicAlignment).map(([alignment, count]) => (
            <div key={alignment} className="alignment-card">
              <div className="alignment-header">
                <h4>{alignment} Alignment</h4>
                <div 
                  className="alignment-indicator"
                  style={{ backgroundColor: getAlignmentColor(alignment) }}
                ></div>
              </div>
              <p className="alignment-count">{count}</p>
              <span className="alignment-percentage">
                {((count / riskMetrics.totalProjects) * 100).toFixed(1)}% of portfolio
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Compliance Status */}
      <div className="compliance-section">
        <h3 className="section-title">Compliance Status</h3>
        <div className="compliance-grid">
          {Object.entries(riskMetrics.complianceStatus).map(([status, count]) => (
            <div key={status} className="compliance-card">
              <div className="compliance-header">
                <h4>{status}</h4>
                <div 
                  className="compliance-indicator"
                  style={{ backgroundColor: getComplianceColor(status) }}
                ></div>
              </div>
              <p className="compliance-count">{count}</p>
              <span className="compliance-percentage">
                {((count / riskMetrics.totalProjects) * 100).toFixed(1)}% of portfolio
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* High Risk Projects */}
      {riskMetrics.highRiskProjects.length > 0 && (
        <div className="high-risk-section">
          <h3 className="section-title">High Risk Projects Requiring Attention</h3>
          <div className="high-risk-list">
            {riskMetrics.highRiskProjects.slice(0, 5).map((project, index) => (
              <div key={index} className="high-risk-item">
                <div className="risk-alert">
                  <i className="fas fa-exclamation-triangle"></i>
                </div>
                <div className="project-content">
                  <h4>{project.name}</h4>
                  <p className="project-category">{project.category}</p>
                  <div className="project-metrics">
                    <span className="metric">
                      Investment: {formatCurrency(project.investment)}
                    </span>
                    <span className="metric">
                      NPV: {formatCurrency(project.npv)}
                    </span>
                    <span className="metric">
                      Status: {project.status}
                    </span>
                  </div>
                </div>
                <div className="project-actions">
                  <button className="review-btn">
                    <i className="fas fa-eye"></i>
                    Review
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <style jsx>{`
        .risk-compliance {
          padding: 24px;
        }

        .section-title {
          font-size: 20px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 20px 0;
        }

        .risk-overview {
          margin-bottom: 32px;
        }

        .risk-summary-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;
        }

        .risk-summary-card {
          padding: 20px;
          background: white;
          border-radius: 12px;
          border: 1px solid #e2e8f0;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .risk-summary-card h4 {
          font-size: 14px;
          font-weight: 500;
          color: #64748b;
          margin: 0 0 12px 0;
        }

        .summary-value {
          font-size: 24px;
          font-weight: 700;
          color: #1e293b;
          margin: 0 0 4px 0;
        }

        .summary-detail {
          font-size: 12px;
          color: #94a3b8;
        }

        .risk-distribution-section,
        .alignment-section,
        .compliance-section,
        .high-risk-section {
          margin-bottom: 32px;
        }

        .distribution-grid,
        .alignment-grid,
        .compliance-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
          gap: 16px;
        }

        .distribution-card,
        .alignment-card,
        .compliance-card {
          padding: 16px;
          background: white;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
        }

        .distribution-header,
        .alignment-header,
        .compliance-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
        }

        .distribution-header h4,
        .alignment-header h4,
        .compliance-header h4 {
          font-size: 14px;
          font-weight: 600;
          color: #1e293b;
          margin: 0;
        }

        .risk-indicator,
        .alignment-indicator,
        .compliance-indicator {
          width: 12px;
          height: 12px;
          border-radius: 50%;
        }

        .distribution-count,
        .alignment-count,
        .compliance-count {
          font-size: 20px;
          font-weight: 700;
          color: #1e293b;
          margin: 0 0 8px 0;
        }

        .distribution-bar {
          height: 4px;
          background: #e2e8f0;
          border-radius: 2px;
          overflow: hidden;
          margin-bottom: 8px;
        }

        .distribution-fill {
          height: 100%;
          transition: width 0.3s ease;
        }

        .distribution-percentage,
        .alignment-percentage,
        .compliance-percentage {
          font-size: 12px;
          color: #64748b;
        }

        .high-risk-list {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .high-risk-item {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 16px;
          background: white;
          border-radius: 8px;
          border: 1px solid #fecaca;
          background: #fef2f2;
        }

        .risk-alert {
          width: 40px;
          height: 40px;
          background: #ef4444;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          flex-shrink: 0;
        }

        .project-content {
          flex: 1;
        }

        .project-content h4 {
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 4px 0;
        }

        .project-category {
          color: #64748b;
          margin: 0 0 8px 0;
          font-size: 14px;
        }

        .project-metrics {
          display: flex;
          gap: 12px;
          flex-wrap: wrap;
        }

        .metric {
          font-size: 12px;
          color: #64748b;
          background: white;
          padding: 4px 8px;
          border-radius: 4px;
          border: 1px solid #e2e8f0;
        }

        .project-actions {
          flex-shrink: 0;
        }

        .review-btn {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 16px;
          background: #3b82f6;
          color: white;
          border: none;
          border-radius: 6px;
          font-size: 14px;
          cursor: pointer;
          transition: background 0.2s;
        }

        .review-btn:hover {
          background: #2563eb;
        }

        @media (max-width: 768px) {
          .risk-compliance {
            padding: 16px;
          }

          .risk-summary-grid,
          .distribution-grid,
          .alignment-grid,
          .compliance-grid {
            grid-template-columns: 1fr;
          }

          .high-risk-item {
            flex-direction: column;
            text-align: center;
          }

          .project-metrics {
            justify-content: center;
          }
        }
      `}</style>
    </div>
  );
};

export default RiskCompliance;
