import React from 'react';

const BusinessCaseDetailModal = ({ isOpen, onClose, businessCase, onEdit, onExport }) => {
  if (!isOpen || !businessCase) return null;

  const formatCurrency = (amount) => {
    if (!amount) return '$0';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateROI = () => {
    const totalInvestment = (businessCase.totalCapex || 0) + (businessCase.totalOpex || 0);
    const totalRevenue = businessCase.totalRevenue || 0;
    
    if (totalInvestment === 0) return 'N/A';
    
    const roi = ((totalRevenue - totalInvestment) / totalInvestment) * 100;
    return `${roi.toFixed(1)}%`;
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      draft: { color: 'bg-gray-100 text-gray-800', icon: 'fas fa-edit' },
      'under-review': { color: 'bg-yellow-100 text-yellow-800', icon: 'fas fa-clock' },
      approved: { color: 'bg-green-100 text-green-800', icon: 'fas fa-check-circle' },
      rejected: { color: 'bg-red-100 text-red-800', icon: 'fas fa-times-circle' },
      'on-hold': { color: 'bg-orange-100 text-orange-800', icon: 'fas fa-pause-circle' }
    };

    const config = statusConfig[status] || statusConfig.draft;
    
    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.color}`}>
        <i className={`${config.icon} mr-2`}></i>
        {status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </span>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">{businessCase.name}</h2>
              <p className="text-blue-100 mt-1">{businessCase.businessUnit}</p>
            </div>
            <div className="flex items-center space-x-3">
              {getStatusBadge(businessCase.status)}
              <button
                onClick={onClose}
                className="text-white hover:bg-white hover:bg-opacity-20 rounded-lg p-2"
              >
                <i className="fas fa-times text-xl"></i>
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[calc(90vh-200px)] overflow-y-auto">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-blue-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">
                {formatCurrency(businessCase.totalRevenue)}
              </div>
              <div className="text-sm text-blue-600">Total Revenue</div>
            </div>
            <div className="bg-green-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(businessCase.totalCapex)}
              </div>
              <div className="text-sm text-green-600">Total CAPEX</div>
            </div>
            <div className="bg-orange-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-orange-600">
                {formatCurrency(businessCase.totalOpex)}
              </div>
              <div className="text-sm text-orange-600">Total OPEX</div>
            </div>
            <div className="bg-purple-50 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${
                calculateROI() !== 'N/A' && parseFloat(calculateROI()) > 0
                  ? 'text-purple-600' 
                  : 'text-red-600'
              }`}>
                {calculateROI()}
              </div>
              <div className="text-sm text-purple-600">ROI</div>
            </div>
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Timeline</label>
                  <p className="text-sm text-gray-900">{businessCase.startYear} - {businessCase.endYear}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Created</label>
                  <p className="text-sm text-gray-900">{formatDate(businessCase.createdAt)}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Last Updated</label>
                  <p className="text-sm text-gray-900">{formatDate(businessCase.updatedAt)}</p>
                </div>
                {businessCase.promotedFromIdea && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Source</label>
                    <span className="inline-flex items-center text-sm text-green-600 bg-green-50 px-2 py-1 rounded">
                      <i className="fas fa-lightbulb mr-1"></i>
                      Promoted from Idea
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
              {businessCase.tags && businessCase.tags.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {businessCase.tags.map((tag, index) => (
                    <span key={index} className="inline-block bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">No tags assigned</p>
              )}
            </div>
          </div>

          {/* Description */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Description</h3>
            <p className="text-gray-700 leading-relaxed">
              {businessCase.description || 'No description provided'}
            </p>
          </div>

          {/* Business Case Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Problem Statement</h3>
              <p className="text-gray-700 leading-relaxed">
                {businessCase.problemStatement || 'No problem statement provided'}
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Proposed Solution</h3>
              <p className="text-gray-700 leading-relaxed">
                {businessCase.proposedSolution || 'No proposed solution provided'}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Strategic Alignment</h3>
              <p className="text-gray-700 leading-relaxed">
                {businessCase.strategicAlignment || 'No strategic alignment information provided'}
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Success Criteria</h3>
              <p className="text-gray-700 leading-relaxed">
                {businessCase.successCriteria || 'No success criteria defined'}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Assumptions</h3>
              <p className="text-gray-700 leading-relaxed">
                {businessCase.assumptions || 'No assumptions documented'}
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Risks & Mitigation</h3>
              <p className="text-gray-700 leading-relaxed">
                {businessCase.risks || 'No risks identified'}
              </p>
            </div>
          </div>

          {/* Financial Breakdown */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Breakdown</h3>
            
            {/* CAPEX Items */}
            {businessCase.capexItems && businessCase.capexItems.length > 0 && (
              <div className="mb-6">
                <h4 className="text-md font-medium text-gray-900 mb-3">CAPEX Items</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Year</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                        <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Amount</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {businessCase.capexItems.map((item, index) => (
                        <tr key={index}>
                          <td className="px-4 py-2 text-sm text-gray-900">{item.year}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">{item.description}</td>
                          <td className="px-4 py-2 text-sm text-gray-900 text-right">{formatCurrency(item.amount)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* OPEX Items */}
            {businessCase.opexItems && businessCase.opexItems.length > 0 && (
              <div className="mb-6">
                <h4 className="text-md font-medium text-gray-900 mb-3">OPEX Items</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Year</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                        <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Amount</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {businessCase.opexItems.map((item, index) => (
                        <tr key={index}>
                          <td className="px-4 py-2 text-sm text-gray-900">{item.year}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">{item.description}</td>
                          <td className="px-4 py-2 text-sm text-gray-900 text-right">{formatCurrency(item.amount)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Revenue Items */}
            {businessCase.revenueItems && businessCase.revenueItems.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">Revenue Items</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Year</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                        <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Amount</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {businessCase.revenueItems.map((item, index) => (
                        <tr key={index}>
                          <td className="px-4 py-2 text-sm text-gray-900">{item.year}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">{item.description}</td>
                          <td className="px-4 py-2 text-sm text-gray-900 text-right">{formatCurrency(item.amount)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 flex items-center justify-between border-t border-gray-200">
          <div className="text-sm text-gray-600">
            Business Case ID: {businessCase.id}
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={() => onExport(businessCase)}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              <i className="fas fa-download mr-2"></i>
              Export
            </button>
            <button
              onClick={() => onEdit(businessCase)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <i className="fas fa-edit mr-2"></i>
              Edit
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessCaseDetailModal;
