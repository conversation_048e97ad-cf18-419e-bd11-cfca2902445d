/* Request Management Main Container */
.request-management {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Header */
.request-management-header {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
}

.header-info p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.view-btn, .create-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.view-btn {
  background: #f3f4f6;
  color: #374151;
}

.view-btn:hover, .view-btn.active {
  background: #3b82f6;
  color: white;
}

.create-btn {
  background: #10b981;
  color: white;
}

.create-btn:hover {
  background: #059669;
}

/* Notification */
.notification {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

.notification.success {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.notification.error {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.notification.info {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #bfdbfe;
}

.notification button {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  margin-left: auto;
}

/* Content */
.request-management-content {
  position: relative;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-height: 600px;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Request Form Styles */
.request-form {
  padding: 24px;
}

.form-header {
  margin-bottom: 32px;
  text-align: center;
}

.form-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.form-header p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

/* Section Navigation */
.section-navigation {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 32px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.section-nav-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
}

.section-nav-btn:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.section-nav-btn.active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.section-nav-btn i {
  font-size: 18px;
  color: #6b7280;
  min-width: 18px;
}

.section-nav-btn.active i {
  color: #3b82f6;
}

.section-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.section-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.section-description {
  font-size: 12px;
  color: #6b7280;
}

/* Form Sections */
.form-section {
  margin-bottom: 32px;
}

.form-section h3 {
  margin: 0 0 24px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  padding-bottom: 12px;
  border-bottom: 2px solid #e5e7eb;
}

.form-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

/* Form Grid */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #ef4444;
}

.error-message {
  font-size: 12px;
  color: #ef4444;
  margin-top: 4px;
}

/* Checkbox Grid */
.checkbox-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

/* Score Input */
.score-input {
  display: flex;
  align-items: center;
  gap: 12px;
}

.score-input input[type="range"] {
  flex: 1;
}

.score-value {
  font-weight: 600;
  color: #3b82f6;
  min-width: 20px;
}

/* Priority Calculation */
.priority-calculation {
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.calculation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.calc-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.calc-item.total {
  font-weight: 600;
  background: #eff6ff;
  border-color: #3b82f6;
}

/* Dependency Summary */
.dependency-summary {
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.summary-item i {
  font-size: 18px;
}

.summary-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.summary-status {
  font-size: 12px;
  color: #6b7280;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
  margin-top: 32px;
}

.cancel-btn, .submit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.submit-btn {
  background: #3b82f6;
  color: white;
}

.submit-btn:hover:not(:disabled) {
  background: #2563eb;
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.submit-error {
  margin-top: 16px;
  text-align: center;
}

/* Utility Classes */
.text-blue-500 { color: #3b82f6; }
.text-green-500 { color: #10b981; }
.text-purple-500 { color: #8b5cf6; }
.text-orange-500 { color: #f59e0b; }
.text-red-500 { color: #ef4444; }

/* Request List Styles */
.request-list {
  padding: 24px;
}

.list-filters {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.filter-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 16px;
  align-items: center;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-info h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.selected-count {
  font-size: 14px;
  color: #6b7280;
  margin-left: 8px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.bulk-action-btn {
  padding: 8px 16px;
  background: #f59e0b;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

/* Requests Table */
.requests-table-container {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.requests-table {
  width: 100%;
  border-collapse: collapse;
}

.requests-table th,
.requests-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.requests-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.requests-table th.sortable {
  cursor: pointer;
  user-select: none;
}

.requests-table th.sortable:hover {
  background: #f3f4f6;
}

.requests-table tr:hover {
  background: #f9fafb;
}

.requests-table tr.selected {
  background: #eff6ff;
}

.request-title .title {
  display: block;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.request-title .description {
  display: block;
  font-size: 12px;
  color: #6b7280;
}

.status-badge, .priority-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-open { background: #fef3c7; color: #92400e; }
.status-review { background: #dbeafe; color: #1e40af; }
.status-approved { background: #dcfce7; color: #166534; }
.status-progress { background: #e0e7ff; color: #3730a3; }
.status-completed { background: #dcfce7; color: #166534; }
.status-rejected { background: #fee2e2; color: #991b1b; }
.status-hold { background: #f3f4f6; color: #374151; }

.priority-critical { background: #fee2e2; color: #991b1b; }
.priority-high { background: #fed7aa; color: #9a3412; }
.priority-medium { background: #fef3c7; color: #92400e; }
.priority-low { background: #dcfce7; color: #166534; }

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.action-btn.edit {
  background: #dbeafe;
  color: #1e40af;
}

.action-btn.delete {
  background: #fee2e2;
  color: #991b1b;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d1d5db;
}

.empty-state h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #374151;
}

/* Request Stats Styles */
.request-stats {
  padding: 24px;
}

.stats-header {
  margin-bottom: 32px;
  text-align: center;
}

.stats-header h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.key-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.metric-icon i {
  font-size: 24px;
}

.metric-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}

.metric-label {
  display: block;
  font-size: 14px;
  color: #6b7280;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.chart-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-container h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.status-bar, .type-item {
  margin-bottom: 12px;
}

.status-info, .type-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 14px;
}

.progress-bar, .type-bar {
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill, .type-fill {
  height: 100%;
  background: #3b82f6;
  transition: width 0.3s ease;
}

.priority-chart {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
}

.priority-item {
  text-align: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
}

.priority-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.priority-count {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.priority-percentage {
  font-size: 12px;
  color: #6b7280;
}

.validation-metrics, .recent-activity, .export-options {
  margin-bottom: 32px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.validation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.validation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.validation-label {
  font-size: 14px;
  color: #374151;
}

.validation-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-left: auto;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.activity-icon i {
  font-size: 16px;
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.activity-title {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.activity-meta {
  font-size: 12px;
  color: #6b7280;
}

.activity-time {
  font-size: 12px;
  color: #9ca3af;
}

.activity-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  align-self: flex-start;
}

.export-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.export-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

.export-btn:hover {
  background: #2563eb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .request-management {
    padding: 12px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .section-navigation {
    flex-direction: column;
  }

  .calculation-grid,
  .summary-grid {
    grid-template-columns: 1fr;
  }

  .filter-row {
    grid-template-columns: 1fr;
  }

  .key-metrics {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .requests-table-container {
    overflow-x: auto;
  }
}
