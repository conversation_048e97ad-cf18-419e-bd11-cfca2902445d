import React, { useState, useMemo } from 'react';
import './RelationshipTree.css';

const RelationshipTree = ({ data }) => {
  const [expandedNodes, setExpandedNodes] = useState(new Set());
  const [selectedNode, setSelectedNode] = useState(null);

  // Build hierarchical tree structure
  const treeData = useMemo(() => {
    if (!data || !data.nodes || !data.edges) {
      return [];
    }

    // Create a map of nodes by ID
    const nodeMap = new Map();
    data.nodes.forEach(node => {
      nodeMap.set(node.id, { ...node, children: [] });
    });

    // Create a map of edges by source
    const edgeMap = new Map();
    data.edges.forEach(edge => {
      if (!edgeMap.has(edge.source)) {
        edgeMap.set(edge.source, []);
      }
      edgeMap.get(edge.source).push(edge.target);
    });

    // Build tree structure by levels
    const levels = {
      0: [], // Ideas
      1: [], // Business Cases
      2: [], // Projects
      3: [], // Programs
      4: []  // Master Business Cases
    };

    // Group nodes by level
    data.nodes.forEach(node => {
      if (levels[node.level] !== undefined) {
        levels[node.level].push(nodeMap.get(node.id));
      }
    });

    // Connect children to parents
    data.edges.forEach(edge => {
      const parent = nodeMap.get(edge.source);
      const child = nodeMap.get(edge.target);
      if (parent && child) {
        parent.children.push(child);
      }
    });

    // Return root nodes (Ideas and orphaned nodes)
    const rootNodes = [];
    
    // Add Ideas as root nodes
    levels[0].forEach(node => {
      rootNodes.push(node);
    });

    // Add orphaned nodes from other levels
    Object.values(levels).flat().forEach(node => {
      const hasParent = data.edges.some(edge => edge.target === node.id);
      if (!hasParent && node.level > 0) {
        rootNodes.push(node);
      }
    });

    return rootNodes;
  }, [data]);

  const toggleNode = (nodeId) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const selectNode = (node) => {
    setSelectedNode(selectedNode?.id === node.id ? null : node);
  };

  const getNodeIcon = (type) => {
    const icons = {
      idea: 'fas fa-lightbulb',
      businessCase: 'fas fa-briefcase',
      project: 'fas fa-project-diagram',
      program: 'fas fa-layer-group',
      masterBusinessCase: 'fas fa-crown'
    };
    return icons[type] || 'fas fa-circle';
  };

  const getNodeColor = (type) => {
    const colors = {
      idea: '#fbbf24',
      businessCase: '#3b82f6',
      project: '#10b981',
      program: '#8b5cf6',
      masterBusinessCase: '#f59e0b'
    };
    return colors[type] || '#6b7280';
  };

  const getNodeTypeLabel = (type) => {
    const labels = {
      idea: 'Idea',
      businessCase: 'Business Case',
      project: 'Project',
      program: 'Program',
      masterBusinessCase: 'Master Business Case'
    };
    return labels[type] || type;
  };

  const renderNode = (node, depth = 0) => {
    const isExpanded = expandedNodes.has(node.id);
    const hasChildren = node.children && node.children.length > 0;
    const isSelected = selectedNode?.id === node.id;

    return (
      <div key={node.id} className="tree-node-container">
        <div 
          className={`tree-node ${isSelected ? 'selected' : ''}`}
          style={{ marginLeft: `${depth * 24}px` }}
          onClick={() => selectNode(node)}
        >
          <div className="node-expand">
            {hasChildren ? (
              <button
                className={`expand-button ${isExpanded ? 'expanded' : ''}`}
                onClick={(e) => {
                  e.stopPropagation();
                  toggleNode(node.id);
                }}
              >
                <i className="fas fa-chevron-right"></i>
              </button>
            ) : (
              <div className="expand-placeholder"></div>
            )}
          </div>

          <div 
            className="node-icon"
            style={{ backgroundColor: getNodeColor(node.type) }}
          >
            <i className={getNodeIcon(node.type)}></i>
          </div>

          <div className="node-content">
            <div className="node-title">{node.label}</div>
            <div className="node-type">{getNodeTypeLabel(node.type)}</div>
          </div>

          <div className="node-actions">
            {hasChildren && (
              <span className="children-count">
                {node.children.length} connected
              </span>
            )}
          </div>
        </div>

        {hasChildren && isExpanded && (
          <div className="tree-children">
            {node.children.map(child => renderNode(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  if (!data || !data.nodes || data.nodes.length === 0) {
    return (
      <div className="relationship-tree">
        <div className="empty-state">
          <i className="fas fa-sitemap"></i>
          <h3>No Relationship Data</h3>
          <p>No entities or relationships found to display in tree view.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relationship-tree">
      <div className="tree-header">
        <div className="tree-title">
          <h3>Entity Relationship Tree</h3>
          <p>Hierarchical view of entity relationships</p>
        </div>
        <div className="tree-controls">
          <button
            className="btn btn-outline btn-sm"
            onClick={() => setExpandedNodes(new Set(data.nodes.map(n => n.id)))}
          >
            <i className="fas fa-expand-arrows-alt"></i>
            Expand All
          </button>
          <button
            className="btn btn-outline btn-sm"
            onClick={() => setExpandedNodes(new Set())}
          >
            <i className="fas fa-compress-arrows-alt"></i>
            Collapse All
          </button>
        </div>
      </div>

      <div className="tree-content">
        <div className="tree-structure">
          {treeData.length > 0 ? (
            treeData.map(node => renderNode(node))
          ) : (
            <div className="no-root-nodes">
              <p>No root entities found. All entities may be interconnected.</p>
            </div>
          )}
        </div>

        {selectedNode && (
          <div className="node-details">
            <div className="details-header">
              <div 
                className="details-icon"
                style={{ backgroundColor: getNodeColor(selectedNode.type) }}
              >
                <i className={getNodeIcon(selectedNode.type)}></i>
              </div>
              <div className="details-title">
                <h4>{selectedNode.label}</h4>
                <p>{getNodeTypeLabel(selectedNode.type)}</p>
              </div>
              <button
                className="close-details"
                onClick={() => setSelectedNode(null)}
              >
                <i className="fas fa-times"></i>
              </button>
            </div>

            <div className="details-content">
              <div className="detail-section">
                <h5>Entity Information</h5>
                <div className="detail-grid">
                  <div className="detail-item">
                    <label>ID:</label>
                    <span>{selectedNode.id}</span>
                  </div>
                  <div className="detail-item">
                    <label>Type:</label>
                    <span>{getNodeTypeLabel(selectedNode.type)}</span>
                  </div>
                  <div className="detail-item">
                    <label>Level:</label>
                    <span>{selectedNode.level}</span>
                  </div>
                  <div className="detail-item">
                    <label>Children:</label>
                    <span>{selectedNode.children?.length || 0}</span>
                  </div>
                </div>
              </div>

              {selectedNode.data && (
                <div className="detail-section">
                  <h5>Additional Details</h5>
                  <div className="detail-grid">
                    {selectedNode.data.description && (
                      <div className="detail-item full-width">
                        <label>Description:</label>
                        <span>{selectedNode.data.description}</span>
                      </div>
                    )}
                    {selectedNode.data.status && (
                      <div className="detail-item">
                        <label>Status:</label>
                        <span className={`status-badge status-${selectedNode.data.status.toLowerCase()}`}>
                          {selectedNode.data.status}
                        </span>
                      </div>
                    )}
                    {selectedNode.data.priority && (
                      <div className="detail-item">
                        <label>Priority:</label>
                        <span className={`priority-badge priority-${selectedNode.data.priority.toLowerCase()}`}>
                          {selectedNode.data.priority}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {selectedNode.children && selectedNode.children.length > 0 && (
                <div className="detail-section">
                  <h5>Connected Entities ({selectedNode.children.length})</h5>
                  <div className="connected-entities">
                    {selectedNode.children.map(child => (
                      <div key={child.id} className="connected-entity">
                        <div 
                          className="connected-icon"
                          style={{ backgroundColor: getNodeColor(child.type) }}
                        >
                          <i className={getNodeIcon(child.type)}></i>
                        </div>
                        <div className="connected-info">
                          <div className="connected-name">{child.label}</div>
                          <div className="connected-type">{getNodeTypeLabel(child.type)}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RelationshipTree;
