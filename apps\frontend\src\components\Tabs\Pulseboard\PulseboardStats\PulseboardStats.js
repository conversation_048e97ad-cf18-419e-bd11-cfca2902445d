import React, { useMemo } from 'react';
import './PulseboardStats.css';

const PulseboardStats = ({ 
  items, 
  projects, 
  programs, 
  businessCases, 
  masterBusinessCases, 
  epics 
}) => {
  // Calculate comprehensive statistics
  const stats = useMemo(() => {
    const totalItems = items.length;
    
    // Status distribution
    const statusCounts = items.reduce((acc, item) => {
      acc[item.status] = (acc[item.status] || 0) + 1;
      return acc;
    }, {});

    // Priority distribution
    const priorityCounts = items.reduce((acc, item) => {
      acc[item.priority] = (acc[item.priority] || 0) + 1;
      return acc;
    }, {});

    // Type distribution
    const typeCounts = items.reduce((acc, item) => {
      acc[item.type] = (acc[item.type] || 0) + 1;
      return acc;
    }, {});

    // Business Unit distribution
    const businessUnitCounts = items.reduce((acc, item) => {
      if (item.businessUnit) {
        acc[item.businessUnit] = (acc[item.businessUnit] || 0) + 1;
      }
      return acc;
    }, {});

    // Linking statistics
    const linkedToProjects = items.filter(item => item.linkedProjectId).length;
    const linkedToEpics = items.filter(item => item.linkedEpicId).length;
    const linkedToPrograms = items.filter(item => item.linkedProgramId).length;
    const linkedToBusinessCases = items.filter(item => item.linkedBusinessCaseId).length;
    const linkedToMasterBC = items.filter(item => item.linkedMasterBusinessCaseId).length;

    // Progress statistics
    const avgProgress = items.length > 0 
      ? items.reduce((sum, item) => sum + (item.progress || 0), 0) / items.length 
      : 0;

    // Effort statistics
    const totalEffort = items.reduce((sum, item) => sum + (parseFloat(item.effort) || 0), 0);
    const avgEffort = items.length > 0 ? totalEffort / items.length : 0;

    // Cost statistics
    const totalCost = items.reduce((sum, item) => sum + (parseFloat(item.estimatedCost) || 0), 0);
    const avgCost = items.length > 0 ? totalCost / items.length : 0;

    // Recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentItems = items.filter(item => 
      new Date(item.createdAt) > thirtyDaysAgo
    ).length;

    return {
      totalItems,
      statusCounts,
      priorityCounts,
      typeCounts,
      businessUnitCounts,
      linkedToProjects,
      linkedToEpics,
      linkedToPrograms,
      linkedToBusinessCases,
      linkedToMasterBC,
      avgProgress: Math.round(avgProgress),
      totalEffort,
      avgEffort: Math.round(avgEffort),
      totalCost,
      avgCost: Math.round(avgCost),
      recentItems
    };
  }, [items]);

  const renderMetricCard = (title, value, icon, color = 'blue', subtitle = null) => (
    <div className={`metric-card ${color}`}>
      <div className="metric-header">
        <div className="metric-icon">
          <i className={icon}></i>
        </div>
        <div className="metric-info">
          <h3>{title}</h3>
          <div className="metric-value">{value}</div>
          {subtitle && <div className="metric-subtitle">{subtitle}</div>}
        </div>
      </div>
    </div>
  );

  const renderDistributionChart = (title, data, colorMap) => (
    <div className="distribution-chart">
      <h4>{title}</h4>
      <div className="chart-items">
        {Object.entries(data).map(([key, value]) => (
          <div key={key} className="chart-item">
            <div className="chart-label">
              <span className={`chart-indicator ${colorMap[key] || 'default'}`}></span>
              <span className="label-text">{key}</span>
            </div>
            <div className="chart-value">{value}</div>
            <div className="chart-bar">
              <div 
                className={`chart-fill ${colorMap[key] || 'default'}`}
                style={{ width: `${(value / stats.totalItems) * 100}%` }}
              ></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const statusColorMap = {
    'new': 'gray',
    'in-progress': 'blue',
    'review': 'yellow',
    'approved': 'green',
    'rejected': 'red',
    'completed': 'purple',
    'on-hold': 'orange',
    'cancelled': 'red'
  };

  const priorityColorMap = {
    'critical': 'red',
    'high': 'orange',
    'medium': 'yellow',
    'low': 'green'
  };

  const typeColorMap = {
    'feature': 'blue',
    'enhancement': 'green',
    'bug': 'red',
    'change': 'purple',
    'support': 'yellow',
    'research': 'indigo',
    'maintenance': 'gray',
    'integration': 'teal'
  };

  return (
    <div className="pulseboard-stats">
      <div className="stats-header">
        <h2>📊 Pulseboard Analytics</h2>
        <p>Comprehensive insights and metrics for your pulseboard items</p>
      </div>

      {/* Key Metrics */}
      <div className="metrics-grid">
        {renderMetricCard('Total Items', stats.totalItems, 'fas fa-list', 'blue')}
        {renderMetricCard('Recent Activity', stats.recentItems, 'fas fa-clock', 'green', 'Last 30 days')}
        {renderMetricCard('Avg Progress', `${stats.avgProgress}%`, 'fas fa-chart-line', 'purple')}
        {renderMetricCard('Total Effort', `${stats.totalEffort}h`, 'fas fa-stopwatch', 'orange')}
        {renderMetricCard('Total Cost', `$${stats.totalCost.toLocaleString()}`, 'fas fa-dollar-sign', 'green')}
        {renderMetricCard('Avg Cost', `$${stats.avgCost.toLocaleString()}`, 'fas fa-calculator', 'teal')}
      </div>

      {/* Linking Statistics */}
      <div className="linking-stats">
        <h3>🔗 Real Data Linking Statistics</h3>
        <div className="linking-grid">
          {renderMetricCard('Linked to Projects', stats.linkedToProjects, 'fas fa-project-diagram', 'blue')}
          {renderMetricCard('Linked to Epics', stats.linkedToEpics, 'fas fa-layer-group', 'indigo')}
          {renderMetricCard('Linked to Programs', stats.linkedToPrograms, 'fas fa-sitemap', 'purple')}
          {renderMetricCard('Linked to Business Cases', stats.linkedToBusinessCases, 'fas fa-briefcase', 'green')}
          {renderMetricCard('Linked to Master BC', stats.linkedToMasterBC, 'fas fa-crown', 'yellow')}
        </div>
      </div>

      {/* Distribution Charts */}
      <div className="charts-grid">
        <div className="chart-section">
          {renderDistributionChart('Status Distribution', stats.statusCounts, statusColorMap)}
        </div>
        
        <div className="chart-section">
          {renderDistributionChart('Priority Distribution', stats.priorityCounts, priorityColorMap)}
        </div>
        
        <div className="chart-section">
          {renderDistributionChart('Type Distribution', stats.typeCounts, typeColorMap)}
        </div>
        
        {Object.keys(stats.businessUnitCounts).length > 0 && (
          <div className="chart-section">
            {renderDistributionChart('Business Unit Distribution', stats.businessUnitCounts, {})}
          </div>
        )}
      </div>

      {/* Summary Insights */}
      <div className="insights-section">
        <h3>💡 Key Insights</h3>
        <div className="insights-grid">
          <div className="insight-card">
            <h4>Most Active Status</h4>
            <p>{Object.entries(stats.statusCounts).sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A'}</p>
          </div>
          
          <div className="insight-card">
            <h4>Highest Priority</h4>
            <p>{Object.entries(stats.priorityCounts).sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A'}</p>
          </div>
          
          <div className="insight-card">
            <h4>Most Common Type</h4>
            <p>{Object.entries(stats.typeCounts).sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A'}</p>
          </div>
          
          <div className="insight-card">
            <h4>Linking Rate</h4>
            <p>{stats.totalItems > 0 ? Math.round(((stats.linkedToProjects + stats.linkedToEpics + stats.linkedToPrograms + stats.linkedToBusinessCases + stats.linkedToMasterBC) / (stats.totalItems * 5)) * 100) : 0}%</p>
          </div>
        </div>
      </div>

      {/* Export Options */}
      <div className="export-section">
        <h3>📤 Export Options</h3>
        <div className="export-buttons">
          <button className="export-btn excel">
            <i className="fas fa-file-excel"></i>
            Export to Excel
          </button>
          <button className="export-btn pdf">
            <i className="fas fa-file-pdf"></i>
            Export to PDF
          </button>
          <button className="export-btn dashboard">
            <i className="fas fa-chart-bar"></i>
            Dashboard Report
          </button>
        </div>
      </div>
    </div>
  );
};

export default PulseboardStats;
