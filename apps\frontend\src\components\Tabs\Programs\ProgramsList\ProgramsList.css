.programs-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Header */
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.header-info h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.header-info p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

/* Filters */
.filters-section {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.filters-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 16px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.filter-input,
.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Summary Statistics */
.summary-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1px;
  background: #e5e7eb;
  margin: 0;
}

.stat-card {
  background: white;
  padding: 16px;
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

/* Table */
.table-container {
  overflow-x: auto;
}

.programs-table {
  width: 100%;
  border-collapse: collapse;
}

.programs-table th {
  background: #f9fafb;
  padding: 12px 16px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  white-space: nowrap;
}

.programs-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
}

.programs-table th.sortable:hover {
  background: #f3f4f6;
}

.programs-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: top;
}

.programs-table tr:hover {
  background: #f9fafb;
}

/* Name Cell */
.name-cell strong {
  display: block;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.description-preview {
  font-size: 12px;
  color: #6b7280;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Status and Priority Badges */
.status-badge,
.priority-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active {
  background: #d1fae5;
  color: #065f46;
}

.status-planning {
  background: #fef3c7;
  color: #92400e;
}

.status-on-hold {
  background: #fee2e2;
  color: #991b1b;
}

.status-completed {
  background: #dbeafe;
  color: #1e40af;
}

.status-cancelled {
  background: #f3f4f6;
  color: #374151;
}

.priority-high {
  background: #fee2e2;
  color: #991b1b;
}

.priority-medium {
  background: #fef3c7;
  color: #92400e;
}

.priority-low {
  background: #d1fae5;
  color: #065f46;
}

/* Master BC Link */
.master-bc-link {
  display: flex;
  align-items: center;
  gap: 8px;
}

.linked-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #dbeafe;
  color: #1e40af;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.link-btn {
  font-size: 11px;
  padding: 4px 8px;
  white-space: nowrap;
}

.unlink-btn {
  padding: 4px 6px;
  min-width: auto;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 4px;
}

.action-buttons .btn {
  padding: 6px 8px;
  min-width: auto;
}

/* Text Alignment */
.text-right {
  text-align: right;
}

/* Pagination */
.pagination-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.pagination-info {
  font-size: 14px;
  color: #6b7280;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.items-per-page {
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
}

.page-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-info {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-state i {
  font-size: 48px;
  color: #9ca3af;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #374151;
}

.empty-state p {
  margin: 0 0 20px 0;
  color: #6b7280;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-outline {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-outline:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

/* Connected relationships styling */
.connected-master-bc {
  min-width: 180px;
}

.master-bc-name {
  display: flex;
  align-items: center;
  font-weight: 500;
  margin-bottom: 2px;
}

.master-bc-unit {
  font-size: 0.75rem;
  color: #6b7280;
}

.connected-projects {
  min-width: 200px;
}

.project-item {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
  font-size: 0.875rem;
}

.connected-business-cases {
  min-width: 220px;
}

.bc-item {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
  font-size: 0.875rem;
}

.text-blue-500 {
  color: #3b82f6;
}

.text-green-500 {
  color: #10b981;
}

.text-purple-500 {
  color: #8b5cf6;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-500 {
  color: #6b7280;
}

.ml-1 {
  margin-left: 0.25rem;
}

.text-xs {
  font-size: 0.75rem;
}

.text-sm {
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .filters-row {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }
  
  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .list-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .filters-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .summary-stats {
    grid-template-columns: 1fr;
  }
  
  .pagination-section {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .pagination-controls {
    justify-content: center;
  }
  
  .programs-table {
    font-size: 12px;
  }
  
  .programs-table th,
  .programs-table td {
    padding: 8px 12px;
  }
  
  .description-preview {
    max-width: 120px;
  }
}
