/**
 * Database Adapter - Hybrid Approach
 * Supports both JSON files (development) and MongoDB (production)
 */

const fs = require('fs');
const path = require('path');

class DatabaseAdapter {
    constructor(config = {}) {
        this.mode = config.mode || process.env.DB_MODE || 'json'; // 'json' or 'mongodb'
        this.mongoClient = null;
        this.mongoDb = null;
        this.dataDir = path.join(__dirname, 'data');
        
        // Collection definitions
        this.collections = {
            businessCases: 'businessCases',
            masterBusinessCases: 'masterBusinessCases',
            projects: 'projects',
            programs: 'programs',
            ideas: 'ideas',
            users: 'users',
            feedback: 'feedback',
            milestones: 'milestones',
            auditLog: 'auditLog'
        };

        console.log(`🔧 Database Adapter initialized in ${this.mode.toUpperCase()} mode`);
        
        if (this.mode === 'mongodb') {
            this.initializeMongoDB(config);
        } else {
            this.initializeJSONStorage();
        }
    }

    /**
     * Initialize MongoDB connection
     */
    async initializeMongoDB(config) {
        try {
            const { MongoClient } = require('mongodb');
            
            const mongoUrl = config.mongoUrl || process.env.MONGO_URL || 'mongodb://localhost:27017';
            const dbName = config.dbName || process.env.MONGO_DB_NAME || 'master_business_case';
            
            this.mongoClient = new MongoClient(mongoUrl);
            await this.mongoClient.connect();
            this.mongoDb = this.mongoClient.db(dbName);
            
            console.log('✅ MongoDB connected successfully');
            
            // Create indexes for better performance
            await this.createIndexes();
            
        } catch (error) {
            console.error('❌ MongoDB connection failed:', error.message);
            console.log('🔄 Falling back to JSON file storage');
            this.mode = 'json';
            this.initializeJSONStorage();
        }
    }

    /**
     * Initialize JSON file storage
     */
    initializeJSONStorage() {
        // Ensure data directory exists
        if (!fs.existsSync(this.dataDir)) {
            fs.mkdirSync(this.dataDir, { recursive: true });
        }

        // Initialize empty collections if they don't exist
        Object.values(this.collections).forEach(collection => {
            const filePath = path.join(this.dataDir, `${collection}.json`);
            if (!fs.existsSync(filePath)) {
                fs.writeFileSync(filePath, JSON.stringify([], null, 2));
                console.log(`📄 Created ${collection}.json`);
            }
        });

        console.log('✅ JSON file storage initialized');
    }

    /**
     * Create MongoDB indexes for performance
     */
    async createIndexes() {
        try {
            // Business Cases indexes
            await this.mongoDb.collection('businessCases').createIndex({ "_id": 1 });
            await this.mongoDb.collection('businessCases').createIndex({ "businessUnit": 1 });
            await this.mongoDb.collection('businessCases').createIndex({ "createdAt": -1 });
            
            // Master Business Cases indexes
            await this.mongoDb.collection('masterBusinessCases').createIndex({ "_id": 1 });
            await this.mongoDb.collection('masterBusinessCases').createIndex({ "linkedBusinessCases": 1 });
            
            // Projects indexes
            await this.mongoDb.collection('projects').createIndex({ "_id": 1 });
            await this.mongoDb.collection('projects').createIndex({ "type": 1 });
            
            // Programs indexes
            await this.mongoDb.collection('programs').createIndex({ "_id": 1 });
            await this.mongoDb.collection('programs').createIndex({ "linkedMasterBC": 1 });
            
            // Ideas indexes
            await this.mongoDb.collection('ideas').createIndex({ "_id": 1 });
            await this.mongoDb.collection('ideas').createIndex({ "status": 1 });
            
            // Users indexes
            await this.mongoDb.collection('users').createIndex({ "email": 1 }, { unique: true });
            
            console.log('✅ MongoDB indexes created');
        } catch (error) {
            console.warn('⚠️ Index creation warning:', error.message);
        }
    }

    /**
     * Read collection data
     */
    async readCollection(collectionName) {
        try {
            if (this.mode === 'mongodb') {
                const collection = this.mongoDb.collection(collectionName);
                const documents = await collection.find({}).toArray();
                return documents;
            } else {
                // JSON file mode
                const filePath = path.join(this.dataDir, `${collectionName}.json`);
                if (fs.existsSync(filePath)) {
                    const data = fs.readFileSync(filePath, 'utf8');
                    return JSON.parse(data);
                }
                return [];
            }
        } catch (error) {
            console.error(`❌ Error reading collection ${collectionName}:`, error.message);
            return [];
        }
    }

    /**
     * Write collection data
     */
    async writeCollection(collectionName, data, userId = 'system') {
        try {
            if (this.mode === 'mongodb') {
                const collection = this.mongoDb.collection(collectionName);
                
                // Clear existing data and insert new data
                await collection.deleteMany({});
                if (Array.isArray(data) && data.length > 0) {
                    await collection.insertMany(data);
                }
                
                // Log operation
                await this.logOperation('WRITE', collectionName, Array.isArray(data) ? data.length : 1, userId);
                
            } else {
                // JSON file mode
                const filePath = path.join(this.dataDir, `${collectionName}.json`);
                fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
                
                // Log operation
                this.logOperation('WRITE', collectionName, Array.isArray(data) ? data.length : 1, userId);
            }
            
            return true;
        } catch (error) {
            console.error(`❌ Error writing collection ${collectionName}:`, error.message);
            return false;
        }
    }

    /**
     * Insert single document
     */
    async insertDocument(collectionName, document) {
        try {
            if (this.mode === 'mongodb') {
                const collection = this.mongoDb.collection(collectionName);
                const result = await collection.insertOne(document);
                return result.insertedId;
            } else {
                // JSON file mode
                const data = await this.readCollection(collectionName);
                data.push(document);
                await this.writeCollection(collectionName, data);
                return document._id;
            }
        } catch (error) {
            console.error(`❌ Error inserting document in ${collectionName}:`, error.message);
            return null;
        }
    }

    /**
     * Update single document
     */
    async updateDocument(collectionName, filter, update) {
        try {
            if (this.mode === 'mongodb') {
                const collection = this.mongoDb.collection(collectionName);
                const result = await collection.updateOne(filter, { $set: update });
                return result.modifiedCount > 0;
            } else {
                // JSON file mode
                const data = await this.readCollection(collectionName);
                const index = data.findIndex(item => {
                    return Object.keys(filter).every(key => item[key] === filter[key]);
                });
                
                if (index !== -1) {
                    data[index] = { ...data[index], ...update };
                    await this.writeCollection(collectionName, data);
                    return true;
                }
                return false;
            }
        } catch (error) {
            console.error(`❌ Error updating document in ${collectionName}:`, error.message);
            return false;
        }
    }

    /**
     * Delete document
     */
    async deleteDocument(collectionName, filter) {
        try {
            if (this.mode === 'mongodb') {
                const collection = this.mongoDb.collection(collectionName);
                const result = await collection.deleteOne(filter);
                return result.deletedCount > 0;
            } else {
                // JSON file mode
                const data = await this.readCollection(collectionName);
                const index = data.findIndex(item => {
                    return Object.keys(filter).every(key => item[key] === filter[key]);
                });
                
                if (index !== -1) {
                    data.splice(index, 1);
                    await this.writeCollection(collectionName, data);
                    return true;
                }
                return false;
            }
        } catch (error) {
            console.error(`❌ Error deleting document in ${collectionName}:`, error.message);
            return false;
        }
    }

    /**
     * Find documents with query
     */
    async findDocuments(collectionName, query = {}, options = {}) {
        try {
            if (this.mode === 'mongodb') {
                const collection = this.mongoDb.collection(collectionName);
                let cursor = collection.find(query);
                
                if (options.sort) cursor = cursor.sort(options.sort);
                if (options.limit) cursor = cursor.limit(options.limit);
                if (options.skip) cursor = cursor.skip(options.skip);
                
                return await cursor.toArray();
            } else {
                // JSON file mode - basic filtering
                let data = await this.readCollection(collectionName);
                
                // Apply basic query filtering
                if (Object.keys(query).length > 0) {
                    data = data.filter(item => {
                        return Object.keys(query).every(key => {
                            if (typeof query[key] === 'object' && query[key].$regex) {
                                const regex = new RegExp(query[key].$regex, query[key].$options || '');
                                return regex.test(item[key]);
                            }
                            return item[key] === query[key];
                        });
                    });
                }
                
                // Apply sorting
                if (options.sort) {
                    const sortKey = Object.keys(options.sort)[0];
                    const sortOrder = options.sort[sortKey];
                    data.sort((a, b) => {
                        if (sortOrder === 1) return a[sortKey] > b[sortKey] ? 1 : -1;
                        return a[sortKey] < b[sortKey] ? 1 : -1;
                    });
                }
                
                // Apply pagination
                if (options.skip) data = data.slice(options.skip);
                if (options.limit) data = data.slice(0, options.limit);
                
                return data;
            }
        } catch (error) {
            console.error(`❌ Error finding documents in ${collectionName}:`, error.message);
            return [];
        }
    }

    /**
     * Log database operations
     */
    async logOperation(operation, collection, count, userId) {
        const logEntry = {
            _id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date().toISOString(),
            operation,
            collection,
            count,
            userId,
            mode: this.mode
        };

        try {
            if (this.mode === 'mongodb') {
                await this.mongoDb.collection('auditLog').insertOne(logEntry);
            } else {
                const auditLog = await this.readCollection('auditLog');
                auditLog.push(logEntry);
                // Keep only last 1000 entries in JSON mode
                if (auditLog.length > 1000) {
                    auditLog.splice(0, auditLog.length - 1000);
                }
                fs.writeFileSync(path.join(this.dataDir, 'auditLog.json'), JSON.stringify(auditLog, null, 2));
            }
        } catch (error) {
            console.warn('⚠️ Audit log warning:', error.message);
        }
    }

    /**
     * Get system statistics
     */
    async getSystemStats() {
        const stats = {
            mode: this.mode,
            timestamp: new Date().toISOString(),
            collections: {}
        };

        try {
            for (const [key, collectionName] of Object.entries(this.collections)) {
                const data = await this.readCollection(collectionName);
                stats.collections[key] = {
                    count: Array.isArray(data) ? data.length : 0,
                    size: JSON.stringify(data).length
                };
            }
        } catch (error) {
            console.error('❌ Error getting system stats:', error.message);
        }

        return stats;
    }

    /**
     * Close database connection
     */
    async close() {
        if (this.mode === 'mongodb' && this.mongoClient) {
            await this.mongoClient.close();
            console.log('✅ MongoDB connection closed');
        }
    }

    /**
     * Health check
     */
    async healthCheck() {
        try {
            if (this.mode === 'mongodb') {
                await this.mongoDb.admin().ping();
                return { status: 'healthy', mode: 'mongodb' };
            } else {
                // Check if data directory is accessible
                fs.accessSync(this.dataDir, fs.constants.R_OK | fs.constants.W_OK);
                return { status: 'healthy', mode: 'json' };
            }
        } catch (error) {
            return { status: 'unhealthy', mode: this.mode, error: error.message };
        }
    }
}

module.exports = DatabaseAdapter;
