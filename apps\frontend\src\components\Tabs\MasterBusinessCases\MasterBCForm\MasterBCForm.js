import React, { useState } from 'react';
import './MasterBCForm.css';

const MasterBCForm = ({ onSave, onCancel, initialData = null }) => {
  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    description: initialData?.description || '',
    category: initialData?.category || '',
    businessUnits: initialData?.businessUnits || [],
    priority: initialData?.priority || 'Medium',
    ...initialData
  });

  const handleSubmit = (e) => {
    e.preventDefault();

    // Validation
    if (formData.businessUnits.length === 0) {
      alert('Please select at least one Business Unit');
      return;
    }

    onSave(formData);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleBusinessUnitChange = (e) => {
    const { value, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      businessUnits: checked
        ? [...prev.businessUnits, value]
        : prev.businessUnits.filter(unit => unit !== value)
    }));
  };

  return (
    <div className="master-bc-form">
      <div className="form-header">
        <h3>{initialData ? 'Edit Master Business Case' : 'Create Master Business Case'}</h3>
      </div>
      
      <form onSubmit={handleSubmit} className="form-content">
        <div className="form-group">
          <label htmlFor="name">Name *</label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
            placeholder="Enter Master BC name"
          />
        </div>

        <div className="form-group">
          <label htmlFor="description">Description</label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={4}
            placeholder="Enter description"
          />
        </div>

        <div className="form-group">
          <label htmlFor="category">Category *</label>
          <select
            id="category"
            name="category"
            value={formData.category}
            onChange={handleChange}
            required
          >
            <option value="">Select Category</option>
            <option value="Technology">Technology</option>
            <option value="Operations">Operations</option>
            <option value="Customer">Customer</option>
            <option value="Sustainability">Sustainability</option>
            <option value="Growth">Growth</option>
            <option value="Security">Security</option>
            <option value="Human Resources">Human Resources</option>
            <option value="Innovation">Innovation</option>
          </select>
        </div>

        <div className="form-group">
          <label>Business Units *</label>
          <div className="checkbox-group">
            {[
              'IT', 'Operations', 'Customer Service', 'Manufacturing', 'Supply Chain',
              'Marketing', 'Sales', 'Business Development', 'Facilities', 'Procurement',
              'Analytics', 'Business Intelligence', 'Security', 'Risk Management',
              'HR', 'Training', 'Development', 'R&D', 'Product Management', 'Engineering',
              'Logistics'
            ].map(unit => (
              <label key={unit} className="checkbox-label">
                <input
                  type="checkbox"
                  value={unit}
                  checked={formData.businessUnits.includes(unit)}
                  onChange={handleBusinessUnitChange}
                />
                <span className="checkbox-text">{unit}</span>
              </label>
            ))}
          </div>
          {formData.businessUnits.length === 0 && (
            <div className="field-error">Please select at least one Business Unit</div>
          )}
        </div>

        <div className="form-group">
          <label htmlFor="priority">Priority *</label>
          <select
            id="priority"
            name="priority"
            value={formData.priority}
            onChange={handleChange}
            required
          >
            <option value="High">High</option>
            <option value="Medium">Medium</option>
            <option value="Low">Low</option>
          </select>
        </div>

        <div className="form-actions">
          <button type="button" onClick={onCancel} className="btn btn-secondary">
            Cancel
          </button>
          <button type="submit" className="btn btn-primary">
            {initialData ? 'Update' : 'Create'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default MasterBCForm;
