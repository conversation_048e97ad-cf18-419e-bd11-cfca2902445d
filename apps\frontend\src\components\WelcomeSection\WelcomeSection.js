import React from 'react';

const WelcomeSection = ({ onLogin }) => {
  return (
    <div className="text-center py-12">
      <div className="max-w-3xl mx-auto">
        {/* Welcome Header */}
        <div className="mb-8">
          <i className="fas fa-briefcase text-blue-600 text-6xl mb-6"></i>
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Welcome to Business Case Management
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Streamline your business case development, financial modeling, and strategic decision-making process
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="text-blue-600 text-3xl mb-4">
              <i className="fas fa-chart-line"></i>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Financial Modeling
            </h3>
            <p className="text-gray-600">
              Create comprehensive financial models with CAPEX, OPEX, and revenue projections
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="text-green-600 text-3xl mb-4">
              <i className="fas fa-calculator"></i>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Auto-Calculations
            </h3>
            <p className="text-gray-600">
              Automatic calculation of IRR, NPV, Payback Period, and other key financial metrics
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="text-purple-600 text-3xl mb-4">
              <i className="fas fa-sitemap"></i>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Portfolio Management
            </h3>
            <p className="text-gray-600">
              Manage Master Business Cases with automatic aggregation and portfolio analysis
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="text-orange-600 text-3xl mb-4">
              <i className="fas fa-project-diagram"></i>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Project Integration
            </h3>
            <p className="text-gray-600">
              Link business cases to projects, epics, and programs for complete traceability
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="text-red-600 text-3xl mb-4">
              <i className="fas fa-file-excel"></i>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Excel Integration
            </h3>
            <p className="text-gray-600">
              Import/export data with Excel templates and maintain financial formulas
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="text-indigo-600 text-3xl mb-4">
              <i className="fas fa-chart-bar"></i>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Strategic Dashboard
            </h3>
            <p className="text-gray-600">
              Real-time insights and analytics for strategic decision making
            </p>
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg p-8">
          <h3 className="text-2xl font-bold mb-4">Ready to Get Started?</h3>
          <p className="text-blue-100 mb-6">
            Access the complete Business Case Management system with all features and capabilities
          </p>
          <button
            onClick={onLogin}
            className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            Login to Continue
          </button>
        </div>

        {/* Demo Credentials Info */}
        <div className="mt-8 bg-gray-50 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Demo Credentials</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="bg-white p-4 rounded border">
              <p className="font-medium text-gray-900">Quick Access:</p>
              <p className="text-gray-600">Username: <code className="bg-gray-100 px-2 py-1 rounded">test</code></p>
              <p className="text-gray-600">Password: <code className="bg-gray-100 px-2 py-1 rounded">test</code></p>
            </div>
            <div className="bg-white p-4 rounded border">
              <p className="font-medium text-gray-900">Admin Access:</p>
              <p className="text-gray-600">Username: <code className="bg-gray-100 px-2 py-1 rounded">admin</code></p>
              <p className="text-gray-600">Password: <code className="bg-gray-100 px-2 py-1 rounded">admin</code></p>
            </div>
          </div>
          <p className="text-xs text-blue-600 mt-4">
            💡 Tip: Use "test/test" for quick access to explore all features
          </p>
        </div>
      </div>
    </div>
  );
};



export default WelcomeSection;
