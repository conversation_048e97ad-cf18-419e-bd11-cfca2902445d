import React, { useState, useEffect } from 'react';

const RequestForm = ({ request, onSubmit, onCancel, isEditing = false }) => {
  const [formData, setFormData] = useState({
    // Request Details
    title: '',
    description: '',
    currentChallenge: '',
    expectedOutcome: '',
    requestType: '',
    category: '',
    priority: '',
    affectedApplications: [],
    affectedOrganizations: [],
    workarounds: '',
    dependencies: '',
    impactedPersonas: '',
    businessImpact: '',
    customerValue: '',
    deadline: '',
    supportingFiles: [],
    
    // Validation & Impact Assessment
    isRequestClear: false,
    isBusinessValueDefined: false,
    isStrategicallyAligned: false,
    areToolsListed: false,
    areStakeholdersInvolved: false,
    areDependenciesIdentified: false,
    hasSimilarRequest: false,
    similarRequestIds: '',
    initialEffortEstimation: '',
    
    // Change Management & Traceability
    changeType: '',
    impactedModules: [],
    changeOwner: '',
    relatedIssueIds: '',
    previousOccurrences: '',
    resolutionSummary: '',
    approvalComments: '',
    lessonsLearned: '',
    linkedDocuments: [],
    feedbackComments: '',
    
    // Ranking & Prioritization
    urgencyScore: 1,
    customerValueScore: 1,
    strategicAlignmentScore: 1,
    effortEstimation: '',
    finalPriorityRank: '',
    
    // Dependency Assessment
    applicationDependencies: '',
    thirdPartyIntegrations: '',
    apiDataFlowChanges: '',
    techStackImpact: '',
    dataModelImpact: '',
    deploymentScheduleImpact: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeSection, setActiveSection] = useState('request-details');

  // Form sections for step-by-step navigation
  const sections = [
    {
      id: 'request-details',
      name: 'Request Details',
      icon: 'fas fa-clipboard-list',
      description: 'Basic request information and requirements'
    },
    {
      id: 'validation-assessment',
      name: 'Validation & Impact',
      icon: 'fas fa-search',
      description: 'Validation checklist and impact assessment'
    },
    {
      id: 'change-management',
      name: 'Change Management',
      icon: 'fas fa-exchange-alt',
      description: 'Change tracking and traceability'
    },
    {
      id: 'ranking-prioritization',
      name: 'Ranking & Priority',
      icon: 'fas fa-sort-amount-up',
      description: 'Priority scoring and ranking'
    },
    {
      id: 'dependency-assessment',
      name: 'Dependencies',
      icon: 'fas fa-project-diagram',
      description: 'Technical and business dependencies'
    }
  ];

  // Populate form data when editing
  useEffect(() => {
    if (request && isEditing) {
      setFormData({
        title: request.title || '',
        description: request.description || '',
        currentChallenge: request.currentChallenge || '',
        expectedOutcome: request.expectedOutcome || '',
        requestType: request.requestType || '',
        category: request.category || '',
        priority: request.priority || '',
        affectedApplications: request.affectedApplications || [],
        affectedOrganizations: request.affectedOrganizations || [],
        workarounds: request.workarounds || '',
        dependencies: request.dependencies || '',
        impactedPersonas: request.impactedPersonas || '',
        businessImpact: request.businessImpact || '',
        customerValue: request.customerValue || '',
        deadline: request.deadline || '',
        supportingFiles: request.supportingFiles || [],
        
        isRequestClear: request.isRequestClear || false,
        isBusinessValueDefined: request.isBusinessValueDefined || false,
        isStrategicallyAligned: request.isStrategicallyAligned || false,
        areToolsListed: request.areToolsListed || false,
        areStakeholdersInvolved: request.areStakeholdersInvolved || false,
        areDependenciesIdentified: request.areDependenciesIdentified || false,
        hasSimilarRequest: request.hasSimilarRequest || false,
        similarRequestIds: request.similarRequestIds || '',
        initialEffortEstimation: request.initialEffortEstimation || '',
        
        changeType: request.changeType || '',
        impactedModules: request.impactedModules || [],
        changeOwner: request.changeOwner || '',
        relatedIssueIds: request.relatedIssueIds || '',
        previousOccurrences: request.previousOccurrences || '',
        resolutionSummary: request.resolutionSummary || '',
        approvalComments: request.approvalComments || '',
        lessonsLearned: request.lessonsLearned || '',
        linkedDocuments: request.linkedDocuments || [],
        feedbackComments: request.feedbackComments || '',
        
        urgencyScore: request.urgencyScore || 1,
        customerValueScore: request.customerValueScore || 1,
        strategicAlignmentScore: request.strategicAlignmentScore || 1,
        effortEstimation: request.effortEstimation || '',
        finalPriorityRank: request.finalPriorityRank || '',
        
        applicationDependencies: request.applicationDependencies || '',
        thirdPartyIntegrations: request.thirdPartyIntegrations || '',
        apiDataFlowChanges: request.apiDataFlowChanges || '',
        techStackImpact: request.techStackImpact || '',
        dataModelImpact: request.dataModelImpact || '',
        deploymentScheduleImpact: request.deploymentScheduleImpact || ''
      });
    }
  }, [request, isEditing]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleArrayInputChange = (name, value) => {
    const arrayValue = value.split(',').map(item => item.trim()).filter(item => item);
    setFormData(prev => ({
      ...prev,
      [name]: arrayValue
    }));
  };

  const validateForm = () => {
    const newErrors = {};
    
    // Required fields validation
    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }
    
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }
    
    if (!formData.requestType) {
      newErrors.requestType = 'Request type is required';
    }
    
    if (!formData.priority) {
      newErrors.priority = 'Priority is required';
    }
    
    if (!formData.businessImpact.trim()) {
      newErrors.businessImpact = 'Business impact is required';
    }
    
    if (!formData.customerValue.trim()) {
      newErrors.customerValue = 'Customer value is required';
    }

    // Validation checklist requirements
    if (!formData.isRequestClear) {
      newErrors.isRequestClear = 'Request clarity confirmation is required';
    }
    
    if (!formData.isBusinessValueDefined) {
      newErrors.isBusinessValueDefined = 'Business value definition confirmation is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const result = await onSubmit(formData);
      if (result.success) {
        // Form will be closed by parent component
      } else {
        setErrors({ submit: result.error });
      }
    } catch (error) {
      console.error('Error submitting request:', error);
      setErrors({ submit: 'An error occurred while submitting the request' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderSectionNavigation = () => (
    <div className="section-navigation">
      {sections.map(section => (
        <button
          key={section.id}
          type="button"
          className={`section-nav-btn ${activeSection === section.id ? 'active' : ''}`}
          onClick={() => setActiveSection(section.id)}
        >
          <i className={section.icon}></i>
          <div className="section-info">
            <span className="section-name">{section.name}</span>
            <span className="section-description">{section.description}</span>
          </div>
        </button>
      ))}
    </div>
  );

  const renderRequestDetails = () => (
    <div className="form-section">
      <h3>📋 Request Details</h3>
      <div className="form-grid">
        {/* Title */}
        <div className="form-group full-width">
          <label>Title *</label>
          <input
            type="text"
            name="title"
            value={formData.title}
            onChange={handleInputChange}
            className={errors.title ? 'error' : ''}
            placeholder="Enter a clear, descriptive title for your request"
          />
          {errors.title && <span className="error-message">{errors.title}</span>}
        </div>

        {/* Description */}
        <div className="form-group full-width">
          <label>Description *</label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            rows={4}
            className={errors.description ? 'error' : ''}
            placeholder="Provide a detailed description of the request"
          />
          {errors.description && <span className="error-message">{errors.description}</span>}
        </div>

        {/* Current Challenge */}
        <div className="form-group full-width">
          <label>Current Challenge</label>
          <textarea
            name="currentChallenge"
            value={formData.currentChallenge}
            onChange={handleInputChange}
            rows={3}
            placeholder="Describe the current challenge or problem"
          />
        </div>

        {/* Expected Outcome */}
        <div className="form-group full-width">
          <label>Expected Outcome</label>
          <textarea
            name="expectedOutcome"
            value={formData.expectedOutcome}
            onChange={handleInputChange}
            rows={3}
            placeholder="Describe the expected outcome or solution"
          />
        </div>

        {/* Request Type */}
        <div className="form-group">
          <label>Request Type *</label>
          <select
            name="requestType"
            value={formData.requestType}
            onChange={handleInputChange}
            className={errors.requestType ? 'error' : ''}
          >
            <option value="">Select request type</option>
            <option value="Enhancement">Enhancement</option>
            <option value="Bug Fix">Bug Fix</option>
            <option value="New Feature">New Feature</option>
            <option value="Integration">Integration</option>
            <option value="Performance">Performance</option>
            <option value="Security">Security</option>
            <option value="Maintenance">Maintenance</option>
          </select>
          {errors.requestType && <span className="error-message">{errors.requestType}</span>}
        </div>

        {/* Category */}
        <div className="form-group">
          <label>Category Tag</label>
          <select
            name="category"
            value={formData.category}
            onChange={handleInputChange}
          >
            <option value="">Select category</option>
            <option value="UI/UX">UI/UX</option>
            <option value="Backend">Backend</option>
            <option value="Database">Database</option>
            <option value="API">API</option>
            <option value="Analytics">Analytics</option>
            <option value="Reporting">Reporting</option>
            <option value="Integration">Integration</option>
            <option value="Security">Security</option>
            <option value="Performance">Performance</option>
          </select>
        </div>

        {/* Priority */}
        <div className="form-group">
          <label>Priority *</label>
          <select
            name="priority"
            value={formData.priority}
            onChange={handleInputChange}
            className={errors.priority ? 'error' : ''}
          >
            <option value="">Select priority</option>
            <option value="Critical">Critical</option>
            <option value="High">High</option>
            <option value="Medium">Medium</option>
            <option value="Low">Low</option>
          </select>
          {errors.priority && <span className="error-message">{errors.priority}</span>}
        </div>

        {/* Deadline */}
        <div className="form-group">
          <label>Deadline</label>
          <input
            type="date"
            name="deadline"
            value={formData.deadline}
            onChange={handleInputChange}
          />
        </div>
      </div>
    </div>
  );

  const renderValidationAssessment = () => (
    <div className="form-section">
      <h3>🔍 Validation & Impact Assessment</h3>
      <div className="form-grid">
        {/* Validation Checklist */}
        <div className="form-group full-width">
          <h4>Validation Checklist</h4>
          <div className="checkbox-grid">
            <label className="checkbox-item">
              <input
                type="checkbox"
                name="isRequestClear"
                checked={formData.isRequestClear}
                onChange={handleInputChange}
              />
              <span>Is the request clearly described? *</span>
            </label>
            {errors.isRequestClear && <span className="error-message">{errors.isRequestClear}</span>}

            <label className="checkbox-item">
              <input
                type="checkbox"
                name="isBusinessValueDefined"
                checked={formData.isBusinessValueDefined}
                onChange={handleInputChange}
              />
              <span>Is business value defined? *</span>
            </label>
            {errors.isBusinessValueDefined && <span className="error-message">{errors.isBusinessValueDefined}</span>}

            <label className="checkbox-item">
              <input
                type="checkbox"
                name="isStrategicallyAligned"
                checked={formData.isStrategicallyAligned}
                onChange={handleInputChange}
              />
              <span>Is it aligned with strategic goals/KPIs?</span>
            </label>

            <label className="checkbox-item">
              <input
                type="checkbox"
                name="areToolsListed"
                checked={formData.areToolsListed}
                onChange={handleInputChange}
              />
              <span>Are all impacted tools/modules listed?</span>
            </label>

            <label className="checkbox-item">
              <input
                type="checkbox"
                name="areStakeholdersInvolved"
                checked={formData.areStakeholdersInvolved}
                onChange={handleInputChange}
              />
              <span>Are stakeholders involved?</span>
            </label>

            <label className="checkbox-item">
              <input
                type="checkbox"
                name="areDependenciesIdentified"
                checked={formData.areDependenciesIdentified}
                onChange={handleInputChange}
              />
              <span>Are dependencies identified?</span>
            </label>

            <label className="checkbox-item">
              <input
                type="checkbox"
                name="hasSimilarRequest"
                checked={formData.hasSimilarRequest}
                onChange={handleInputChange}
              />
              <span>Has a similar request been submitted before?</span>
            </label>
          </div>
        </div>

        {/* Similar Request IDs */}
        {formData.hasSimilarRequest && (
          <div className="form-group full-width">
            <label>Similar Request IDs</label>
            <input
              type="text"
              name="similarRequestIds"
              value={formData.similarRequestIds}
              onChange={handleInputChange}
              placeholder="Enter similar request IDs (comma-separated)"
            />
          </div>
        )}

        {/* Affected Applications */}
        <div className="form-group full-width">
          <label>Affected Application(s)</label>
          <input
            type="text"
            value={formData.affectedApplications.join(', ')}
            onChange={(e) => handleArrayInputChange('affectedApplications', e.target.value)}
            placeholder="Enter affected applications (comma-separated)"
          />
        </div>

        {/* Affected Organizations */}
        <div className="form-group full-width">
          <label>Affected Organization(s)</label>
          <input
            type="text"
            value={formData.affectedOrganizations.join(', ')}
            onChange={(e) => handleArrayInputChange('affectedOrganizations', e.target.value)}
            placeholder="Enter affected organizations (comma-separated)"
          />
        </div>

        {/* Workarounds */}
        <div className="form-group full-width">
          <label>Workarounds</label>
          <textarea
            name="workarounds"
            value={formData.workarounds}
            onChange={handleInputChange}
            rows={3}
            placeholder="Describe any current workarounds"
          />
        </div>

        {/* Dependencies */}
        <div className="form-group full-width">
          <label>Dependencies</label>
          <textarea
            name="dependencies"
            value={formData.dependencies}
            onChange={handleInputChange}
            rows={3}
            placeholder="List any dependencies for this request"
          />
        </div>

        {/* Impacted Personas */}
        <div className="form-group full-width">
          <label>Impacted Personas</label>
          <input
            type="text"
            name="impactedPersonas"
            value={formData.impactedPersonas}
            onChange={handleInputChange}
            placeholder="Who will be impacted by this change?"
          />
        </div>

        {/* Business Impact */}
        <div className="form-group full-width">
          <label>Business Impact *</label>
          <textarea
            name="businessImpact"
            value={formData.businessImpact}
            onChange={handleInputChange}
            rows={3}
            className={errors.businessImpact ? 'error' : ''}
            placeholder="Describe the business impact of this request"
          />
          {errors.businessImpact && <span className="error-message">{errors.businessImpact}</span>}
        </div>

        {/* Customer Value */}
        <div className="form-group full-width">
          <label>Customer Value *</label>
          <textarea
            name="customerValue"
            value={formData.customerValue}
            onChange={handleInputChange}
            rows={3}
            className={errors.customerValue ? 'error' : ''}
            placeholder="Describe the value this brings to customers"
          />
          {errors.customerValue && <span className="error-message">{errors.customerValue}</span>}
        </div>

        {/* Initial Effort Estimation */}
        <div className="form-group">
          <label>Initial Effort Estimation</label>
          <input
            type="text"
            name="initialEffortEstimation"
            value={formData.initialEffortEstimation}
            onChange={handleInputChange}
            placeholder="e.g., 2 weeks, 40 hours, 8 story points"
          />
        </div>

        {/* Supporting Files */}
        <div className="form-group full-width">
          <label>Supporting Files or References</label>
          <input
            type="text"
            value={formData.supportingFiles.join(', ')}
            onChange={(e) => handleArrayInputChange('supportingFiles', e.target.value)}
            placeholder="Enter file names or reference URLs (comma-separated)"
          />
        </div>
      </div>
    </div>
  );

  const renderChangeManagement = () => (
    <div className="form-section">
      <h3>🔄 Change Management & Traceability</h3>
      <div className="form-grid">
        {/* Change Type */}
        <div className="form-group">
          <label>Change Type</label>
          <select
            name="changeType"
            value={formData.changeType}
            onChange={handleInputChange}
          >
            <option value="">Select change type</option>
            <option value="Minor">Minor</option>
            <option value="Major">Major</option>
            <option value="Breaking">Breaking</option>
          </select>
        </div>

        {/* Change Owner */}
        <div className="form-group">
          <label>Change Owner</label>
          <input
            type="text"
            name="changeOwner"
            value={formData.changeOwner}
            onChange={handleInputChange}
            placeholder="Who will own this change?"
          />
        </div>

        {/* Impacted Modules */}
        <div className="form-group full-width">
          <label>Impacted Modules/Features</label>
          <input
            type="text"
            value={formData.impactedModules.join(', ')}
            onChange={(e) => handleArrayInputChange('impactedModules', e.target.value)}
            placeholder="Enter impacted modules (comma-separated)"
          />
        </div>

        {/* Related Issue IDs */}
        <div className="form-group full-width">
          <label>Related Issue IDs</label>
          <input
            type="text"
            name="relatedIssueIds"
            value={formData.relatedIssueIds}
            onChange={handleInputChange}
            placeholder="Enter related issue IDs (comma-separated)"
          />
        </div>

        {/* Previous Occurrences */}
        <div className="form-group full-width">
          <label>Previous Occurrences</label>
          <textarea
            name="previousOccurrences"
            value={formData.previousOccurrences}
            onChange={handleInputChange}
            rows={3}
            placeholder="Describe any previous similar occurrences"
          />
        </div>

        {/* Resolution Summary */}
        <div className="form-group full-width">
          <label>Resolution Summary</label>
          <textarea
            name="resolutionSummary"
            value={formData.resolutionSummary}
            onChange={handleInputChange}
            rows={3}
            placeholder="Summarize the proposed resolution"
          />
        </div>

        {/* Approval Comments */}
        <div className="form-group full-width">
          <label>Approval Comments</label>
          <textarea
            name="approvalComments"
            value={formData.approvalComments}
            onChange={handleInputChange}
            rows={3}
            placeholder="Comments for approval process"
          />
        </div>

        {/* Lessons Learned */}
        <div className="form-group full-width">
          <label>Lessons Learned</label>
          <textarea
            name="lessonsLearned"
            value={formData.lessonsLearned}
            onChange={handleInputChange}
            rows={3}
            placeholder="Document lessons learned from similar requests"
          />
        </div>

        {/* Linked Documents */}
        <div className="form-group full-width">
          <label>Linked Documents (JIRA, SharePoint, etc.)</label>
          <input
            type="text"
            value={formData.linkedDocuments.join(', ')}
            onChange={(e) => handleArrayInputChange('linkedDocuments', e.target.value)}
            placeholder="Enter document links (comma-separated)"
          />
        </div>

        {/* Feedback/Closure Comments */}
        <div className="form-group full-width">
          <label>Feedback/Closure Comments</label>
          <textarea
            name="feedbackComments"
            value={formData.feedbackComments}
            onChange={handleInputChange}
            rows={3}
            placeholder="Feedback or closure comments"
          />
        </div>
      </div>
    </div>
  );

  const renderRankingPrioritization = () => (
    <div className="form-section">
      <h3>📊 Ranking & Prioritization</h3>
      <div className="form-grid">
        {/* Urgency Score */}
        <div className="form-group">
          <label>Urgency Score (1-5)</label>
          <div className="score-input">
            <input
              type="range"
              name="urgencyScore"
              min="1"
              max="5"
              value={formData.urgencyScore}
              onChange={handleInputChange}
            />
            <span className="score-value">{formData.urgencyScore}</span>
          </div>
        </div>

        {/* Customer Value Score */}
        <div className="form-group">
          <label>Customer Value Score (1-5)</label>
          <div className="score-input">
            <input
              type="range"
              name="customerValueScore"
              min="1"
              max="5"
              value={formData.customerValueScore}
              onChange={handleInputChange}
            />
            <span className="score-value">{formData.customerValueScore}</span>
          </div>
        </div>

        {/* Strategic Alignment Score */}
        <div className="form-group">
          <label>Strategic Alignment Score (1-5)</label>
          <div className="score-input">
            <input
              type="range"
              name="strategicAlignmentScore"
              min="1"
              max="5"
              value={formData.strategicAlignmentScore}
              onChange={handleInputChange}
            />
            <span className="score-value">{formData.strategicAlignmentScore}</span>
          </div>
        </div>

        {/* Effort Estimation */}
        <div className="form-group">
          <label>Effort Estimation (Story Points or Hours)</label>
          <input
            type="text"
            name="effortEstimation"
            value={formData.effortEstimation}
            onChange={handleInputChange}
            placeholder="e.g., 13 story points, 80 hours"
          />
        </div>

        {/* Final Priority Rank */}
        <div className="form-group">
          <label>Final Priority Rank</label>
          <select
            name="finalPriorityRank"
            value={formData.finalPriorityRank}
            onChange={handleInputChange}
          >
            <option value="">Auto-calculated</option>
            <option value="Critical">Critical</option>
            <option value="High">High</option>
            <option value="Medium">Medium</option>
            <option value="Low">Low</option>
          </select>
        </div>

        {/* Priority Calculation Display */}
        <div className="form-group full-width">
          <div className="priority-calculation">
            <h4>Priority Calculation</h4>
            <div className="calculation-grid">
              <div className="calc-item">
                <span>Urgency:</span>
                <span>{formData.urgencyScore}/5</span>
              </div>
              <div className="calc-item">
                <span>Customer Value:</span>
                <span>{formData.customerValueScore}/5</span>
              </div>
              <div className="calc-item">
                <span>Strategic Alignment:</span>
                <span>{formData.strategicAlignmentScore}/5</span>
              </div>
              <div className="calc-item total">
                <span>Total Score:</span>
                <span>{formData.urgencyScore + formData.customerValueScore + formData.strategicAlignmentScore}/15</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderDependencyAssessment = () => (
    <div className="form-section">
      <h3>🔗 Dependency Assessment</h3>
      <div className="form-grid">
        {/* Application/Technology Dependencies */}
        <div className="form-group full-width">
          <label>Application/Technology Dependencies</label>
          <textarea
            name="applicationDependencies"
            value={formData.applicationDependencies}
            onChange={handleInputChange}
            rows={3}
            placeholder="Describe application and technology dependencies"
          />
        </div>

        {/* Third-party Integration Needs */}
        <div className="form-group full-width">
          <label>Third-party Integration Needs</label>
          <textarea
            name="thirdPartyIntegrations"
            value={formData.thirdPartyIntegrations}
            onChange={handleInputChange}
            rows={3}
            placeholder="Describe any third-party integration requirements"
          />
        </div>

        {/* API/Data Flow Changes */}
        <div className="form-group full-width">
          <label>API/Data Flow Changes</label>
          <textarea
            name="apiDataFlowChanges"
            value={formData.apiDataFlowChanges}
            onChange={handleInputChange}
            rows={3}
            placeholder="Describe any API or data flow changes required"
          />
        </div>

        {/* Tech Stack Impact */}
        <div className="form-group full-width">
          <label>Tech Stack or Infrastructure Impact</label>
          <textarea
            name="techStackImpact"
            value={formData.techStackImpact}
            onChange={handleInputChange}
            rows={3}
            placeholder="Describe impact on technology stack or infrastructure"
          />
        </div>

        {/* Data Model Impact */}
        <div className="form-group full-width">
          <label>Data Model Impact</label>
          <textarea
            name="dataModelImpact"
            value={formData.dataModelImpact}
            onChange={handleInputChange}
            rows={3}
            placeholder="Describe any data model changes required"
          />
        </div>

        {/* Deployment Schedule Impact */}
        <div className="form-group full-width">
          <label>Deployment Schedule Impact</label>
          <textarea
            name="deploymentScheduleImpact"
            value={formData.deploymentScheduleImpact}
            onChange={handleInputChange}
            rows={3}
            placeholder="Describe impact on deployment schedules"
          />
        </div>

        {/* Dependency Summary */}
        <div className="form-group full-width">
          <div className="dependency-summary">
            <h4>Dependency Summary</h4>
            <div className="summary-grid">
              <div className="summary-item">
                <i className="fas fa-server text-blue-500"></i>
                <div>
                  <span className="summary-label">Application Dependencies</span>
                  <span className="summary-status">{formData.applicationDependencies ? 'Identified' : 'Not specified'}</span>
                </div>
              </div>
              <div className="summary-item">
                <i className="fas fa-plug text-green-500"></i>
                <div>
                  <span className="summary-label">Third-party Integrations</span>
                  <span className="summary-status">{formData.thirdPartyIntegrations ? 'Required' : 'Not required'}</span>
                </div>
              </div>
              <div className="summary-item">
                <i className="fas fa-exchange-alt text-purple-500"></i>
                <div>
                  <span className="summary-label">API/Data Flow Changes</span>
                  <span className="summary-status">{formData.apiDataFlowChanges ? 'Required' : 'Not required'}</span>
                </div>
              </div>
              <div className="summary-item">
                <i className="fas fa-cogs text-orange-500"></i>
                <div>
                  <span className="summary-label">Infrastructure Impact</span>
                  <span className="summary-status">{formData.techStackImpact ? 'Impact identified' : 'No impact'}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="request-form">
      <div className="form-header">
        <h2>{isEditing ? 'Edit Request' : 'Create New Request'}</h2>
        <p>Complete all sections to submit a comprehensive request</p>
      </div>

      {renderSectionNavigation()}

      <form onSubmit={handleSubmit} className="request-form-content">
        {activeSection === 'request-details' && renderRequestDetails()}
        {activeSection === 'validation-assessment' && renderValidationAssessment()}
        {activeSection === 'change-management' && renderChangeManagement()}
        {activeSection === 'ranking-prioritization' && renderRankingPrioritization()}
        {activeSection === 'dependency-assessment' && renderDependencyAssessment()}
        
        <div className="form-actions">
          <button type="button" onClick={onCancel} className="cancel-btn">
            Cancel
          </button>
          <button type="submit" disabled={isSubmitting} className="submit-btn">
            {isSubmitting ? 'Submitting...' : (isEditing ? 'Update Request' : 'Create Request')}
          </button>
        </div>
        
        {errors.submit && (
          <div className="error-message submit-error">
            {errors.submit}
          </div>
        )}
      </form>
    </div>
  );
};

export default RequestForm;
