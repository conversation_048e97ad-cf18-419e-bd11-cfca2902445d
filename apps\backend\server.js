// Simple Node.js server for Financial Modeling Application
const http = require('http');
const url = require('url');

const PORT = process.env.PORT || 5001;

// Mock data for demonstration
const mockUsers = [
  {
    id: '1',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    password: 'password123' // In real app, this would be hashed
  },
  {
    id: '2',
    name: 'Financial Analyst',
    email: '<EMAIL>',
    role: 'financial_analyst',
    password: 'password123'
  },
  {
    id: '3',
    name: 'Executive User',
    email: '<EMAIL>',
    role: 'executive',
    password: 'password123'
  },
  {
    id: '4',
    name: '<PERSON><PERSON><PERSON>',
    email: 'gyan<PERSON>',
    role: 'admin',
    password: 'gyanesh123'
  },
  {
    id: '5',
    name: '<PERSON><PERSON><PERSON> (Cache)',
    email: '<EMAIL>',
    role: 'admin',
    password: 'gyanesh123'
  },
  {
    id: '6',
    name: 'Test User',
    email: 'test',
    role: 'admin',
    password: 'test'
  },
  {
    id: '7',
    name: 'Admin Simple',
    email: 'admin',
    role: 'admin',
    password: 'admin'
  }
];

const mockFinancialData = {
  metrics: {
    npv: 2400000,
    irr: 18.7,
    paybackPeriod: 3.2,
    yieldIndex: 1.45,
    grossMargin: 42.3,
    breakEvenSales: 1800000
  },
  models: [
    {
      id: '1',
      name: 'Product Launch Q1 2024',
      lastModified: '2 hours ago',
      npv: '$2.4M',
      irr: '18.7%',
      status: 'Active'
    },
    {
      id: '2',
      name: 'Market Expansion Europe',
      lastModified: '1 day ago',
      npv: '$1.8M',
      irr: '15.2%',
      status: 'Draft'
    }
  ]
};

// Helper function to parse JSON body
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(error);
      }
    });
  });
}

// Helper function to send JSON response
function sendJSON(res, statusCode, data) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  res.end(JSON.stringify(data));
}

// Helper function to read JSON file
const fs = require('fs').promises;
const pathModule = require('path');

async function readJSONFile(filePath) {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    if (error.code === 'ENOENT') {
      return [];
    }
    throw error;
  }
}

// Helper function to write JSON file
async function writeJSONFile(filePath, data) {
  await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
}

// Helper function to generate unique ID
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 9);
}

// Helper function to get request body
function getRequestBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      resolve(body);
    });
    req.on('error', reject);
  });
}

// Helper function to calculate aggregated metrics for Master Business Cases
function calculateAggregatedMetrics(linkedBusinessCases) {
  if (!linkedBusinessCases || linkedBusinessCases.length === 0) {
    return {
      totalCapex: 0,
      totalOpex: 0,
      totalInvestment: 0,
      totalRevenue: 0,
      totalNPV: 0,
      avgIRR: 0,
      avgPaybackPeriod: 0,
      avgGrossMargin: 0,
      avgCommercialMargin: 0,
      linkedCount: 0
    };
  }

  const totalCapex = linkedBusinessCases.reduce((sum, bc) =>
    sum + (bc.financialData?.totalCapex || bc.totalCapex || 0), 0);

  const totalOpex = linkedBusinessCases.reduce((sum, bc) =>
    sum + (bc.financialData?.totalOpex || bc.totalOpex || 0), 0);

  const totalRevenue = linkedBusinessCases.reduce((sum, bc) => {
    if (bc.financialData?.revenue) {
      return sum + bc.financialData.revenue.reduce((revSum, item) => revSum + (item.amount || 0), 0);
    }
    return sum + (bc.totalRevenue || 0);
  }, 0);

  const totalNPV = linkedBusinessCases.reduce((sum, bc) =>
    sum + (bc.calculatedMetrics?.npv || bc.npv || 0), 0);

  const validIRRs = linkedBusinessCases
    .map(bc => bc.calculatedMetrics?.irr || bc.irr || 0)
    .filter(irr => irr > 0);

  const validPaybackPeriods = linkedBusinessCases
    .map(bc => bc.calculatedMetrics?.paybackPeriod || bc.paybackPeriod || 0)
    .filter(period => period > 0);

  const validGrossMargins = linkedBusinessCases
    .map(bc => bc.calculatedMetrics?.grossMargin || bc.grossMargin || 0)
    .filter(margin => margin !== undefined);

  const validCommercialMargins = linkedBusinessCases
    .map(bc => bc.calculatedMetrics?.commercialMargin || bc.commercialMargin || 0)
    .filter(margin => margin !== undefined);

  return {
    totalCapex: Math.round(totalCapex),
    totalOpex: Math.round(totalOpex),
    totalInvestment: Math.round(totalCapex + totalOpex),
    totalRevenue: Math.round(totalRevenue),
    totalNPV: Math.round(totalNPV),
    avgIRR: validIRRs.length > 0 ? Math.round((validIRRs.reduce((sum, irr) => sum + irr, 0) / validIRRs.length) * 10) / 10 : 0,
    avgPaybackPeriod: validPaybackPeriods.length > 0 ? Math.round((validPaybackPeriods.reduce((sum, period) => sum + period, 0) / validPaybackPeriods.length) * 10) / 10 : 0,
    avgGrossMargin: validGrossMargins.length > 0 ? Math.round((validGrossMargins.reduce((sum, margin) => sum + margin, 0) / validGrossMargins.length) * 10) / 10 : 0,
    avgCommercialMargin: validCommercialMargins.length > 0 ? Math.round((validCommercialMargins.reduce((sum, margin) => sum + margin, 0) / validCommercialMargins.length) * 10) / 10 : 0,
    linkedCount: linkedBusinessCases.length
  };
}

// Create HTTP server
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${path}`);

  // Handle CORS preflight
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end();
    return;
  }

  try {
    // Health check
    if (path === '/health' && method === 'GET') {
      sendJSON(res, 200, {
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      });
      return;
    }

    // Login endpoint
    if (path === '/api/auth/login' && method === 'POST') {
      const body = await parseBody(req);
      const { email, password } = body;

      console.log('Login attempt:', { email, password });
      console.log('Available users:', mockUsers.map(u => ({ email: u.email, password: u.password })));

      const user = mockUsers.find(u => u.email === email && u.password === password);
      
      if (user) {
        sendJSON(res, 200, {
          success: true,
          data: {
            token: 'mock-jwt-token-' + user.id,
            user: {
              id: user.id,
              name: user.name,
              email: user.email,
              role: user.role,
              createdAt: new Date(),
              updatedAt: new Date()
            }
          }
        });
      } else {
        sendJSON(res, 400, {
          success: false,
          error: 'Invalid credentials'
        });
      }
      return;
    }

    // Verify token endpoint
    if (path === '/api/auth/verify' && method === 'GET') {
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const userId = token.replace('mock-jwt-token-', '');
        const user = mockUsers.find(u => u.id === userId);
        
        if (user) {
          sendJSON(res, 200, {
            success: true,
            data: {
              id: user.id,
              name: user.name,
              email: user.email,
              role: user.role,
              createdAt: new Date(),
              updatedAt: new Date()
            }
          });
        } else {
          sendJSON(res, 401, {
            success: false,
            error: 'Invalid token'
          });
        }
      } else {
        sendJSON(res, 401, {
          success: false,
          error: 'No token provided'
        });
      }
      return;
    }

    // Dashboard data endpoint
    if (path === '/api/dashboard' && method === 'GET') {
      sendJSON(res, 200, {
        success: true,
        data: mockFinancialData
      });
      return;
    }

    // Financial metrics endpoint
    if (path === '/api/financial/metrics' && method === 'GET') {
      sendJSON(res, 200, {
        success: true,
        data: mockFinancialData.metrics
      });
      return;
    }

    // Financial models endpoint
    if (path === '/api/financial/models' && method === 'GET') {
      sendJSON(res, 200, {
        success: true,
        data: mockFinancialData.models
      });
      return;
    }

    // Feedback endpoints
    if (path === '/api/feedback' && method === 'POST') {
      const body = await parseBody(req);
      const feedback = {
        id: Date.now().toString(),
        ...body,
        createdAt: new Date().toISOString()
      };

      // Store feedback (in memory for demo)
      if (!global.feedbacks) {
        global.feedbacks = [];
      }
      global.feedbacks.push(feedback);

      console.log('📝 Feedback received:', feedback);

      sendJSON(res, 200, {
        success: true,
        message: 'Feedback submitted successfully',
        data: feedback
      });
      return;
    }

    if (path === '/api/feedback' && method === 'GET') {
      const feedbacks = global.feedbacks || [];
      sendJSON(res, 200, {
        success: true,
        data: { feedbacks }
      });
      return;
    }

    // Excel template endpoints
    if (path.startsWith('/api/template/') && method === 'GET') {
      const type = path.split('/')[3];

      const templates = {
        'ideas': {
          filename: 'ideas-template.xlsx',
          headers: ['Title', 'Description', 'Category', 'Priority', 'Submitted By', 'Business Unit', 'Estimated Cost', 'Expected Benefit', 'Tags']
        },
        'business-cases': {
          filename: 'business-cases-template.xlsx',
          headers: ['Name', 'Description', 'Business Unit', 'Start Year', 'End Year', 'CAPEX Amount', 'OPEX Amount', 'Revenue Amount', 'Tags']
        },
        'projects': {
          filename: 'projects-template.xlsx',
          headers: ['Name', 'Description', 'Type', 'Start Date', 'End Date', 'Owner', 'Business Unit', 'Status', 'Budget']
        },
        'programs': {
          filename: 'programs-template.xlsx',
          headers: ['Name', 'Description', 'Owner', 'Business Unit', 'Status', 'Start Date', 'End Date', 'Budget']
        }
      };

      const template = templates[type];
      if (template) {
        sendJSON(res, 200, {
          success: true,
          data: template
        });
      } else {
        sendJSON(res, 400, {
          success: false,
          error: 'Invalid template type'
        });
      }
      return;
    }

    // Bulk operations endpoints
    if (path.endsWith('/bulk') && method === 'POST') {
      const body = await parseBody(req);
      const { data } = body;

      if (!data || !Array.isArray(data)) {
        sendJSON(res, 400, {
          success: false,
          error: 'Invalid data format'
        });
        return;
      }

      // Simulate processing
      const processed = data.map(item => ({
        ...item,
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        createdAt: new Date().toISOString()
      }));

      console.log(`📊 Bulk operation: ${path} - processed ${processed.length} items`);

      sendJSON(res, 200, {
        success: true,
        message: `Successfully processed ${processed.length} items`,
        data: { created: processed.length, items: processed }
      });
      return;
    }

    // Ideas endpoints
    if (path === '/api/ideas' && method === 'GET') {
      try {
        const ideas = await readJSONFile(pathModule.join(__dirname, 'data', 'ideas.json'));

        // Apply filters if provided
        let filteredIdeas = ideas;
        const query = parsedUrl.query || {};
        const { status, priority, businessUnitId, submitterId, search } = query;

        if (status) {
          filteredIdeas = filteredIdeas.filter(idea => idea.status === status);
        }

        if (priority) {
          filteredIdeas = filteredIdeas.filter(idea => idea.priority === priority);
        }

        if (businessUnitId) {
          filteredIdeas = filteredIdeas.filter(idea => idea.businessUnitId === businessUnitId);
        }

        if (submitterId) {
          filteredIdeas = filteredIdeas.filter(idea => idea.submitterId === submitterId);
        }

        if (search) {
          const searchLower = search.toLowerCase();
          filteredIdeas = filteredIdeas.filter(idea =>
            (idea.title && idea.title.toLowerCase().includes(searchLower)) ||
            (idea.description && idea.description.toLowerCase().includes(searchLower)) ||
            (idea.problemStatement && idea.problemStatement.toLowerCase().includes(searchLower)) ||
            (idea.opportunityDescription && idea.opportunityDescription.toLowerCase().includes(searchLower))
          );
        }

        sendJSON(res, 200, {
          success: true,
          data: filteredIdeas,
          count: filteredIdeas.length,
          total: ideas.length
        });
      } catch (error) {
        console.error('Error reading ideas:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to read ideas'
        });
      }
      return;
    }

    if (path.startsWith('/api/ideas/') && method === 'GET') {
      try {
        const ideaId = path.split('/')[3];
        const ideas = await readJSONFile(pathModule.join(__dirname, 'data', 'ideas.json'));
        const idea = ideas.find(i => i.id === ideaId);

        if (!idea) {
          sendJSON(res, 404, {
            success: false,
            error: 'Idea not found'
          });
          return;
        }

        sendJSON(res, 200, {
          success: true,
          data: idea
        });
      } catch (error) {
        console.error('Error reading idea:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to read idea'
        });
      }
      return;
    }

    if (path === '/api/ideas' && method === 'POST') {
      try {
        const body = await getRequestBody(req);
        const ideaData = JSON.parse(body);

        const ideas = await readJSONFile(pathModule.join(__dirname, 'data', 'ideas.json'));

        const newIdea = {
          id: generateId(),
          ...ideaData,
          status: 'submitted',
          submissionDate: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        ideas.push(newIdea);
        await writeJSONFile(pathModule.join(__dirname, 'data', 'ideas.json'), ideas);

        console.log('Created new idea:', newIdea.title);

        sendJSON(res, 201, {
          success: true,
          data: newIdea
        });
      } catch (error) {
        console.error('Error creating idea:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to create idea'
        });
      }
      return;
    }

    if (path.startsWith('/api/ideas/') && method === 'PUT') {
      try {
        const ideaId = path.split('/')[3];
        const body = await getRequestBody(req);
        const ideaData = JSON.parse(body);

        const ideas = await readJSONFile(pathModule.join(__dirname, 'data', 'ideas.json'));
        const ideaIndex = ideas.findIndex(i => i.id === ideaId);

        if (ideaIndex === -1) {
          sendJSON(res, 404, {
            success: false,
            error: 'Idea not found'
          });
          return;
        }

        const updatedIdea = {
          ...ideas[ideaIndex],
          ...ideaData,
          updatedAt: new Date().toISOString()
        };

        ideas[ideaIndex] = updatedIdea;
        await writeJSONFile(pathModule.join(__dirname, 'data', 'ideas.json'), ideas);

        console.log('Updated idea:', updatedIdea.title);

        sendJSON(res, 200, {
          success: true,
          data: updatedIdea
        });
      } catch (error) {
        console.error('Error updating idea:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to update idea'
        });
      }
      return;
    }

    if (path.startsWith('/api/ideas/') && method === 'DELETE') {
      try {
        const ideaId = path.split('/')[3];
        const ideas = await readJSONFile(pathModule.join(__dirname, 'data', 'ideas.json'));
        const ideaIndex = ideas.findIndex(i => i.id === ideaId);

        if (ideaIndex === -1) {
          sendJSON(res, 404, {
            success: false,
            error: 'Idea not found'
          });
          return;
        }

        const deletedIdea = ideas[ideaIndex];
        ideas.splice(ideaIndex, 1);
        await writeJSONFile(pathModule.join(__dirname, 'data', 'ideas.json'), ideas);

        console.log('Deleted idea:', deletedIdea.title);

        sendJSON(res, 200, {
          success: true,
          message: 'Idea deleted successfully'
        });
      } catch (error) {
        console.error('Error deleting idea:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to delete idea'
        });
      }
      return;
    }

    // Business Cases endpoints
    if (path === '/api/business-cases' && method === 'GET') {
      try {
        const businessCases = await readJSONFile(pathModule.join(__dirname, 'data', 'businessCases.json'));

        // Apply filters if provided
        let filteredBusinessCases = businessCases;
        const query = parsedUrl.query || {};
        const { status, businessUnit, search } = query;

        if (status) {
          filteredBusinessCases = filteredBusinessCases.filter(bc => bc.status === status);
        }

        if (businessUnit) {
          filteredBusinessCases = filteredBusinessCases.filter(bc => bc.businessUnit === businessUnit);
        }

        if (search) {
          const searchLower = search.toLowerCase();
          filteredBusinessCases = filteredBusinessCases.filter(bc =>
            (bc.name && bc.name.toLowerCase().includes(searchLower)) ||
            (bc.description && bc.description.toLowerCase().includes(searchLower)) ||
            (bc.tags && bc.tags.some(tag => tag.toLowerCase().includes(searchLower)))
          );
        }

        // Transform data to match frontend expectations
        const transformedBusinessCases = filteredBusinessCases.map(bc => ({
          id: bc.id,
          name: bc.name,
          description: bc.description,
          businessUnit: bc.businessUnit,
          status: bc.status || 'draft',
          startYear: bc.timeframe?.startYear || bc.startYear,
          endYear: bc.timeframe?.endYear || bc.endYear,
          tags: bc.tags || [],
          totalCapex: bc.financialData?.totalCapex || bc.totalCapex || 0,
          totalOpex: bc.financialData?.totalOpex || bc.totalOpex || 0,
          totalRevenue: bc.financialData?.revenue?.reduce((sum, item) => sum + (item.amount || 0), 0) || bc.totalRevenue || 0,
          capexItems: bc.financialData?.capex || bc.capexItems || [],
          opexItems: bc.financialData?.opex || bc.opexItems || [],
          revenueItems: bc.financialData?.revenue || bc.revenueItems || [],
          problemStatement: bc.problemStatement || '',
          proposedSolution: bc.proposedSolution || '',
          strategicAlignment: bc.strategicAlignment || '',
          successCriteria: bc.successCriteria || '',
          assumptions: bc.assumptions || '',
          risks: bc.risks || '',
          promotedFromIdea: bc.promotedFromIdea || bc.linkedIdeas?.length > 0 || false,
          createdAt: bc.createdAt,
          updatedAt: bc.updatedAt
        }));

        sendJSON(res, 200, {
          success: true,
          data: transformedBusinessCases,
          count: transformedBusinessCases.length,
          total: businessCases.length
        });
      } catch (error) {
        console.error('Error reading business cases:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to read business cases'
        });
      }
      return;
    }

    if (path.startsWith('/api/business-cases/') && method === 'GET') {
      try {
        const businessCaseId = path.split('/')[3];
        const businessCases = await readJSONFile(pathModule.join(__dirname, 'data', 'businessCases.json'));
        const businessCase = businessCases.find(bc => bc.id === businessCaseId);

        if (!businessCase) {
          sendJSON(res, 404, {
            success: false,
            error: 'Business case not found'
          });
          return;
        }

        // Transform data to match frontend expectations
        const transformedBusinessCase = {
          id: businessCase.id,
          name: businessCase.name,
          description: businessCase.description,
          businessUnit: businessCase.businessUnit,
          status: businessCase.status || 'draft',
          startYear: businessCase.timeframe?.startYear || businessCase.startYear,
          endYear: businessCase.timeframe?.endYear || businessCase.endYear,
          tags: businessCase.tags || [],
          totalCapex: businessCase.financialData?.totalCapex || businessCase.totalCapex || 0,
          totalOpex: businessCase.financialData?.totalOpex || businessCase.totalOpex || 0,
          totalRevenue: businessCase.financialData?.revenue?.reduce((sum, item) => sum + (item.amount || 0), 0) || businessCase.totalRevenue || 0,
          capexItems: businessCase.financialData?.capex || businessCase.capexItems || [],
          opexItems: businessCase.financialData?.opex || businessCase.opexItems || [],
          revenueItems: businessCase.financialData?.revenue || businessCase.revenueItems || [],
          problemStatement: businessCase.problemStatement || '',
          proposedSolution: businessCase.proposedSolution || '',
          strategicAlignment: businessCase.strategicAlignment || '',
          successCriteria: businessCase.successCriteria || '',
          assumptions: businessCase.assumptions || '',
          risks: businessCase.risks || '',
          promotedFromIdea: businessCase.promotedFromIdea || businessCase.linkedIdeas?.length > 0 || false,
          createdAt: businessCase.createdAt,
          updatedAt: businessCase.updatedAt
        };

        sendJSON(res, 200, {
          success: true,
          data: transformedBusinessCase
        });
      } catch (error) {
        console.error('Error reading business case:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to read business case'
        });
      }
      return;
    }

    // Master Business Cases endpoints
    if (path === '/api/master-bc' && method === 'GET') {
      try {
        const masterBusinessCases = await readJSONFile(pathModule.join(__dirname, 'data', 'masterBusinessCases.json'));

        // Apply filters if provided
        let filteredMasterBCs = masterBusinessCases;
        const query = parsedUrl.query || {};
        const { status, category, priority, search } = query;

        if (status) {
          filteredMasterBCs = filteredMasterBCs.filter(mbc => mbc.status === status);
        }

        if (category) {
          filteredMasterBCs = filteredMasterBCs.filter(mbc => mbc.category === category);
        }

        if (priority) {
          filteredMasterBCs = filteredMasterBCs.filter(mbc => mbc.priority === priority);
        }

        if (search) {
          const searchLower = search.toLowerCase();
          filteredMasterBCs = filteredMasterBCs.filter(mbc =>
            (mbc.name && mbc.name.toLowerCase().includes(searchLower)) ||
            (mbc.description && mbc.description.toLowerCase().includes(searchLower)) ||
            (mbc.metadata?.tags && mbc.metadata.tags.some(tag => tag.toLowerCase().includes(searchLower)))
          );
        }

        // Transform data to match frontend expectations
        const transformedMasterBCs = filteredMasterBCs.map(mbc => ({
          id: mbc.id,
          name: mbc.name,
          description: mbc.description,
          category: mbc.category || 'General',
          businessUnits: mbc.businessUnits || [],
          priority: mbc.priority || 'Medium',
          status: mbc.status || 'draft',
          linkedBCs: mbc.linkedBCs || [],
          linkedPrograms: mbc.linkedPrograms || [],
          aggregatedMetrics: mbc.aggregatedMetrics || {},
          metadata: mbc.metadata || {},
          createdBy: mbc.createdBy,
          createdAt: mbc.createdAt,
          updatedAt: mbc.updatedAt
        }));

        sendJSON(res, 200, {
          success: true,
          data: transformedMasterBCs,
          count: transformedMasterBCs.length,
          total: masterBusinessCases.length
        });
      } catch (error) {
        console.error('Error reading master business cases:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to read master business cases'
        });
      }
      return;
    }

    if (path.startsWith('/api/master-bc/') && method === 'GET') {
      try {
        const masterBCId = path.split('/')[3];
        const masterBusinessCases = await readJSONFile(pathModule.join(__dirname, 'data', 'masterBusinessCases.json'));
        const masterBC = masterBusinessCases.find(mbc => mbc.id === masterBCId);

        if (!masterBC) {
          sendJSON(res, 404, {
            success: false,
            error: 'Master business case not found'
          });
          return;
        }

        // Get linked business cases details
        const businessCases = await readJSONFile(pathModule.join(__dirname, 'data', 'businessCases.json'));
        const linkedBCDetails = businessCases.filter(bc => masterBC.linkedBCs?.includes(bc.id));

        // Get linked programs details
        const programs = await readJSONFile(pathModule.join(__dirname, 'data', 'programs.json'));
        const linkedProgramDetails = programs.filter(p => p.linkedMasterBC === masterBCId);

        const transformedMasterBC = {
          id: masterBC.id,
          name: masterBC.name,
          description: masterBC.description,
          category: masterBC.category || 'General',
          businessUnits: masterBC.businessUnits || [],
          priority: masterBC.priority || 'Medium',
          status: masterBC.status || 'draft',
          linkedBCs: masterBC.linkedBCs || [],
          linkedBCDetails: linkedBCDetails,
          linkedPrograms: linkedProgramDetails.map(p => p.id),
          linkedProgramDetails: linkedProgramDetails,
          aggregatedMetrics: masterBC.aggregatedMetrics || {},
          metadata: masterBC.metadata || {},
          createdBy: masterBC.createdBy,
          createdAt: masterBC.createdAt,
          updatedAt: masterBC.updatedAt
        };

        sendJSON(res, 200, {
          success: true,
          data: transformedMasterBC
        });
      } catch (error) {
        console.error('Error reading master business case:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to read master business case'
        });
      }
      return;
    }

    if (path === '/api/master-bc' && method === 'POST') {
      try {
        const body = await getRequestBody(req);
        const masterBCData = JSON.parse(body);

        const masterBusinessCases = await readJSONFile(pathModule.join(__dirname, 'data', 'masterBusinessCases.json'));

        const newMasterBC = {
          id: `mbc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: masterBCData.name,
          description: masterBCData.description,
          category: masterBCData.category || 'General',
          businessUnits: masterBCData.businessUnits || [],
          priority: masterBCData.priority || 'Medium',
          status: 'draft',
          linkedBCs: [],
          aggregatedMetrics: {},
          metadata: {
            version: '1.0',
            lastReviewed: new Date().toISOString(),
            nextReview: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(), // 90 days from now
            stakeholders: [masterBCData.createdBy || 'unknown'],
            tags: masterBCData.tags || [],
            riskLevel: masterBCData.riskLevel || 'Medium',
            strategicAlignment: masterBCData.strategicAlignment || 'Medium'
          },
          createdBy: masterBCData.createdBy || 'unknown',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        masterBusinessCases.push(newMasterBC);
        await writeJSONFile(pathModule.join(__dirname, 'data', 'masterBusinessCases.json'), masterBusinessCases);

        console.log('Created new master business case:', newMasterBC.name);

        sendJSON(res, 201, {
          success: true,
          data: newMasterBC,
          message: 'Master business case created successfully'
        });
      } catch (error) {
        console.error('Error creating master business case:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to create master business case'
        });
      }
      return;
    }

    if (path.startsWith('/api/master-bc/') && path.endsWith('/link-bc') && method === 'POST') {
      try {
        const masterBCId = path.split('/')[3];
        const body = await getRequestBody(req);
        const { businessCaseIds } = JSON.parse(body);

        const masterBusinessCases = await readJSONFile(pathModule.join(__dirname, 'data', 'masterBusinessCases.json'));
        const masterBCIndex = masterBusinessCases.findIndex(mbc => mbc.id === masterBCId);

        if (masterBCIndex === -1) {
          sendJSON(res, 404, {
            success: false,
            error: 'Master business case not found'
          });
          return;
        }

        // Update linked business cases
        const masterBC = masterBusinessCases[masterBCIndex];
        masterBC.linkedBCs = [...new Set([...(masterBC.linkedBCs || []), ...businessCaseIds])];
        masterBC.updatedAt = new Date().toISOString();

        // Recalculate aggregated metrics
        const businessCases = await readJSONFile(pathModule.join(__dirname, 'data', 'businessCases.json'));
        const linkedBCs = businessCases.filter(bc => masterBC.linkedBCs.includes(bc.id));

        masterBC.aggregatedMetrics = calculateAggregatedMetrics(linkedBCs);

        await writeJSONFile(pathModule.join(__dirname, 'data', 'masterBusinessCases.json'), masterBusinessCases);

        sendJSON(res, 200, {
          success: true,
          data: masterBC,
          message: 'Business cases linked successfully'
        });
      } catch (error) {
        console.error('Error linking business cases:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to link business cases'
        });
      }
      return;
    }

    // Programs endpoints
    if (path === '/api/programs' && method === 'GET') {
      try {
        const programs = await readJSONFile(pathModule.join(__dirname, 'data', 'programs.json'));

        // Apply filters if provided
        let filteredPrograms = programs;
        const query = parsedUrl.query || {};
        const { status, businessUnit, search } = query;

        if (status) {
          filteredPrograms = filteredPrograms.filter(p => p.status === status);
        }

        if (businessUnit) {
          filteredPrograms = filteredPrograms.filter(p => p.businessUnit === businessUnit);
        }

        if (search) {
          const searchLower = search.toLowerCase();
          filteredPrograms = filteredPrograms.filter(p =>
            (p.name && p.name.toLowerCase().includes(searchLower)) ||
            (p.description && p.description.toLowerCase().includes(searchLower))
          );
        }

        // Transform data to match frontend expectations
        const transformedPrograms = filteredPrograms.map(p => ({
          id: p.id,
          name: p.name,
          description: p.description,
          businessUnit: p.businessUnit,
          status: p.status || 'active',
          owner: p.owner,
          startDate: p.startDate,
          endDate: p.endDate,
          budget: p.budget || 0,
          linkedMasterBC: p.linkedMasterBC,
          linkedProjects: p.linkedProjects || [],
          linkedEpics: p.linkedEpics || [],
          createdAt: p.createdAt,
          updatedAt: p.updatedAt
        }));

        sendJSON(res, 200, {
          success: true,
          data: transformedPrograms,
          count: transformedPrograms.length,
          total: programs.length
        });
      } catch (error) {
        console.error('Error reading programs:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to read programs'
        });
      }
      return;
    }

    if (path.startsWith('/api/programs/') && method === 'GET') {
      try {
        const programId = path.split('/')[3];
        const programs = await readJSONFile(pathModule.join(__dirname, 'data', 'programs.json'));
        const program = programs.find(p => p.id === programId);

        if (!program) {
          sendJSON(res, 404, {
            success: false,
            error: 'Program not found'
          });
          return;
        }

        // Get linked Master BC details
        let linkedMasterBCDetails = null;
        if (program.linkedMasterBC) {
          const masterBusinessCases = await readJSONFile(pathModule.join(__dirname, 'data', 'masterBusinessCases.json'));
          linkedMasterBCDetails = masterBusinessCases.find(mbc => mbc.id === program.linkedMasterBC);
        }

        const transformedProgram = {
          id: program.id,
          name: program.name,
          description: program.description,
          businessUnit: program.businessUnit,
          status: program.status || 'active',
          owner: program.owner,
          startDate: program.startDate,
          endDate: program.endDate,
          budget: program.budget || 0,
          linkedMasterBC: program.linkedMasterBC,
          linkedMasterBCDetails: linkedMasterBCDetails,
          linkedProjects: program.linkedProjects || [],
          linkedEpics: program.linkedEpics || [],
          createdAt: program.createdAt,
          updatedAt: program.updatedAt
        };

        sendJSON(res, 200, {
          success: true,
          data: transformedProgram
        });
      } catch (error) {
        console.error('Error reading program:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to read program'
        });
      }
      return;
    }

    if (path.startsWith('/api/programs/') && path.endsWith('/link-master-bc') && method === 'POST') {
      try {
        const programId = path.split('/')[3];
        const body = await getRequestBody(req);
        const { masterBCId } = JSON.parse(body);

        const programs = await readJSONFile(pathModule.join(__dirname, 'data', 'programs.json'));
        const programIndex = programs.findIndex(p => p.id === programId);

        if (programIndex === -1) {
          sendJSON(res, 404, {
            success: false,
            error: 'Program not found'
          });
          return;
        }

        // Check if Master BC exists
        const masterBusinessCases = await readJSONFile(pathModule.join(__dirname, 'data', 'masterBusinessCases.json'));
        const masterBC = masterBusinessCases.find(mbc => mbc.id === masterBCId);

        if (!masterBC) {
          sendJSON(res, 404, {
            success: false,
            error: 'Master business case not found'
          });
          return;
        }

        // Check if another program is already linked to this Master BC (one-to-one constraint)
        const existingProgram = programs.find(p => p.linkedMasterBC === masterBCId && p.id !== programId);
        if (existingProgram) {
          sendJSON(res, 400, {
            success: false,
            error: `Master business case is already linked to program: ${existingProgram.name}`
          });
          return;
        }

        // Update program
        programs[programIndex].linkedMasterBC = masterBCId;
        programs[programIndex].updatedAt = new Date().toISOString();

        await writeJSONFile(pathModule.join(__dirname, 'data', 'programs.json'), programs);

        sendJSON(res, 200, {
          success: true,
          data: programs[programIndex],
          message: 'Master business case linked successfully'
        });
      } catch (error) {
        console.error('Error linking master business case:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to link master business case'
        });
      }
      return;
    }

    // Placeholder endpoints
    const placeholderEndpoints = [
      '/api/parameters',
      '/api/costs',
      '/api/sales',
      '/api/pricing',
      '/api/sensitivity',
      '/api/financial',
      '/api/export',
      '/api/users',
      '/api/projects',
      '/api/epics',

      '/api/system/integrity/validate'
    ];

    if (placeholderEndpoints.some(endpoint => path.startsWith(endpoint))) {
      sendJSON(res, 200, {
        success: true,
        data: [],
        message: `${path} endpoint - coming soon`
      });
      return;
    }

    // 404 for unknown routes
    sendJSON(res, 404, {
      success: false,
      error: 'Route not found'
    });

  } catch (error) {
    console.error('Server error:', error);
    sendJSON(res, 500, {
      success: false,
      error: 'Internal server error'
    });
  }
});

// Start server
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Financial Modeling API Server running on port ${PORT}`);
  console.log(`📊 Dashboard: http://localhost:${PORT}/health`);
  console.log(`🌐 External access: http://0.0.0.0:${PORT}/health`);
  console.log(`🔐 Demo credentials:`);
  console.log(`   Admin: <EMAIL> / password123`);
  console.log(`   Analyst: <EMAIL> / password123`);
  console.log(`   Executive: <EMAIL> / password123`);
  console.log(`   Gyanesh (Admin): gyanesh / gyanesh123`);
  console.log(`\n📡 API Endpoints:`);
  console.log(`   POST /api/auth/login`);
  console.log(`   GET  /api/auth/verify`);
  console.log(`   GET  /api/dashboard`);
  console.log(`   GET  /api/financial/metrics`);
  console.log(`   GET  /api/financial/models`);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('\nSIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});
