.financial-formulas {
  padding: 24px;
  background: #f8fafc;
}

.formulas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.header-content h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.header-content p {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
}

.download-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

.download-btn:hover {
  background: #2563eb;
}

/* Formula Categories */
.formula-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.formula-category {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.category-header {
  padding: 20px 24px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.category-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.category-header p {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
}

.category-content {
  padding: 24px;
}

/* Individual Formula */
.formula-item {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f1f5f9;
}

.formula-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.formula-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.formula-description {
  font-size: 0.9rem;
  color: #64748b;
  margin-bottom: 12px;
  line-height: 1.5;
}

.formula-equation {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  font-family: 'Courier New', monospace;
  font-size: 0.95rem;
  color: #1e293b;
  overflow-x: auto;
}

.formula-variables {
  background: #fefce8;
  border: 1px solid #fde047;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
}

.formula-variables h5 {
  font-size: 0.85rem;
  font-weight: 600;
  color: #a16207;
  margin: 0 0 8px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.variable-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.variable-list li {
  font-size: 0.85rem;
  color: #92400e;
  margin-bottom: 4px;
  padding-left: 12px;
  position: relative;
}

.variable-list li:before {
  content: "•";
  position: absolute;
  left: 0;
  color: #d97706;
}

.formula-example {
  background: #ecfdf5;
  border: 1px solid #10b981;
  border-radius: 6px;
  padding: 12px;
}

.formula-example h5 {
  font-size: 0.85rem;
  font-weight: 600;
  color: #047857;
  margin: 0 0 8px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.example-calculation {
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  color: #065f46;
  line-height: 1.4;
}

/* Implementation Section */
.implementation-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.implementation-header {
  padding: 20px 24px;
  background: linear-gradient(135deg, #10b981, #047857);
  color: white;
  border-radius: 12px 12px 0 0;
}

.implementation-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.implementation-header p {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
}

.implementation-content {
  padding: 24px;
}

.code-block {
  background: #1e293b;
  color: #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  overflow-x: auto;
  line-height: 1.5;
}

.code-comment {
  color: #94a3b8;
}

.code-keyword {
  color: #60a5fa;
}

.code-string {
  color: #34d399;
}

.code-number {
  color: #fbbf24;
}

/* Usage Guidelines */
.usage-guidelines {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.guidelines-header {
  padding: 20px 24px;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border-radius: 12px 12px 0 0;
}

.guidelines-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.guidelines-header p {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
}

.guidelines-content {
  padding: 24px;
}

.guideline-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #8b5cf6;
}

.guideline-icon {
  width: 24px;
  height: 24px;
  background: #8b5cf6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  flex-shrink: 0;
  margin-top: 2px;
}

.guideline-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.guideline-content p {
  font-size: 0.9rem;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .financial-formulas {
    padding: 16px;
  }
  
  .formulas-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .formula-categories {
    grid-template-columns: 1fr;
  }
  
  .formula-equation,
  .code-block {
    font-size: 0.8rem;
  }
}
