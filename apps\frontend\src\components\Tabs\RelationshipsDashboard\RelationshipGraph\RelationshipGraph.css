.relationship-graph {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.graph-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.graph-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.25rem;
  font-weight: 600;
}

.graph-stats {
  display: flex;
  gap: 1rem;
}

.stat {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 0.875rem;
}

.graph-container {
  flex: 1;
  overflow: auto;
  padding: 1rem;
  background: white;
  position: relative;
}

.relationship-svg {
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background: #fafafa;
}

.graph-edge {
  transition: stroke-width 0.2s ease;
}

.graph-edge:hover {
  stroke-width: 4px !important;
}

.edge-label {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  font-weight: 500;
  pointer-events: none;
}

.arrow {
  transition: fill 0.2s ease;
}

.graph-node {
  transition: all 0.2s ease;
}

.graph-node:hover .node-circle {
  filter: brightness(1.1);
  stroke-width: 3px;
}

.node-circle {
  transition: all 0.2s ease;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.node-icon {
  pointer-events: none;
  user-select: none;
}

.node-label {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 500;
  pointer-events: none;
  user-select: none;
}

.graph-legend {
  padding: 1rem 1.5rem;
  background: white;
  border-top: 1px solid #e9ecef;
}

.graph-legend h4 {
  margin: 0 0 0.75rem 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #666;
}

.legend-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.node-details-panel {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 300px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  z-index: 10;
}

.details-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.details-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.details-header h4 {
  margin: 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.details-header p {
  margin: 0;
  color: #666;
  font-size: 0.875rem;
  text-transform: capitalize;
}

.details-header button {
  margin-left: auto;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.details-header button:hover {
  background: #e9ecef;
  color: #333;
}

.details-content {
  padding: 1rem;
}

.detail-section {
  margin-bottom: 1rem;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h5 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-section p {
  margin: 0.25rem 0;
  color: #666;
  font-size: 0.875rem;
  line-height: 1.4;
}

.detail-section strong {
  color: #333;
  font-weight: 600;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #666;
}

.no-data i {
  margin-bottom: 1rem;
}

.no-data p {
  margin: 0;
  font-size: 1.125rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .graph-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .graph-stats {
    flex-direction: column;
    gap: 0.5rem;
  }

  .legend-items {
    flex-direction: column;
    gap: 0.5rem;
  }

  .node-details-panel {
    position: relative;
    top: 0;
    right: 0;
    width: 100%;
    margin-top: 1rem;
  }

  .relationship-svg {
    width: 100%;
    height: auto;
  }
}

/* Animation for node selection */
@keyframes nodeSelect {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.graph-node.selected .node-circle {
  animation: nodeSelect 0.3s ease;
}

/* Hover effects */
.graph-node:hover .node-label {
  font-weight: 600;
  fill: #000;
}

.graph-edge:hover + .edge-label {
  font-weight: 600;
  fill: #000;
}
