import React, { useState } from 'react';
import ApprovalModal from './ApprovalModal';

const IdeaApprovalCard = ({ 
  idea, 
  onApprove, 
  onReject, 
  onRequestMoreInfo, 
  viewMode = 'cards',
  showPendingOnly = false 
}) => {
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [modalType, setModalType] = useState(''); // 'approve', 'reject', 'request-info'

  const getStatusBadge = (status) => {
    const statusConfig = {
      submitted: { color: 'bg-blue-100 text-blue-800', icon: 'fas fa-paper-plane' },
      'under-review': { color: 'bg-yellow-100 text-yellow-800', icon: 'fas fa-clock' },
      approved: { color: 'bg-green-100 text-green-800', icon: 'fas fa-check-circle' },
      rejected: { color: 'bg-red-100 text-red-800', icon: 'fas fa-times-circle' },
      'on-hold': { color: 'bg-orange-100 text-orange-800', icon: 'fas fa-pause-circle' }
    };

    const config = statusConfig[status] || statusConfig.submitted;
    
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <i className={`${config.icon} mr-1`}></i>
        {status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </span>
    );
  };

  const getPriorityBadge = (priority) => {
    const priorityConfig = {
      low: { color: 'bg-green-100 text-green-800', icon: 'fas fa-arrow-down' },
      medium: { color: 'bg-yellow-100 text-yellow-800', icon: 'fas fa-minus' },
      high: { color: 'bg-orange-100 text-orange-800', icon: 'fas fa-arrow-up' },
      critical: { color: 'bg-red-100 text-red-800', icon: 'fas fa-exclamation-triangle' }
    };

    const config = priorityConfig[priority] || priorityConfig.medium;
    
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <i className={`${config.icon} mr-1`}></i>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </span>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getBusinessUnitName = (businessUnitId) => {
    const businessUnits = {
      'bu1': 'Technology',
      'bu2': 'Marketing', 
      'bu3': 'Operations',
      'bu4': 'Finance',
      'bu5': 'Human Resources',
      'bu6': 'Sales'
    };
    return businessUnits[businessUnitId] || businessUnitId;
  };

  const handleApprovalAction = (type) => {
    setModalType(type);
    setShowApprovalModal(true);
  };

  const handleModalSubmit = (data) => {
    switch (modalType) {
      case 'approve':
        onApprove(idea.id, data);
        break;
      case 'reject':
        onReject(idea.id, data);
        break;
      case 'request-info':
        onRequestMoreInfo(idea.id, data);
        break;
    }
    setShowApprovalModal(false);
  };

  const canTakeAction = idea.status === 'submitted' || idea.status === 'under-review';

  if (viewMode === 'table') {
    return (
      <>
        <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
          <div className="grid grid-cols-12 gap-4 items-center">
            {/* Title and Description */}
            <div className="col-span-4">
              <h4 className="font-semibold text-gray-900 mb-1">{idea.title}</h4>
              <p className="text-sm text-gray-600 line-clamp-2">
                {idea.description || idea.problemStatement}
              </p>
            </div>

            {/* Status and Priority */}
            <div className="col-span-2 space-y-1">
              {getStatusBadge(idea.status)}
              {getPriorityBadge(idea.priority)}
            </div>

            {/* Business Unit */}
            <div className="col-span-2">
              <span className="text-sm text-gray-600">
                {getBusinessUnitName(idea.businessUnitId)}
              </span>
            </div>

            {/* Submission Date */}
            <div className="col-span-2">
              <span className="text-sm text-gray-600">
                {formatDate(idea.submissionDate || idea.createdAt)}
              </span>
            </div>

            {/* Actions */}
            <div className="col-span-2">
              {canTakeAction ? (
                <div className="flex space-x-1">
                  <button
                    onClick={() => handleApprovalAction('approve')}
                    className="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700"
                    title="Approve"
                  >
                    <i className="fas fa-check"></i>
                  </button>
                  <button
                    onClick={() => handleApprovalAction('reject')}
                    className="px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700"
                    title="Reject"
                  >
                    <i className="fas fa-times"></i>
                  </button>
                  <button
                    onClick={() => handleApprovalAction('request-info')}
                    className="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
                    title="Request More Info"
                  >
                    <i className="fas fa-question"></i>
                  </button>
                </div>
              ) : (
                <span className="text-xs text-gray-500">No actions</span>
              )}
            </div>
          </div>
        </div>

        <ApprovalModal
          isOpen={showApprovalModal}
          onClose={() => setShowApprovalModal(false)}
          onSubmit={handleModalSubmit}
          type={modalType}
          idea={idea}
        />
      </>
    );
  }

  // Card view
  return (
    <>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{idea.title}</h3>
            <div className="flex items-center space-x-2 mb-2">
              {getStatusBadge(idea.status)}
              {getPriorityBadge(idea.priority)}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-3 mb-4">
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-1">Description</h4>
            <p className="text-sm text-gray-600 line-clamp-3">
              {idea.description || idea.problemStatement || 'No description provided'}
            </p>
          </div>

          {idea.opportunityDescription && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-1">Opportunity</h4>
              <p className="text-sm text-gray-600 line-clamp-2">
                {idea.opportunityDescription}
              </p>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Business Unit:</span>
              <span className="ml-1 text-gray-600">
                {getBusinessUnitName(idea.businessUnitId)}
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Submitted:</span>
              <span className="ml-1 text-gray-600">
                {formatDate(idea.submissionDate || idea.createdAt)}
              </span>
            </div>
            {idea.estimatedCost && (
              <div>
                <span className="font-medium text-gray-700">Est. Cost:</span>
                <span className="ml-1 text-gray-600">
                  ${idea.estimatedCost.toLocaleString()}
                </span>
              </div>
            )}
            {idea.expectedBenefit && (
              <div>
                <span className="font-medium text-gray-700">Est. Benefit:</span>
                <span className="ml-1 text-gray-600">
                  ${idea.expectedBenefit.toLocaleString()}
                </span>
              </div>
            )}
          </div>

          {idea.submitterName && (
            <div className="text-sm">
              <span className="font-medium text-gray-700">Submitted by:</span>
              <span className="ml-1 text-gray-600">{idea.submitterName}</span>
            </div>
          )}

          {idea.tags && idea.tags.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-1">Tags</h4>
              <div className="flex flex-wrap gap-1">
                {idea.tags.map((tag, index) => (
                  <span key={index} className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        {canTakeAction && (
          <div className="border-t border-gray-200 pt-4">
            <div className="flex space-x-3">
              <button
                onClick={() => handleApprovalAction('approve')}
                className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
              >
                <i className="fas fa-check mr-2"></i>
                Approve
              </button>
              <button
                onClick={() => handleApprovalAction('reject')}
                className="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
              >
                <i className="fas fa-times mr-2"></i>
                Reject
              </button>
              <button
                onClick={() => handleApprovalAction('request-info')}
                className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                <i className="fas fa-question mr-2"></i>
                More Info
              </button>
            </div>
          </div>
        )}

        {!canTakeAction && (
          <div className="border-t border-gray-200 pt-4">
            <div className="text-center text-sm text-gray-500">
              {idea.status === 'approved' && (
                <span className="text-green-600">
                  <i className="fas fa-check-circle mr-1"></i>
                  Already approved
                </span>
              )}
              {idea.status === 'rejected' && (
                <span className="text-red-600">
                  <i className="fas fa-times-circle mr-1"></i>
                  Already rejected
                </span>
              )}
            </div>
          </div>
        )}
      </div>

      <ApprovalModal
        isOpen={showApprovalModal}
        onClose={() => setShowApprovalModal(false)}
        onSubmit={handleModalSubmit}
        type={modalType}
        idea={idea}
      />
    </>
  );
};

export default IdeaApprovalCard;
