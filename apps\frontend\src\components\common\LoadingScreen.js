/**
 * MasterBC - Loading Screen Component
 * 
 * <AUTHOR>
 * @repository https://github.com/mahegyaneshpandey/spm
 * @date January 27, 2025
 */

import React from 'react';
import { APP_METADATA } from '../../utils/constants';

/**
 * Loading Screen Component
 * 
 * Displays a professional loading screen during application initialization
 * or long-running operations.
 * 
 * @param {Object} props - Component props
 * @param {string} props.message - Loading message to display
 * @param {boolean} props.showProgress - Whether to show progress indicator
 * @param {number} props.progress - Progress percentage (0-100)
 */
const LoadingScreen = ({ 
  message = 'Loading...', 
  showProgress = false, 
  progress = 0 
}) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="text-center">
        {/* Application Logo/Icon */}
        <div className="mx-auto mb-8">
          <div className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
            <svg 
              className="w-8 h-8 text-white" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" 
              />
            </svg>
          </div>
          
          {/* Application Name */}
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {APP_METADATA.NAME}
          </h1>
          <p className="text-gray-600 text-sm">
            {APP_METADATA.DESCRIPTION}
          </p>
        </div>

        {/* Loading Spinner */}
        <div className="mb-6">
          <div className="inline-flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>

        {/* Loading Message */}
        <p className="text-gray-700 text-lg mb-4">
          {message}
        </p>

        {/* Progress Bar (Optional) */}
        {showProgress && (
          <div className="w-64 mx-auto mb-6">
            <div className="bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-500 mt-2">
              {Math.round(progress)}% Complete
            </p>
          </div>
        )}

        {/* Loading Dots Animation */}
        <div className="flex justify-center space-x-1 mb-8">
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>

        {/* Application Info */}
        <div className="text-xs text-gray-500">
          <p>Version {APP_METADATA.VERSION}</p>
          <p>By {APP_METADATA.AUTHOR}</p>
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
