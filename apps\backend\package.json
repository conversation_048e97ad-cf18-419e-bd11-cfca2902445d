{"name": "financial-modeling-backend", "version": "1.0.0", "description": "Backend API for Financial Modeling Application", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "xlsx": "^0.18.5", "pdfkit": "^0.13.0", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.5.0", "@types/cors": "^2.8.13", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/multer": "^1.4.7", "@types/compression": "^1.7.2", "@types/morgan": "^1.9.4", "@types/jest": "^29.5.4", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "eslint": "^8.47.0", "jest": "^29.6.2", "nodemon": "^3.0.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}}