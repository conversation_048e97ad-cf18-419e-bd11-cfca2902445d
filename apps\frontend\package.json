{"name": "business-case-management-react", "version": "1.0.0", "description": "React-based Business Case Management System", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "ajv": "^8.17.1", "axios": "^1.6.0", "chart.js": "^4.4.0", "classnames": "^2.3.2", "date-fns": "^2.30.0", "intro.js": "^7.2.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-modal": "^3.16.1", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-toastify": "^9.1.3", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.0"}}