import React, { useState, useMemo } from 'react';
import './MasterBCList.css';

const MasterBCList = ({
  masterBusinessCases,
  onCreateNew,
  onEdit,
  onViewDetail,
  onLinkBusinessCases,
  onLinkPrograms
}) => {
  console.log('🔍 MasterBCList received data:', masterBusinessCases);
  console.log('📊 MasterBCList data length:', masterBusinessCases?.length);
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    category: '',
    priority: ''
  });
  const [sortConfig, setSortConfig] = useState({ key: 'name', direction: 'asc' });
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Filter and sort data
  const filteredAndSortedData = useMemo(() => {
    let filtered = masterBusinessCases.filter(mbc => {
      const matchesSearch = !filters.search || 
        mbc.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        mbc.description.toLowerCase().includes(filters.search.toLowerCase());
      
      const matchesStatus = !filters.status || mbc.status === filters.status;
      const matchesCategory = !filters.category || mbc.category === filters.category;
      const matchesPriority = !filters.priority || mbc.priority === filters.priority;

      return matchesSearch && matchesStatus && matchesCategory && matchesPriority;
    });

    // Sort data
    if (sortConfig.key) {
      filtered.sort((a, b) => {
        let aValue = a[sortConfig.key];
        let bValue = b[sortConfig.key];

        // Handle nested values for aggregated metrics
        if (sortConfig.key.includes('.')) {
          const keys = sortConfig.key.split('.');
          aValue = keys.reduce((obj, key) => obj?.[key], a);
          bValue = keys.reduce((obj, key) => obj?.[key], b);
        }

        if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return filtered;
  }, [masterBusinessCases, filters, sortConfig]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedData = filteredAndSortedData.slice(startIndex, startIndex + itemsPerPage);

  const handleSort = (key) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount || 0);
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      active: 'status-badge status-active',
      draft: 'status-badge status-draft',
      completed: 'status-badge status-completed',
      on_hold: 'status-badge status-on-hold'
    };
    return statusClasses[status] || 'status-badge status-draft';
  };

  const getPriorityBadge = (priority) => {
    const priorityClasses = {
      High: 'priority-badge priority-high',
      Medium: 'priority-badge priority-medium',
      Low: 'priority-badge priority-low'
    };
    return priorityClasses[priority] || 'priority-badge priority-medium';
  };

  // Get unique values for filter dropdowns
  const uniqueStatuses = [...new Set(masterBusinessCases.map(mbc => mbc.status))];
  const uniqueCategories = [...new Set(masterBusinessCases.map(mbc => mbc.category))];
  const uniquePriorities = [...new Set(masterBusinessCases.map(mbc => mbc.priority))];

  return (
    <div className="master-bc-list">
      {/* Header with Create Button */}
      <div className="list-header">
        <div className="header-info">
          <h3>Master Business Cases ({filteredAndSortedData.length})</h3>
          <p>Portfolio-level business case management with automatic aggregation</p>
        </div>
        <button className="btn btn-primary" onClick={onCreateNew}>
          <i className="fas fa-plus"></i>
          Create Master BC
        </button>
      </div>

      {/* Filters */}
      <div className="filters-section">
        <div className="filters-row">
          <div className="filter-group">
            <label>Search</label>
            <input
              type="text"
              placeholder="Search by name or description..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="filter-input"
            />
          </div>
          
          <div className="filter-group">
            <label>Status</label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="filter-select"
            >
              <option value="">All Statuses</option>
              {uniqueStatuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>Category</label>
            <select
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
              className="filter-select"
            >
              <option value="">All Categories</option>
              {uniqueCategories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>Priority</label>
            <select
              value={filters.priority}
              onChange={(e) => handleFilterChange('priority', e.target.value)}
              className="filter-select"
            >
              <option value="">All Priorities</option>
              {uniquePriorities.map(priority => (
                <option key={priority} value={priority}>{priority}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="summary-stats">
        <div className="stat-card">
          <div className="stat-value">{filteredAndSortedData.length}</div>
          <div className="stat-label">Total Master BCs</div>
        </div>
        <div className="stat-card">
          <div className="stat-value">
            {filteredAndSortedData.reduce((sum, mbc) => sum + (mbc.aggregatedMetrics?.linkedCount || 0), 0)}
          </div>
          <div className="stat-label">Linked Business Cases</div>
        </div>
        <div className="stat-card">
          <div className="stat-value">
            {formatCurrency(filteredAndSortedData.reduce((sum, mbc) => sum + (mbc.aggregatedMetrics?.totalInvestment || 0), 0))}
          </div>
          <div className="stat-label">Total Investment</div>
        </div>
        <div className="stat-card">
          <div className="stat-value">
            {formatCurrency(filteredAndSortedData.reduce((sum, mbc) => sum + (mbc.aggregatedMetrics?.totalNPV || 0), 0))}
          </div>
          <div className="stat-label">Total NPV</div>
        </div>
      </div>

      {/* Data Table */}
      <div className="table-container">
        <table className="master-bc-table">
          <thead>
            <tr>
              <th onClick={() => handleSort('name')} className="sortable">
                Name {sortConfig.key === 'name' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
              </th>
              <th onClick={() => handleSort('category')} className="sortable">
                Category {sortConfig.key === 'category' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
              </th>
              <th onClick={() => handleSort('status')} className="sortable">
                Status {sortConfig.key === 'status' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
              </th>
              <th onClick={() => handleSort('priority')} className="sortable">
                Priority {sortConfig.key === 'priority' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
              </th>
              <th>
                Connected Program
              </th>
              <th>
                Projects/Epics
              </th>
              <th>
                Business Cases
              </th>
              <th onClick={() => handleSort('aggregatedMetrics.totalInvestment')} className="sortable">
                Investment {sortConfig.key === 'aggregatedMetrics.totalInvestment' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
              </th>
              <th onClick={() => handleSort('aggregatedMetrics.totalNPV')} className="sortable">
                NPV {sortConfig.key === 'aggregatedMetrics.totalNPV' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
              </th>
              <th onClick={() => handleSort('aggregatedMetrics.avgIRR')} className="sortable">
                Avg IRR {sortConfig.key === 'aggregatedMetrics.avgIRR' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
              </th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {paginatedData.map(mbc => (
              <tr key={mbc.id}>
                <td>
                  <div className="name-cell">
                    <strong>{mbc.name}</strong>
                    <div className="description-preview">{mbc.description}</div>
                  </div>
                </td>
                <td>{mbc.category}</td>
                <td>
                  <span className={getStatusBadge(mbc.status)}>
                    {mbc.status}
                  </span>
                </td>
                <td>
                  <span className={getPriorityBadge(mbc.priority)}>
                    {mbc.priority}
                  </span>
                </td>
                <td>
                  {mbc.connectedProgram ? (
                    <div className="connected-program">
                      <div className="program-name">
                        <i className="fas fa-sitemap text-blue-500"></i>
                        <span className="ml-1">{mbc.connectedProgram.name}</span>
                      </div>
                      <div className="program-owner text-sm text-gray-500">
                        Owner: {mbc.connectedProgram.owner}
                      </div>
                    </div>
                  ) : (
                    <span className="text-gray-400">No program linked</span>
                  )}
                </td>
                <td>
                  {mbc.connectedProjects && mbc.connectedProjects.length > 0 ? (
                    <div className="connected-projects">
                      {mbc.connectedProjects.slice(0, 2).map(proj => (
                        <div key={proj.id} className="project-item">
                          <i className={`fas ${proj.type === 'epic' ? 'fa-layer-group' : 'fa-project-diagram'} text-green-500`}></i>
                          <span className="ml-1">{proj.name}</span>
                          <span className="text-xs text-gray-500">({proj.type})</span>
                        </div>
                      ))}
                      {mbc.connectedProjects.length > 2 && (
                        <div className="text-xs text-gray-500">
                          +{mbc.connectedProjects.length - 2} more
                        </div>
                      )}
                    </div>
                  ) : (
                    <span className="text-gray-400">No projects linked</span>
                  )}
                </td>
                <td>
                  {mbc.linkedBusinessCaseNames && mbc.linkedBusinessCaseNames.length > 0 ? (
                    <div className="linked-business-cases">
                      {mbc.linkedBusinessCaseNames.slice(0, 2).map(bc => (
                        <div key={bc.id} className="bc-item">
                          <i className="fas fa-briefcase text-purple-500"></i>
                          <span className="ml-1">{bc.name}</span>
                        </div>
                      ))}
                      {mbc.linkedBusinessCaseNames.length > 2 && (
                        <div className="text-xs text-gray-500">
                          +{mbc.linkedBusinessCaseNames.length - 2} more
                        </div>
                      )}
                    </div>
                  ) : (
                    <span className="text-gray-400">No BCs linked</span>
                  )}
                </td>
                <td className="text-right">
                  {formatCurrency(mbc.aggregatedMetrics?.totalInvestment)}
                </td>
                <td className="text-right">
                  {formatCurrency(mbc.aggregatedMetrics?.totalNPV)}
                </td>
                <td className="text-right">
                  {mbc.aggregatedMetrics?.avgIRR ? `${mbc.aggregatedMetrics.avgIRR.toFixed(1)}%` : '-'}
                </td>
                <td>
                  <div className="action-buttons">
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={() => onViewDetail(mbc)}
                      title="View Details"
                    >
                      <i className="fas fa-eye"></i>
                    </button>
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={() => onEdit(mbc)}
                      title="Edit"
                    >
                      <i className="fas fa-edit"></i>
                    </button>
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={() => onLinkBusinessCases(mbc)}
                      title="Link Business Cases"
                    >
                      <i className="fas fa-link"></i>
                    </button>
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={() => onLinkPrograms(mbc)}
                      title="Link Programs"
                    >
                      <i className="fas fa-sitemap"></i>
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="pagination-section">
        <div className="pagination-info">
          Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredAndSortedData.length)} of {filteredAndSortedData.length} entries
        </div>
        
        <div className="pagination-controls">
          <select
            value={itemsPerPage}
            onChange={(e) => {
              setItemsPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
            className="items-per-page"
          >
            <option value={10}>10 per page</option>
            <option value={25}>25 per page</option>
            <option value={50}>50 per page</option>
            <option value={100}>100 per page</option>
          </select>

          <div className="page-buttons">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="btn btn-sm btn-outline"
            >
              Previous
            </button>
            
            <span className="page-info">
              Page {currentPage} of {totalPages}
            </span>
            
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="btn btn-sm btn-outline"
            >
              Next
            </button>
          </div>
        </div>
      </div>

      {/* Empty State */}
      {filteredAndSortedData.length === 0 && (
        <div className="empty-state">
          <i className="fas fa-sitemap"></i>
          <h3>No Master Business Cases Found</h3>
          <p>
            {masterBusinessCases.length === 0 
              ? "Get started by creating your first Master Business Case"
              : "Try adjusting your filters to see more results"
            }
          </p>
          {masterBusinessCases.length === 0 && (
            <button className="btn btn-primary" onClick={onCreateNew}>
              Create First Master BC
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default MasterBCList;
