"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinancialCalculationService = void 0;
class FinancialCalculationService {
    static calculateNPV(cashFlows, discountRate) {
        return cashFlows.reduce((npv, cashFlow, index) => {
            const discountFactor = Math.pow(1 + discountRate, index);
            return npv + (cashFlow.netCashFlow / discountFactor);
        }, 0);
    }
    static calculateIRR(cashFlows, initialGuess = 0.1) {
        const maxIterations = 100;
        const tolerance = 1e-6;
        let rate = initialGuess;
        for (let i = 0; i < maxIterations; i++) {
            const npv = this.calculateNPVForRate(cashFlows, rate);
            const derivative = this.calculateNPVDerivative(cashFlows, rate);
            if (Math.abs(derivative) < tolerance) {
                break;
            }
            const newRate = rate - (npv / derivative);
            if (Math.abs(newRate - rate) < tolerance) {
                return newRate;
            }
            rate = newRate;
        }
        return rate;
    }
    static calculateNPVForRate(cashFlows, rate) {
        return cashFlows.reduce((npv, cashFlow, index) => {
            const discountFactor = Math.pow(1 + rate, index);
            return npv + (cashFlow.netCashFlow / discountFactor);
        }, 0);
    }
    static calculateNPVDerivative(cashFlows, rate) {
        return cashFlows.reduce((derivative, cashFlow, index) => {
            if (index === 0)
                return derivative;
            const discountFactor = Math.pow(1 + rate, index + 1);
            return derivative - (index * cashFlow.netCashFlow / discountFactor);
        }, 0);
    }
    static calculatePaybackPeriod(cashFlows) {
        let cumulativeCashFlow = 0;
        for (let i = 0; i < cashFlows.length; i++) {
            cumulativeCashFlow += cashFlows[i].netCashFlow;
            if (cumulativeCashFlow >= 0) {
                if (i === 0)
                    return 0;
                const previousCumulative = cumulativeCashFlow - cashFlows[i].netCashFlow;
                const fraction = Math.abs(previousCumulative) / cashFlows[i].netCashFlow;
                return i - 1 + fraction;
            }
        }
        return -1;
    }
    static calculateYieldIndex(cashFlows, discountRate) {
        const initialInvestment = Math.abs(cashFlows[0]?.netCashFlow || 0);
        if (initialInvestment === 0)
            return 0;
        const presentValueOfFutureCashFlows = cashFlows.slice(1).reduce((pv, cashFlow, index) => {
            const discountFactor = Math.pow(1 + discountRate, index + 1);
            return pv + (cashFlow.netCashFlow / discountFactor);
        }, 0);
        return presentValueOfFutureCashFlows / initialInvestment;
    }
    static calculateGrossMargin(revenue, costOfGoodsSold) {
        if (revenue === 0)
            return 0;
        return ((revenue - costOfGoodsSold) / revenue) * 100;
    }
    static calculateSalesAtMaturity(cashFlows) {
        const revenues = cashFlows.map(cf => cf.revenue);
        return Math.max(...revenues);
    }
    static calculateBreakEvenSales(fixedCosts, variableCostPerUnit, pricePerUnit) {
        const contributionMargin = pricePerUnit - variableCostPerUnit;
        if (contributionMargin <= 0)
            return -1;
        return fixedCosts / contributionMargin;
    }
    static generateCashFlow(model, years = 10) {
        const cashFlows = [];
        let cumulativeCashFlow = 0;
        for (let year = 0; year < years; year++) {
            const revenue = this.calculateYearlyRevenue(model, year);
            const costs = this.calculateYearlyCosts(model, year);
            const netCashFlow = revenue - costs;
            cumulativeCashFlow += netCashFlow;
            cashFlows.push({
                year,
                revenue,
                costs,
                netCashFlow,
                cumulativeCashFlow
            });
        }
        return cashFlows;
    }
    static calculateYearlyRevenue(model, year) {
        const { salesStructure } = model;
        let totalRevenue = 0;
        salesStructure.regions.forEach(region => {
            if (region.year <= year) {
                const yearsFromStart = year - region.year;
                const adjustedVolume = region.volume * Math.pow(1 + region.growth, yearsFromStart);
                totalRevenue += adjustedVolume * region.price;
            }
        });
        salesStructure.offers.forEach(offer => {
            if (offer.year <= year) {
                totalRevenue += offer.volume * offer.unitPrice;
            }
        });
        return totalRevenue;
    }
    static calculateYearlyCosts(model, year) {
        const { costStructure, parameters } = model;
        let totalCosts = 0;
        costStructure.capex.forEach(capex => {
            if (capex.year === year) {
                totalCosts += capex.amount;
            }
        });
        costStructure.opex.forEach(opex => {
            if (year >= opex.startYear && year <= opex.endYear) {
                const inflationAdjustment = Math.pow(1 + parameters.inflationRates.production, year - opex.startYear);
                totalCosts += opex.amount * inflationAdjustment;
            }
        });
        costStructure.tools.forEach(tool => {
            const annualDepreciation = (tool.cost * tool.quantity) / tool.lifespan;
            if (year < tool.lifespan) {
                totalCosts += annualDepreciation;
            }
        });
        costStructure.machinery.forEach(machinery => {
            const annualDepreciation = machinery.cost / machinery.lifespan;
            if (year < machinery.lifespan) {
                totalCosts += annualDepreciation + machinery.maintenanceCost;
            }
        });
        return totalCosts;
    }
    static calculateFinancialResults(model) {
        const cashFlow = this.generateCashFlow(model);
        const discountRate = model.parameters.discountRate;
        const npv = this.calculateNPV(cashFlow, discountRate);
        const irr = this.calculateIRR(cashFlow);
        const paybackPeriod = this.calculatePaybackPeriod(cashFlow);
        const yieldIndex = this.calculateYieldIndex(cashFlow, discountRate);
        const totalRevenue = cashFlow.reduce((sum, cf) => sum + cf.revenue, 0);
        const totalCosts = cashFlow.reduce((sum, cf) => sum + cf.costs, 0);
        const grossMargin = this.calculateGrossMargin(totalRevenue, totalCosts);
        const salesAtMaturity = this.calculateSalesAtMaturity(cashFlow);
        const breakEvenSales = this.calculateBreakEvenSales(totalCosts * 0.3, totalCosts * 0.7 / totalRevenue, totalRevenue / (totalRevenue / 100));
        return {
            irr,
            npv,
            paybackPeriod,
            yieldIndex,
            grossMargin,
            salesAtMaturity,
            breakEvenSales,
            cashFlow
        };
    }
    static calculateBusinessCaseMetrics(businessCaseData) {
        const { timeframe, financialData } = businessCaseData;
        const discountRate = 0.10;
        const cashFlow = [];
        for (let year = timeframe.startYear; year <= timeframe.endYear; year++) {
            const capexItem = financialData.capex.find(item => item.year === year);
            const opexItem = financialData.opex.find(item => item.year === year);
            const revenueItem = financialData.revenue?.find(item => item.year === year);
            const capex = capexItem ? capexItem.amount : 0;
            const opex = opexItem ? opexItem.amount : 0;
            const revenue = revenueItem ? revenueItem.amount : 0;
            const costs = capex + opex;
            const netCashFlow = revenue - costs;
            cashFlow.push({
                year: year - timeframe.startYear,
                revenue,
                costs,
                netCashFlow,
                cumulativeCashFlow: 0
            });
        }
        let cumulative = 0;
        cashFlow.forEach(cf => {
            cumulative += cf.netCashFlow;
            cf.cumulativeCashFlow = cumulative;
        });
        const npv = this.calculateNPV(cashFlow, discountRate);
        const irr = this.calculateIRR(cashFlow);
        const paybackPeriod = this.calculatePaybackPeriod(cashFlow);
        const totalRevenue = cashFlow.reduce((sum, cf) => sum + cf.revenue, 0);
        const totalCosts = cashFlow.reduce((sum, cf) => sum + cf.costs, 0);
        const grossMargin = totalRevenue > 0 ? this.calculateGrossMargin(totalRevenue, totalCosts) : 0;
        const commercialMargin = this.calculateCommercialMargin(totalRevenue, totalCosts, financialData);
        return {
            irr,
            npv,
            paybackPeriod,
            grossMargin,
            commercialMargin
        };
    }
    static calculateCommercialMargin(totalRevenue, totalCosts, financialData) {
        if (totalRevenue === 0)
            return 0;
        const baseMargin = ((totalRevenue - totalCosts) / totalRevenue) * 100;
        const totalCapex = financialData.capex.reduce((sum, item) => sum + item.amount, 0);
        const riskAdjustment = totalCapex > 1000000 ? -5 : 0;
        const strategicBonus = financialData.capex.length > 1 ? 2 : 0;
        return baseMargin + riskAdjustment + strategicBonus;
    }
}
exports.FinancialCalculationService = FinancialCalculationService;
//# sourceMappingURL=financialCalculations.js.map