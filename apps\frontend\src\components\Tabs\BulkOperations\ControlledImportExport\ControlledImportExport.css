.controlled-import-export {
  padding: 24px;
}

/* Operation Selector */
.operation-selector {
  margin-bottom: 32px;
}

.operation-selector h3 {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.operation-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.operation-btn {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
}

.operation-btn:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.operation-btn.active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.operation-btn i {
  font-size: 24px;
  color: #6b7280;
  min-width: 24px;
}

.operation-btn.active i {
  color: #3b82f6;
}

.btn-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.btn-description {
  display: block;
  font-size: 14px;
  color: #6b7280;
}

/* Configuration Steps */
.configuration-steps {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.config-step {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 14px;
}

.step-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

/* File Selector */
.file-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-upload-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #3b82f6;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
  max-width: 200px;
}

.file-upload-btn:hover {
  background: #2563eb;
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-info p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.selected-file-info {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #374151;
}

/* Preview Section */
.preview-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
  max-width: 250px;
}

.preview-btn:hover:not(:disabled) {
  background: #059669;
}

.preview-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .controlled-import-export {
    padding: 16px;
  }
  
  .operation-buttons {
    grid-template-columns: 1fr;
  }
  
  .operation-btn {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .step-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .selected-file-info {
    flex-direction: column;
    gap: 4px;
  }
}
