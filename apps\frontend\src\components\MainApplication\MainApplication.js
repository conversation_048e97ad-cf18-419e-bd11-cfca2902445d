import React, { useState } from 'react';
import TabNavigation from './TabNavigation';
import TabContent from './TabContent';

const MainApplication = () => {
  const [activeTab, setActiveTab] = useState('executive-dashboard');

  const tabs = [
    {
      id: 'executive-dashboard',
      name: 'Executive Dashboard',
      icon: 'fas fa-tachometer-alt',
      description: 'Unified strategic insights and portfolio performance'
    },
    {
      id: 'idea-creation',
      name: 'Idea Creation',
      icon: 'fas fa-lightbulb',
      description: 'Create and manage ideas'
    },
    {
      id: 'idea-approval',
      name: 'Idea Approval',
      icon: 'fas fa-check-circle',
      description: 'Review and approve submitted ideas'
    },
    {
      id: 'pulseboard',
      name: 'Pulseboard',
      icon: 'fas fa-table',
      description: 'Excel-like interface for comprehensive request tracking and management'
    },
    {
      id: 'create-bc',
      name: 'Create Business Case',
      icon: 'fas fa-plus-circle',
      description: 'Develop comprehensive business cases'
    },
    {
      id: 'list-bc',
      name: 'Business Cases',
      icon: 'fas fa-briefcase',
      description: 'View and manage all business cases'
    },
    {
      id: 'master-bc',
      name: 'Master Business Cases',
      icon: 'fas fa-sitemap',
      description: 'Portfolio-level business case management'
    },
    {
      id: 'projects-epics',
      name: 'Projects & Epics',
      icon: 'fas fa-project-diagram',
      description: 'Project and epic management'
    },
    {
      id: 'programs',
      name: 'Programs',
      icon: 'fas fa-layer-group',
      description: 'Program management and oversight'
    },
    {
      id: 'relationships',
      name: 'Relationships Dashboard',
      icon: 'fas fa-network-wired',
      description: 'Visualize entity relationships'
    },
    {
      id: 'bulk-operations',
      name: 'Bulk Operations Center',
      icon: 'fas fa-tasks',
      description: 'Bulk import/export operations'
    },
    {
      id: 'business-architecture',
      name: 'Financial Formulas',
      icon: 'fas fa-calculator',
      description: 'Financial calculation formulas and documentation'
    },
    {
      id: 'debug-status',
      name: 'Data Status',
      icon: 'fas fa-bug',
      description: 'Debug data loading status and troubleshooting'
    }
  ];

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Tab Navigation */}
      <TabNavigation 
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={handleTabChange}
      />

      {/* Tab Content */}
      <div className="p-6">
        <TabContent 
          activeTab={activeTab}
          tabs={tabs}
        />
      </div>
    </div>
  );
};

export default MainApplication;
