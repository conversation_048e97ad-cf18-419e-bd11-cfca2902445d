import React, { useState, useEffect } from 'react';

const BusinessCaseForm = ({ onSubmit, onCancel, initialData = null, promotedIdea = null }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    businessUnit: '',
    startYear: new Date().getFullYear(),
    endYear: new Date().getFullYear() + 3,
    problemStatement: '',
    proposedSolution: '',
    strategicAlignment: '',
    successCriteria: '',
    assumptions: '',
    risks: '',
    tags: '',
    // Financial data
    capexItems: [{ year: new Date().getFullYear(), description: '', amount: 0 }],
    opexItems: [{ year: new Date().getFullYear(), description: '', amount: 0 }],
    revenueItems: [{ year: new Date().getFullYear(), description: '', amount: 0 }],
    // Metadata
    promotedFromIdea: promotedIdea?.id || null,
    status: 'draft'
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with promoted idea data
  useEffect(() => {
    if (promotedIdea) {
      setFormData(prev => ({
        ...prev,
        name: promotedIdea.title || '',
        description: promotedIdea.description || '',
        businessUnit: promotedIdea.businessUnit || '',
        problemStatement: promotedIdea.problemStatement || '',
        proposedSolution: promotedIdea.opportunityDescription || '',
        tags: promotedIdea.tags?.join(', ') || '',
        promotedFromIdea: promotedIdea.id
      }));
    }
  }, [promotedIdea]);

  // Initialize form with existing data for editing
  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({
        ...prev,
        ...initialData,
        tags: Array.isArray(initialData.tags) ? initialData.tags.join(', ') : initialData.tags || ''
      }));
    }
  }, [initialData]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const addFinancialItem = (type) => {
    const newItem = {
      year: formData.startYear,
      description: '',
      amount: 0
    };
    
    setFormData(prev => ({
      ...prev,
      [`${type}Items`]: [...prev[`${type}Items`], newItem]
    }));
  };

  const updateFinancialItem = (type, index, field, value) => {
    setFormData(prev => ({
      ...prev,
      [`${type}Items`]: prev[`${type}Items`].map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const removeFinancialItem = (type, index) => {
    setFormData(prev => ({
      ...prev,
      [`${type}Items`]: prev[`${type}Items`].filter((_, i) => i !== index)
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Business case name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.businessUnit.trim()) {
      newErrors.businessUnit = 'Business unit is required';
    }

    if (formData.endYear <= formData.startYear) {
      newErrors.endYear = 'End year must be after start year';
    }

    if (!formData.problemStatement.trim()) {
      newErrors.problemStatement = 'Problem statement is required';
    }

    if (!formData.proposedSolution.trim()) {
      newErrors.proposedSolution = 'Proposed solution is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Calculate totals
      const totalCapex = formData.capexItems.reduce((sum, item) => sum + Number(item.amount), 0);
      const totalOpex = formData.opexItems.reduce((sum, item) => sum + Number(item.amount), 0);
      const totalRevenue = formData.revenueItems.reduce((sum, item) => sum + Number(item.amount), 0);

      // Process data in the format expected by backend
      const processedData = {
        name: formData.name,
        description: formData.description,
        businessUnit: formData.businessUnit,
        timeframe: {
          startYear: parseInt(formData.startYear),
          endYear: parseInt(formData.endYear)
        },
        financialData: {
          capex: formData.capexItems.map(item => ({
            year: parseInt(item.year),
            amount: Number(item.amount),
            description: item.description
          })),
          opex: formData.opexItems.map(item => ({
            year: parseInt(item.year),
            amount: Number(item.amount),
            description: item.description
          })),
          revenue: formData.revenueItems.map(item => ({
            year: parseInt(item.year),
            amount: Number(item.amount),
            description: item.description
          })),
          totalCapex: totalCapex,
          totalOpex: totalOpex
        },
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        problemStatement: formData.problemStatement,
        proposedSolution: formData.proposedSolution,
        strategicAlignment: formData.strategicAlignment,
        successCriteria: formData.successCriteria,
        assumptions: formData.assumptions,
        risks: formData.risks,
        createdAt: initialData?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await onSubmit(processedData);
    } catch (error) {
      console.error('Error submitting business case:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const businessUnits = [
    'Technology',
    'Marketing',
    'Sales',
    'Operations',
    'Finance',
    'Human Resources',
    'Research & Development',
    'Customer Service'
  ];

  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: 10 }, (_, i) => currentYear + i);

  return (
    <div className="max-w-6xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Header */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {initialData ? 'Edit Business Case' : 'Create New Business Case'}
              </h2>
              {promotedIdea && (
                <p className="text-sm text-green-600 mt-1">
                  <i className="fas fa-lightbulb mr-1"></i>
                  Promoted from idea: "{promotedIdea.title}"
                </p>
              )}
            </div>
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {isSubmitting ? (
                  <>
                    <i className="fas fa-spinner fa-spin mr-2"></i>
                    Saving...
                  </>
                ) : (
                  <>
                    <i className="fas fa-save mr-2"></i>
                    {initialData ? 'Update' : 'Create'} Business Case
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Case Name *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter business case name"
              />
              {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Unit *
              </label>
              <select
                name="businessUnit"
                value={formData.businessUnit}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.businessUnit ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select business unit</option>
                {businessUnits.map(unit => (
                  <option key={unit} value={unit}>{unit}</option>
                ))}
              </select>
              {errors.businessUnit && <p className="text-red-500 text-sm mt-1">{errors.businessUnit}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Start Year *
              </label>
              <select
                name="startYear"
                value={formData.startYear}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {yearOptions.map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                End Year *
              </label>
              <select
                name="endYear"
                value={formData.endYear}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.endYear ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                {yearOptions.map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
              {errors.endYear && <p className="text-red-500 text-sm mt-1">{errors.endYear}</p>}
            </div>
          </div>

          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description *
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.description ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Provide a comprehensive description of the business case"
            />
            {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
          </div>

          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tags
            </label>
            <input
              type="text"
              name="tags"
              value={formData.tags}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter tags separated by commas (e.g., digital transformation, cost reduction)"
            />
          </div>
        </div>

        {/* Business Case Details */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Business Case Details</h3>

          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Problem Statement *
              </label>
              <textarea
                name="problemStatement"
                value={formData.problemStatement}
                onChange={handleInputChange}
                rows={4}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.problemStatement ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Clearly describe the problem or opportunity this business case addresses"
              />
              {errors.problemStatement && <p className="text-red-500 text-sm mt-1">{errors.problemStatement}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Proposed Solution *
              </label>
              <textarea
                name="proposedSolution"
                value={formData.proposedSolution}
                onChange={handleInputChange}
                rows={4}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.proposedSolution ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Describe the proposed solution and how it addresses the problem"
              />
              {errors.proposedSolution && <p className="text-red-500 text-sm mt-1">{errors.proposedSolution}</p>}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Strategic Alignment
                </label>
                <textarea
                  name="strategicAlignment"
                  value={formData.strategicAlignment}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="How does this align with company strategy?"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Success Criteria
                </label>
                <textarea
                  name="successCriteria"
                  value={formData.successCriteria}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Define measurable success criteria"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Key Assumptions
                </label>
                <textarea
                  name="assumptions"
                  value={formData.assumptions}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="List key assumptions underlying this business case"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Risks & Mitigation
                </label>
                <textarea
                  name="risks"
                  value={formData.risks}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Identify risks and mitigation strategies"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Financial Information */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Financial Information</h3>

          {/* CAPEX Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-md font-medium text-gray-900">Capital Expenditure (CAPEX)</h4>
              <button
                type="button"
                onClick={() => addFinancialItem('capex')}
                className="px-3 py-1 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 text-sm"
              >
                <i className="fas fa-plus mr-1"></i>
                Add CAPEX Item
              </button>
            </div>
            <div className="space-y-3">
              {formData.capexItems.map((item, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-4 gap-3 items-end">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Year</label>
                    <select
                      value={item.year}
                      onChange={(e) => updateFinancialItem('capex', index, 'year', parseInt(e.target.value))}
                      className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                    >
                      {yearOptions.map(year => (
                        <option key={year} value={year}>{year}</option>
                      ))}
                    </select>
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-xs font-medium text-gray-700 mb-1">Description</label>
                    <input
                      type="text"
                      value={item.description}
                      onChange={(e) => updateFinancialItem('capex', index, 'description', e.target.value)}
                      className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                      placeholder="Equipment, software, etc."
                    />
                  </div>
                  <div className="flex items-end space-x-2">
                    <div className="flex-1">
                      <label className="block text-xs font-medium text-gray-700 mb-1">Amount ($)</label>
                      <input
                        type="number"
                        value={item.amount}
                        onChange={(e) => updateFinancialItem('capex', index, 'amount', parseFloat(e.target.value) || 0)}
                        className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                        min="0"
                        step="0.01"
                      />
                    </div>
                    {formData.capexItems.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeFinancialItem('capex', index)}
                        className="px-2 py-1 text-red-600 hover:bg-red-50 rounded"
                      >
                        <i className="fas fa-trash text-sm"></i>
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* OPEX Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-md font-medium text-gray-900">Operating Expenditure (OPEX)</h4>
              <button
                type="button"
                onClick={() => addFinancialItem('opex')}
                className="px-3 py-1 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 text-sm"
              >
                <i className="fas fa-plus mr-1"></i>
                Add OPEX Item
              </button>
            </div>
            <div className="space-y-3">
              {formData.opexItems.map((item, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-4 gap-3 items-end">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Year</label>
                    <select
                      value={item.year}
                      onChange={(e) => updateFinancialItem('opex', index, 'year', parseInt(e.target.value))}
                      className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                    >
                      {yearOptions.map(year => (
                        <option key={year} value={year}>{year}</option>
                      ))}
                    </select>
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-xs font-medium text-gray-700 mb-1">Description</label>
                    <input
                      type="text"
                      value={item.description}
                      onChange={(e) => updateFinancialItem('opex', index, 'description', e.target.value)}
                      className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                      placeholder="Salaries, maintenance, etc."
                    />
                  </div>
                  <div className="flex items-end space-x-2">
                    <div className="flex-1">
                      <label className="block text-xs font-medium text-gray-700 mb-1">Amount ($)</label>
                      <input
                        type="number"
                        value={item.amount}
                        onChange={(e) => updateFinancialItem('opex', index, 'amount', parseFloat(e.target.value) || 0)}
                        className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                        min="0"
                        step="0.01"
                      />
                    </div>
                    {formData.opexItems.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeFinancialItem('opex', index)}
                        className="px-2 py-1 text-red-600 hover:bg-red-50 rounded"
                      >
                        <i className="fas fa-trash text-sm"></i>
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Revenue Section */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-md font-medium text-gray-900">Revenue Projections</h4>
              <button
                type="button"
                onClick={() => addFinancialItem('revenue')}
                className="px-3 py-1 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 text-sm"
              >
                <i className="fas fa-plus mr-1"></i>
                Add Revenue Item
              </button>
            </div>
            <div className="space-y-3">
              {formData.revenueItems.map((item, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-4 gap-3 items-end">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Year</label>
                    <select
                      value={item.year}
                      onChange={(e) => updateFinancialItem('revenue', index, 'year', parseInt(e.target.value))}
                      className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                    >
                      {yearOptions.map(year => (
                        <option key={year} value={year}>{year}</option>
                      ))}
                    </select>
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-xs font-medium text-gray-700 mb-1">Description</label>
                    <input
                      type="text"
                      value={item.description}
                      onChange={(e) => updateFinancialItem('revenue', index, 'description', e.target.value)}
                      className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                      placeholder="Sales, cost savings, etc."
                    />
                  </div>
                  <div className="flex items-end space-x-2">
                    <div className="flex-1">
                      <label className="block text-xs font-medium text-gray-700 mb-1">Amount ($)</label>
                      <input
                        type="number"
                        value={item.amount}
                        onChange={(e) => updateFinancialItem('revenue', index, 'amount', parseFloat(e.target.value) || 0)}
                        className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                        min="0"
                        step="0.01"
                      />
                    </div>
                    {formData.revenueItems.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeFinancialItem('revenue', index)}
                        className="px-2 py-1 text-red-600 hover:bg-red-50 rounded"
                      >
                        <i className="fas fa-trash text-sm"></i>
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Financial Summary */}
          <div className="mt-6 bg-gray-50 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">Financial Summary</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="text-lg font-semibold text-blue-600">
                  ${formData.capexItems.reduce((sum, item) => sum + Number(item.amount), 0).toLocaleString()}
                </div>
                <div className="text-gray-600">Total CAPEX</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-green-600">
                  ${formData.opexItems.reduce((sum, item) => sum + Number(item.amount), 0).toLocaleString()}
                </div>
                <div className="text-gray-600">Total OPEX</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-purple-600">
                  ${formData.revenueItems.reduce((sum, item) => sum + Number(item.amount), 0).toLocaleString()}
                </div>
                <div className="text-gray-600">Total Revenue</div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default BusinessCaseForm;
