"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessCase = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const businessCaseSchema = new mongoose_1.Schema({
    name: {
        type: String,
        required: [true, 'Business case name is required'],
        trim: true,
        maxlength: [200, 'Name cannot exceed 200 characters']
    },
    description: {
        type: String,
        trim: true,
        maxlength: [1000, 'Description cannot exceed 1000 characters']
    },
    tags: [{
            type: String,
            trim: true
        }],
    businessUnit: {
        type: String,
        trim: true,
        maxlength: [100, 'Business unit cannot exceed 100 characters']
    },
    timeframe: {
        startYear: {
            type: Number,
            required: [true, 'Start year is required'],
            min: [2020, 'Start year must be 2020 or later'],
            max: [2050, 'Start year cannot exceed 2050']
        },
        endYear: {
            type: Number,
            required: [true, 'End year is required'],
            min: [2020, 'End year must be 2020 or later'],
            max: [2050, 'End year cannot exceed 2050']
        }
    },
    financialData: {
        capex: [{
                year: {
                    type: Number,
                    required: true
                },
                amount: {
                    type: Number,
                    required: true,
                    min: [0, 'CAPEX amount cannot be negative']
                },
                description: {
                    type: String,
                    trim: true
                }
            }],
        opex: [{
                year: {
                    type: Number,
                    required: true
                },
                amount: {
                    type: Number,
                    required: true,
                    min: [0, 'OPEX amount cannot be negative']
                },
                description: {
                    type: String,
                    trim: true
                }
            }],
        revenue: [{
                year: {
                    type: Number,
                    required: true
                },
                amount: {
                    type: Number,
                    required: true,
                    min: [0, 'Revenue amount cannot be negative']
                },
                description: {
                    type: String,
                    trim: true
                }
            }],
        totalCapex: {
            type: Number,
            required: true,
            min: [0, 'Total CAPEX cannot be negative']
        },
        totalOpex: {
            type: Number,
            required: true,
            min: [0, 'Total OPEX cannot be negative']
        }
    },
    calculatedMetrics: {
        irr: Number,
        npv: Number,
        paybackPeriod: Number,
        grossMargin: Number,
        commercialMargin: Number
    },
    status: {
        type: String,
        enum: ['draft', 'active', 'completed', 'archived'],
        default: 'draft'
    },
    createdBy: {
        type: String,
        required: [true, 'Creator is required']
    },
    lastModifiedBy: {
        type: String,
        required: [true, 'Last modifier is required']
    }
}, {
    timestamps: true
});
businessCaseSchema.pre('save', function (next) {
    if (this.timeframe.endYear <= this.timeframe.startYear) {
        next(new Error('End year must be after start year'));
    }
    next();
});
businessCaseSchema.pre('save', function (next) {
    this.financialData.totalCapex = this.financialData.capex.reduce((sum, item) => sum + item.amount, 0);
    this.financialData.totalOpex = this.financialData.opex.reduce((sum, item) => sum + item.amount, 0);
    next();
});
businessCaseSchema.index({ name: 'text', description: 'text', tags: 'text' });
businessCaseSchema.index({ createdBy: 1 });
businessCaseSchema.index({ status: 1 });
businessCaseSchema.index({ 'timeframe.startYear': 1, 'timeframe.endYear': 1 });
exports.BusinessCase = mongoose_1.default.model('BusinessCase', businessCaseSchema);
//# sourceMappingURL=BusinessCase.js.map