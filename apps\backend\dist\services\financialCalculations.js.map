{"version": 3, "file": "financialCalculations.js", "sourceRoot": "", "sources": ["../../src/services/financialCalculations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,2BAA2B;IAItC,MAAM,CAAC,YAAY,CAAC,SAAyB,EAAE,YAAoB;QACjE,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE;YAC/C,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC,WAAW,GAAG,cAAc,CAAC,CAAC;QACvD,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC;IAKD,MAAM,CAAC,YAAY,CAAC,SAAyB,EAAE,eAAuB,GAAG;QACvE,MAAM,aAAa,GAAG,GAAG,CAAC;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC;QACvB,IAAI,IAAI,GAAG,YAAY,CAAC;QAExB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,MAAM,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YACtD,MAAM,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAEhE,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,SAAS,EAAE,CAAC;gBACrC,MAAM;YACR,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC;YAE1C,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,SAAS,EAAE,CAAC;gBACzC,OAAO,OAAO,CAAC;YACjB,CAAC;YAED,IAAI,GAAG,OAAO,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,MAAM,CAAC,mBAAmB,CAAC,SAAyB,EAAE,IAAY;QACxE,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE;YAC/C,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC,WAAW,GAAG,cAAc,CAAC,CAAC;QACvD,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC;IAKO,MAAM,CAAC,sBAAsB,CAAC,SAAyB,EAAE,IAAY;QAC3E,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE;YACtD,IAAI,KAAK,KAAK,CAAC;gBAAE,OAAO,UAAU,CAAC;YACnC,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YACrD,OAAO,UAAU,GAAG,CAAC,KAAK,GAAG,QAAQ,CAAC,WAAW,GAAG,cAAc,CAAC,CAAC;QACtE,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC;IAKD,MAAM,CAAC,sBAAsB,CAAC,SAAyB;QACrD,IAAI,kBAAkB,GAAG,CAAC,CAAC;QAE3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,kBAAkB,IAAI,SAAS,CAAC,CAAC,CAAE,CAAC,WAAW,CAAC;YAEhD,IAAI,kBAAkB,IAAI,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,KAAK,CAAC;oBAAE,OAAO,CAAC,CAAC;gBAGtB,MAAM,kBAAkB,GAAG,kBAAkB,GAAG,SAAS,CAAC,CAAC,CAAE,CAAC,WAAW,CAAC;gBAC1E,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,GAAG,SAAS,CAAC,CAAC,CAAE,CAAC,WAAW,CAAC;gBAC1E,OAAO,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC;IAKD,MAAM,CAAC,mBAAmB,CAAC,SAAyB,EAAE,YAAoB;QACxE,MAAM,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,WAAW,IAAI,CAAC,CAAC,CAAC;QACnE,IAAI,iBAAiB,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEtC,MAAM,6BAA6B,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE;YACtF,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YAC7D,OAAO,EAAE,GAAG,CAAC,QAAQ,CAAC,WAAW,GAAG,cAAc,CAAC,CAAC;QACtD,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,OAAO,6BAA6B,GAAG,iBAAiB,CAAC;IAC3D,CAAC;IAKD,MAAM,CAAC,oBAAoB,CAAC,OAAe,EAAE,eAAuB;QAClE,IAAI,OAAO,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAC5B,OAAO,CAAC,CAAC,OAAO,GAAG,eAAe,CAAC,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC;IACvD,CAAC;IAKD,MAAM,CAAC,wBAAwB,CAAC,SAAyB;QAEvD,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAKD,MAAM,CAAC,uBAAuB,CAAC,UAAkB,EAAE,mBAA2B,EAAE,YAAoB;QAClG,MAAM,kBAAkB,GAAG,YAAY,GAAG,mBAAmB,CAAC;QAC9D,IAAI,kBAAkB,IAAI,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC;QACvC,OAAO,UAAU,GAAG,kBAAkB,CAAC;IACzC,CAAC;IAKD,MAAM,CAAC,gBAAgB,CAAC,KAAqB,EAAE,QAAgB,EAAE;QAC/D,MAAM,SAAS,GAAmB,EAAE,CAAC;QACrC,IAAI,kBAAkB,GAAG,CAAC,CAAC;QAE3B,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC;YACxC,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACzD,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACrD,MAAM,WAAW,GAAG,OAAO,GAAG,KAAK,CAAC;YACpC,kBAAkB,IAAI,WAAW,CAAC;YAElC,SAAS,CAAC,IAAI,CAAC;gBACb,IAAI;gBACJ,OAAO;gBACP,KAAK;gBACL,WAAW;gBACX,kBAAkB;aACnB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAKO,MAAM,CAAC,sBAAsB,CAAC,KAAqB,EAAE,IAAY;QACvE,MAAM,EAAE,cAAc,EAAE,GAAG,KAAK,CAAC;QACjC,IAAI,YAAY,GAAG,CAAC,CAAC;QAGrB,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACtC,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;gBACxB,MAAM,cAAc,GAAG,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;gBAC1C,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;gBACnF,YAAY,IAAI,cAAc,GAAG,MAAM,CAAC,KAAK,CAAC;YAChD,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACpC,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;gBACvB,YAAY,IAAI,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC;YACjD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACtB,CAAC;IAKO,MAAM,CAAC,oBAAoB,CAAC,KAAqB,EAAE,IAAY;QACrE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,KAAK,CAAC;QAC5C,IAAI,UAAU,GAAG,CAAC,CAAC;QAGnB,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAClC,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACxB,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAChC,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACnD,MAAM,mBAAmB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,cAAc,CAAC,UAAU,EAAE,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;gBACtG,UAAU,IAAI,IAAI,CAAC,MAAM,GAAG,mBAAmB,CAAC;YAClD,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACjC,MAAM,kBAAkB,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;YACvE,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACzB,UAAU,IAAI,kBAAkB,CAAC;YACnC,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAC1C,MAAM,kBAAkB,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,QAAQ,CAAC;YAC/D,IAAI,IAAI,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC9B,UAAU,IAAI,kBAAkB,GAAG,SAAS,CAAC,eAAe,CAAC;YAC/D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACpB,CAAC;IAKD,MAAM,CAAC,yBAAyB,CAAC,KAAqB;QACpD,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC9C,MAAM,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC;QAEnD,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QACtD,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACxC,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QAEpE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QACvE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACnE,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QAExE,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAChE,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CACjD,UAAU,GAAG,GAAG,EAChB,UAAU,GAAG,GAAG,GAAG,YAAY,EAC/B,YAAY,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,CACpC,CAAC;QAEF,OAAO;YACL,GAAG;YACH,GAAG;YACH,aAAa;YACb,UAAU;YACV,WAAW;YACX,eAAe;YACf,cAAc;YACd,QAAQ;SACT,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,4BAA4B,CAAC,gBAOnC;QACC,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,gBAAgB,CAAC;QACtD,MAAM,YAAY,GAAG,IAAI,CAAC;QAG1B,MAAM,QAAQ,GAAmB,EAAE,CAAC;QACpC,KAAK,IAAI,IAAI,GAAG,SAAS,CAAC,SAAS,EAAE,IAAI,IAAI,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC;YACvE,MAAM,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;YACvE,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;YACrE,MAAM,WAAW,GAAG,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;YAE5E,MAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;YAC3B,MAAM,WAAW,GAAG,OAAO,GAAG,KAAK,CAAC;YAEpC,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,IAAI,GAAG,SAAS,CAAC,SAAS;gBAChC,OAAO;gBACP,KAAK;gBACL,WAAW;gBACX,kBAAkB,EAAE,CAAC;aACtB,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YACpB,UAAU,IAAI,EAAE,CAAC,WAAW,CAAC;YAC7B,EAAE,CAAC,kBAAkB,GAAG,UAAU,CAAC;QACrC,CAAC,CAAC,CAAC;QAGH,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QACtD,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACxC,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAE5D,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QACvE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACnE,MAAM,WAAW,GAAG,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAG/F,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;QAEjG,OAAO;YACL,GAAG;YACH,GAAG;YACH,aAAa;YACb,WAAW;YACX,gBAAgB;SACjB,CAAC;IACJ,CAAC;IAKO,MAAM,CAAC,yBAAyB,CACtC,YAAoB,EACpB,UAAkB,EAClB,aAAkB;QAElB,IAAI,YAAY,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QASjC,MAAM,UAAU,GAAG,CAAC,CAAC,YAAY,GAAG,UAAU,CAAC,GAAG,YAAY,CAAC,GAAG,GAAG,CAAC;QAGtE,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,IAAS,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAChG,MAAM,cAAc,GAAG,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAGrD,MAAM,cAAc,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9D,OAAO,UAAU,GAAG,cAAc,GAAG,cAAc,CAAC;IACtD,CAAC;CACF;AAtVD,kEAsVC"}