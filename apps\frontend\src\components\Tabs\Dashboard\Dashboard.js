import React, { useState, useEffect } from 'react';
import { useData } from '../../../context/DataContext';
import KPICards from './KPICards';
import ChartsSection from './ChartsSection';
import RecentActivity from './RecentActivity';

const Dashboard = () => {
  const { dashboardData, loading, loadDashboardData } = useData();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    // Load dashboard data when component mounts
    if (!dashboardData.kpis || Object.keys(dashboardData.kpis).length === 0) {
      loadDashboardData();
    }
  }, [dashboardData.kpis, loadDashboardData]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  if (loading.dashboard && !dashboardData.kpis) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Strategic Dashboard</h2>
          <p className="text-gray-600">Real-time insights and key performance indicators</p>
        </div>
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center"
        >
          <i className={`fas fa-sync-alt mr-2 ${refreshing ? 'fa-spin' : ''}`}></i>
          Refresh
        </button>
      </div>

      {/* KPI Cards */}
      <KPICards kpis={dashboardData.kpis} />

      {/* Charts Section */}
      <ChartsSection charts={dashboardData.charts} />

      {/* Recent Activity */}
      <RecentActivity activities={dashboardData.recentActivity} />

      {/* Insights */}
      {dashboardData.insights && dashboardData.insights.length > 0 && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">
            <i className="fas fa-lightbulb mr-2"></i>
            Strategic Insights
          </h3>
          <div className="space-y-3">
            {dashboardData.insights.map((insight, index) => (
              <div key={index} className="flex items-start">
                <i className="fas fa-arrow-right text-blue-600 mt-1 mr-3"></i>
                <p className="text-blue-800">{insight}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <i className="fas fa-plus-circle text-green-600 text-2xl mb-2"></i>
            <h4 className="font-medium text-gray-900">Create Business Case</h4>
            <p className="text-sm text-gray-600">Start a new business case</p>
          </button>
          <button className="p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <i className="fas fa-sitemap text-purple-600 text-2xl mb-2"></i>
            <h4 className="font-medium text-gray-900">Master Business Case</h4>
            <p className="text-sm text-gray-600">Create portfolio view</p>
          </button>
          <button className="p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <i className="fas fa-file-excel text-blue-600 text-2xl mb-2"></i>
            <h4 className="font-medium text-gray-900">Bulk Import</h4>
            <p className="text-sm text-gray-600">Import from Excel</p>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
