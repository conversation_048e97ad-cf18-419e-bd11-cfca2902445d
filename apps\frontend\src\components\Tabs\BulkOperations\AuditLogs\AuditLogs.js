import React, { useState } from 'react';

const AuditLogs = () => {
  const [activeTab, setActiveTab] = useState('logs');
  const [filterType, setFilterType] = useState('all');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });

  const tabs = [
    { id: 'logs', name: '<PERSON>t Logs', icon: 'fas fa-list' },
    { id: 'rollback', name: 'Rollback Operations', icon: 'fas fa-undo' },
    { id: 'monitoring', name: 'Real-time Monitoring', icon: 'fas fa-eye' }
  ];

  const mockAuditLogs = [
    {
      id: 'log_001',
      timestamp: '2024-01-15T10:30:00Z',
      operation: 'Import',
      dataType: 'OPEX',
      targetObject: 'Business Cases',
      user: '<EMAIL>',
      status: 'Success',
      recordsAffected: 150,
      details: 'OPEX data imported for Q1 2024 business cases'
    },
    {
      id: 'log_002',
      timestamp: '2024-01-15T09:15:00Z',
      operation: 'Export',
      dataType: 'Financial Metrics',
      targetObject: 'Master Business Cases',
      user: '<EMAIL>',
      status: 'Success',
      recordsAffected: 25,
      details: 'Financial metrics exported to Excel for executive review'
    },
    {
      id: 'log_003',
      timestamp: '2024-01-15T08:45:00Z',
      operation: 'JIRA Sync',
      dataType: 'Epic Status',
      targetObject: 'Projects',
      user: '<EMAIL>',
      status: 'Warning',
      recordsAffected: 8,
      details: '2 epics failed to sync due to permission issues'
    },
    {
      id: 'log_004',
      timestamp: '2024-01-14T16:20:00Z',
      operation: 'Import',
      dataType: 'Resource Plan',
      targetObject: 'Programs',
      user: '<EMAIL>',
      status: 'Failed',
      recordsAffected: 0,
      details: 'Import failed due to invalid file format'
    }
  ];

  const mockRollbackOperations = [
    {
      id: 'rollback_001',
      originalOperation: 'log_001',
      timestamp: '2024-01-15T10:30:00Z',
      operation: 'OPEX Import',
      status: 'Available',
      expiresAt: '2024-01-16T10:30:00Z',
      recordsAffected: 150
    },
    {
      id: 'rollback_002',
      originalOperation: 'log_002',
      timestamp: '2024-01-15T09:15:00Z',
      operation: 'Financial Metrics Export',
      status: 'Not Applicable',
      expiresAt: null,
      recordsAffected: 25
    }
  ];

  const getStatusBadge = (status) => {
    const badges = {
      'Success': { class: 'status-success', icon: 'fas fa-check-circle' },
      'Warning': { class: 'status-warning', icon: 'fas fa-exclamation-triangle' },
      'Failed': { class: 'status-failed', icon: 'fas fa-times-circle' },
      'In Progress': { class: 'status-progress', icon: 'fas fa-spinner fa-spin' }
    };
    return badges[status] || badges.Success;
  };

  const getRollbackStatusBadge = (status) => {
    const badges = {
      'Available': { class: 'rollback-available', icon: 'fas fa-undo' },
      'Expired': { class: 'rollback-expired', icon: 'fas fa-clock' },
      'Not Applicable': { class: 'rollback-na', icon: 'fas fa-minus-circle' },
      'Completed': { class: 'rollback-completed', icon: 'fas fa-check-circle' }
    };
    return badges[status] || badges.Available;
  };

  const renderAuditLogs = () => (
    <div className="audit-logs-content">
      {/* Filters */}
      <div className="logs-filters">
        <div className="filter-group">
          <label>Operation Type:</label>
          <select value={filterType} onChange={(e) => setFilterType(e.target.value)}>
            <option value="all">All Operations</option>
            <option value="import">Import</option>
            <option value="export">Export</option>
            <option value="sync">Sync</option>
          </select>
        </div>
        
        <div className="filter-group">
          <label>Date Range:</label>
          <div className="date-range">
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({...prev, start: e.target.value}))}
            />
            <span>to</span>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({...prev, end: e.target.value}))}
            />
          </div>
        </div>
        
        <button className="btn btn-outline">
          <i className="fas fa-search"></i>
          Filter
        </button>
      </div>

      {/* Logs Table */}
      <div className="logs-table-container">
        <table className="logs-table">
          <thead>
            <tr>
              <th>Timestamp</th>
              <th>Operation</th>
              <th>Data Type</th>
              <th>Target</th>
              <th>User</th>
              <th>Status</th>
              <th>Records</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {mockAuditLogs.map(log => {
              const statusBadge = getStatusBadge(log.status);
              return (
                <tr key={log.id}>
                  <td>{new Date(log.timestamp).toLocaleString()}</td>
                  <td>{log.operation}</td>
                  <td>{log.dataType}</td>
                  <td>{log.targetObject}</td>
                  <td>{log.user}</td>
                  <td>
                    <span className={`status-badge ${statusBadge.class}`}>
                      <i className={statusBadge.icon}></i>
                      {log.status}
                    </span>
                  </td>
                  <td>{log.recordsAffected}</td>
                  <td>
                    <button className="btn btn-sm btn-outline" title="View Details">
                      <i className="fas fa-eye"></i>
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderRollbackOperations = () => (
    <div className="rollback-content">
      <div className="rollback-header">
        <h6>Available Rollback Operations</h6>
        <p>Operations that can be rolled back within the retention period</p>
      </div>

      <div className="rollback-list">
        {mockRollbackOperations.map(rollback => {
          const statusBadge = getRollbackStatusBadge(rollback.status);
          return (
            <div key={rollback.id} className="rollback-item">
              <div className="rollback-info">
                <div className="rollback-header-info">
                  <h7>{rollback.operation}</h7>
                  <span className={`rollback-status ${statusBadge.class}`}>
                    <i className={statusBadge.icon}></i>
                    {rollback.status}
                  </span>
                </div>
                <div className="rollback-details">
                  <span>Executed: {new Date(rollback.timestamp).toLocaleString()}</span>
                  <span>Records Affected: {rollback.recordsAffected}</span>
                  {rollback.expiresAt && (
                    <span>Expires: {new Date(rollback.expiresAt).toLocaleString()}</span>
                  )}
                </div>
              </div>
              
              <div className="rollback-actions">
                {rollback.status === 'Available' && (
                  <button className="btn btn-warning">
                    <i className="fas fa-undo"></i>
                    Rollback
                  </button>
                )}
              </div>
            </div>
          );
        })}
      </div>

      <div className="rollback-policy">
        <h6>Rollback Policy</h6>
        <div className="policy-info">
          <div className="policy-item">
            <i className="fas fa-clock text-blue-500"></i>
            <div>
              <span className="policy-title">Retention Period</span>
              <span className="policy-description">Operations can be rolled back within 24 hours</span>
            </div>
          </div>
          <div className="policy-item">
            <i className="fas fa-shield-alt text-green-500"></i>
            <div>
              <span className="policy-title">Safety Checks</span>
              <span className="policy-description">Rollback operations include data integrity checks</span>
            </div>
          </div>
          <div className="policy-item">
            <i className="fas fa-backup text-purple-500"></i>
            <div>
              <span className="policy-title">Backup Creation</span>
              <span className="policy-description">Automatic backups created before each operation</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderMonitoring = () => (
    <div className="monitoring-content">
      <div className="monitoring-header">
        <h6>Real-time Operation Monitoring</h6>
        <p>Monitor ongoing operations and system health</p>
      </div>

      <div className="monitoring-stats">
        <div className="stat-card">
          <i className="fas fa-tasks text-blue-500"></i>
          <div>
            <span className="stat-number">3</span>
            <span className="stat-label">Active Operations</span>
          </div>
        </div>
        <div className="stat-card">
          <i className="fas fa-check-circle text-green-500"></i>
          <div>
            <span className="stat-number">127</span>
            <span className="stat-label">Completed Today</span>
          </div>
        </div>
        <div className="stat-card">
          <i className="fas fa-exclamation-triangle text-orange-500"></i>
          <div>
            <span className="stat-number">2</span>
            <span className="stat-label">Warnings</span>
          </div>
        </div>
        <div className="stat-card">
          <i className="fas fa-times-circle text-red-500"></i>
          <div>
            <span className="stat-number">1</span>
            <span className="stat-label">Failed</span>
          </div>
        </div>
      </div>

      <div className="active-operations">
        <h6>Active Operations</h6>
        <div className="operations-list">
          <div className="operation-item">
            <div className="operation-icon">
              <i className="fas fa-spinner fa-spin text-blue-500"></i>
            </div>
            <div className="operation-content">
              <span className="operation-title">JIRA Epic Sync</span>
              <span className="operation-description">Syncing 15 epics with JIRA</span>
              <div className="progress-bar">
                <div className="progress-fill" style={{width: '60%'}}></div>
              </div>
            </div>
            <div className="operation-status">
              <span>60% Complete</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="audit-logs">
      {/* Tab Navigation */}
      <div className="audit-tabs">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <i className={tab.icon}></i>
            <span>{tab.name}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {activeTab === 'logs' && renderAuditLogs()}
        {activeTab === 'rollback' && renderRollbackOperations()}
        {activeTab === 'monitoring' && renderMonitoring()}
      </div>
    </div>
  );
};

export default AuditLogs;
