import React, { useState, useEffect } from 'react';
import './ProgramForm.css';

const ProgramForm = ({ 
  program, 
  isEdit, 
  masterBusinessCases,
  onSave, 
  onCancel 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    owner: '',
    status: 'planning',
    priority: 'medium',
    startDate: '',
    endDate: '',
    budget: '',
    linkedMasterBC: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (program && isEdit) {
      setFormData({
        name: program.name || '',
        description: program.description || '',
        owner: program.owner || '',
        status: program.status || 'planning',
        priority: program.priority || 'medium',
        startDate: program.startDate || '',
        endDate: program.endDate || '',
        budget: program.budget || '',
        linkedMasterBC: program.linkedMasterBC || ''
      });
    }
  }, [program, isEdit]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Program name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.owner.trim()) {
      newErrors.owner = 'Owner is required';
    }

    if (formData.budget && isNaN(Number(formData.budget))) {
      newErrors.budget = 'Budget must be a valid number';
    }

    if (formData.startDate && formData.endDate && formData.startDate >= formData.endDate) {
      newErrors.endDate = 'End date must be after start date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const programData = {
        ...formData,
        budget: formData.budget ? Number(formData.budget) : null
      };

      await onSave(programData);
    } catch (error) {
      console.error('Error saving program:', error);
      setErrors({ submit: 'Failed to save program. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount || 0);
  };

  const getAvailableMasterBCs = () => {
    if (isEdit && program?.linkedMasterBC) {
      // If editing and already linked, show current linked MBC plus unlinked ones
      return masterBusinessCases.filter(mbc => 
        mbc.id === program.linkedMasterBC || !mbc.linkedPrograms?.length
      );
    }
    // For new programs, show only unlinked Master BCs
    return masterBusinessCases.filter(mbc => !mbc.linkedPrograms?.length);
  };

  return (
    <div className="program-form">
      <div className="form-header">
        <h3>{isEdit ? 'Edit Program' : 'Create New Program'}</h3>
        <p>
          {isEdit 
            ? 'Update program details and Master BC linking'
            : 'Create a new program and optionally link to a Master Business Case'
          }
        </p>
      </div>

      <form onSubmit={handleSubmit} className="form-content">
        {errors.submit && (
          <div className="error-banner">
            <i className="fas fa-exclamation-triangle"></i>
            {errors.submit}
          </div>
        )}

        <div className="form-grid">
          {/* Basic Information */}
          <div className="form-section">
            <h4>Basic Information</h4>
            
            <div className="form-group">
              <label htmlFor="name">Program Name *</label>
              <input
                id="name"
                type="text"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                className={`form-input ${errors.name ? 'error' : ''}`}
                placeholder="Enter program name"
              />
              {errors.name && <span className="error-text">{errors.name}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="description">Description *</label>
              <textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                className={`form-textarea ${errors.description ? 'error' : ''}`}
                placeholder="Enter program description"
                rows={3}
              />
              {errors.description && <span className="error-text">{errors.description}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="owner">Program Owner *</label>
              <input
                id="owner"
                type="text"
                value={formData.owner}
                onChange={(e) => handleChange('owner', e.target.value)}
                className={`form-input ${errors.owner ? 'error' : ''}`}
                placeholder="Enter owner name or email"
              />
              {errors.owner && <span className="error-text">{errors.owner}</span>}
            </div>
          </div>

          {/* Status and Priority */}
          <div className="form-section">
            <h4>Status & Priority</h4>
            
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="status">Status</label>
                <select
                  id="status"
                  value={formData.status}
                  onChange={(e) => handleChange('status', e.target.value)}
                  className="form-select"
                >
                  <option value="planning">Planning</option>
                  <option value="active">Active</option>
                  <option value="on hold">On Hold</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="priority">Priority</label>
                <select
                  id="priority"
                  value={formData.priority}
                  onChange={(e) => handleChange('priority', e.target.value)}
                  className="form-select"
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </select>
              </div>
            </div>
          </div>

          {/* Timeline and Budget */}
          <div className="form-section">
            <h4>Timeline & Budget</h4>
            
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="startDate">Start Date</label>
                <input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => handleChange('startDate', e.target.value)}
                  className="form-input"
                />
              </div>

              <div className="form-group">
                <label htmlFor="endDate">End Date</label>
                <input
                  id="endDate"
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => handleChange('endDate', e.target.value)}
                  className={`form-input ${errors.endDate ? 'error' : ''}`}
                />
                {errors.endDate && <span className="error-text">{errors.endDate}</span>}
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="budget">Budget (USD)</label>
              <input
                id="budget"
                type="number"
                value={formData.budget}
                onChange={(e) => handleChange('budget', e.target.value)}
                className={`form-input ${errors.budget ? 'error' : ''}`}
                placeholder="Enter budget amount"
                min="0"
                step="1000"
              />
              {errors.budget && <span className="error-text">{errors.budget}</span>}
              {formData.budget && (
                <div className="budget-preview">
                  Budget: {formatCurrency(formData.budget)}
                </div>
              )}
            </div>
          </div>

          {/* Master BC Linking */}
          <div className="form-section">
            <h4>Master Business Case Linking</h4>
            
            <div className="form-group">
              <label htmlFor="linkedMasterBC">Link to Master Business Case</label>
              <select
                id="linkedMasterBC"
                value={formData.linkedMasterBC}
                onChange={(e) => handleChange('linkedMasterBC', e.target.value)}
                className="form-select"
              >
                <option value="">No Master BC Link</option>
                {getAvailableMasterBCs().map(mbc => (
                  <option key={mbc.id} value={mbc.id}>
                    {mbc.name}
                  </option>
                ))}
              </select>
              <div className="help-text">
                Programs can be linked to one Master Business Case. Only unlinked Master BCs are available.
              </div>
              
              {formData.linkedMasterBC && (
                <div className="linked-mbc-preview">
                  <i className="fas fa-link"></i>
                  <span>
                    Will be linked to: {
                      masterBusinessCases.find(mbc => mbc.id === formData.linkedMasterBC)?.name
                    }
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="form-actions">
          <button
            type="button"
            onClick={onCancel}
            className="btn btn-secondary"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <i className="fas fa-spinner fa-spin"></i>
                {isEdit ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              <>
                <i className="fas fa-save"></i>
                {isEdit ? 'Update Program' : 'Create Program'}
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProgramForm;
